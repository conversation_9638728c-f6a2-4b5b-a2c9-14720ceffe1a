using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Utilitaire pour aider à configurer le RaceController
/// Gère les waypoints et évite les erreurs de référence
/// </summary>
public class RaceControllerHelper : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private bool autoSetupOnStart = true;
    [SerializeField] private bool createDefaultWaypoints = true;
    [SerializeField] private bool showDebugLogs = true;

    [Header("Waypoints par défaut")]
    [SerializeField] private int defaultWaypointCount = 4;
    [SerializeField] private float waypointRadius = 50f;
    [SerializeField] private Vector3 centerPosition = Vector3.zero;

    private RaceController raceController;

    private void Start()
    {
        if (autoSetupOnStart)
        {
            SetupRaceController();
        }
    }

    /// <summary>
    /// Configure automatiquement le RaceController
    /// </summary>
    [ContextMenu("Setup Race Controller")]
    public void SetupRaceController()
    {
        LogMessage("🏁 === CONFIGURATION DU RACE CONTROLLER ===");

        // Trouver ou créer le RaceController
        raceController = FindObjectOfType<RaceController>();
        if (raceController == null)
        {
            GameObject raceControllerObject = new GameObject("Race Controller");
            raceController = raceControllerObject.AddComponent<RaceController>();
            LogMessage("✅ RaceController créé automatiquement");
        }
        else
        {
            LogMessage("✅ RaceController existant trouvé");
        }

        // Vérifier et configurer les waypoints
        SetupWaypoints();

        // Vérifier la configuration
        VerifyConfiguration();

        LogMessage("=== CONFIGURATION TERMINÉE ===");
    }

    /// <summary>
    /// Configure les waypoints du RaceController
    /// </summary>
    private void SetupWaypoints()
    {
        var waypoints = GetWaypoints();
        
        if (waypoints == null || waypoints.Count == 0)
        {
            if (createDefaultWaypoints)
            {
                LogMessage("⚠️ Aucun waypoint trouvé, création de waypoints par défaut...");
                CreateDefaultWaypoints();
            }
            else
            {
                LogMessage("⚠️ Aucun waypoint configuré - le système de course ne fonctionnera pas correctement");
            }
        }
        else
        {
            LogMessage($"✅ {waypoints.Count} waypoints trouvés");
            
            // Vérifier que tous les waypoints sont valides
            int invalidCount = 0;
            for (int i = 0; i < waypoints.Count; i++)
            {
                if (waypoints[i] == null)
                {
                    invalidCount++;
                }
            }
            
            if (invalidCount > 0)
            {
                LogMessage($"⚠️ {invalidCount} waypoints sont null - cela peut causer des erreurs");
            }
        }
    }

    /// <summary>
    /// Crée des waypoints par défaut en cercle
    /// </summary>
    private void CreateDefaultWaypoints()
    {
        List<Transform> newWaypoints = new List<Transform>();
        
        GameObject waypointsContainer = new GameObject("Race Waypoints");
        waypointsContainer.transform.SetParent(transform);

        for (int i = 0; i < defaultWaypointCount; i++)
        {
            float angle = (360f / defaultWaypointCount) * i * Mathf.Deg2Rad;
            Vector3 position = centerPosition + new Vector3(
                Mathf.Cos(angle) * waypointRadius,
                0,
                Mathf.Sin(angle) * waypointRadius
            );

            GameObject waypoint = new GameObject($"Waypoint_{i + 1}");
            waypoint.transform.SetParent(waypointsContainer.transform);
            waypoint.transform.position = position;
            
            // Ajouter un marqueur visuel
            GameObject marker = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            marker.transform.SetParent(waypoint.transform);
            marker.transform.localPosition = Vector3.zero;
            marker.transform.localScale = new Vector3(2, 5, 2);
            marker.GetComponent<Renderer>().material.color = Color.yellow;
            
            // Supprimer le collider pour éviter les interférences
            Destroy(marker.GetComponent<Collider>());

            newWaypoints.Add(waypoint.transform);
        }

        // Assigner les waypoints au RaceController
        SetWaypoints(newWaypoints);
        
        LogMessage($"✅ {defaultWaypointCount} waypoints par défaut créés");
    }

    /// <summary>
    /// Obtient la liste des waypoints du RaceController
    /// </summary>
    private List<Transform> GetWaypoints()
    {
        if (raceController == null) return null;
        
        return raceController.GetWaypoints();
    }

    /// <summary>
    /// Définit les waypoints du RaceController
    /// </summary>
    private void SetWaypoints(List<Transform> waypoints)
    {
        if (raceController == null) return;

        // Utiliser la réflexion pour accéder au champ privé waypoints
        var waypointsField = typeof(RaceController).GetField("waypoints", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (waypointsField != null)
        {
            waypointsField.SetValue(raceController, waypoints);
        }
        else
        {
            LogMessage("❌ Impossible d'accéder au champ waypoints du RaceController");
        }
    }

    /// <summary>
    /// Vérifie la configuration du RaceController
    /// </summary>
    private void VerifyConfiguration()
    {
        LogMessage("🔍 === VÉRIFICATION DE LA CONFIGURATION ===");

        if (raceController == null)
        {
            LogMessage("❌ RaceController non trouvé");
            return;
        }

        var waypoints = GetWaypoints();
        LogMessage($"Waypoints: {(waypoints != null ? waypoints.Count.ToString() : "0")}");

        // Vérifier les autres composants nécessaires
        var raceTimer = FindObjectOfType<RaceTimer>();
        LogMessage($"RaceTimer: {(raceTimer != null ? "✅" : "⚠️")}");

        var uiRanking = FindObjectOfType<UIRanking>();
        LogMessage($"UIRanking: {(uiRanking != null ? "✅" : "⚠️")}");

        var meshtasticGPS = FindObjectOfType<MeshtasticGPSTracker>();
        LogMessage($"MeshtasticGPSTracker: {(meshtasticGPS != null ? "✅" : "⚠️")}");

        // Vérifier les bateaux
        var boats = raceController.GetBoats();
        LogMessage($"Bateaux configurés: {boats.Count}");

        LogMessage("=== FIN DE LA VÉRIFICATION ===");
    }

    /// <summary>
    /// Nettoie les waypoints existants
    /// </summary>
    [ContextMenu("Clear Waypoints")]
    public void ClearWaypoints()
    {
        if (raceController == null)
        {
            raceController = FindObjectOfType<RaceController>();
        }

        if (raceController != null)
        {
            SetWaypoints(new List<Transform>());
            LogMessage("🗑️ Waypoints effacés");
        }

        // Supprimer les objets waypoints dans la scène
        var waypointsContainer = GameObject.Find("Race Waypoints");
        if (waypointsContainer != null)
        {
            DestroyImmediate(waypointsContainer);
            LogMessage("🗑️ Objets waypoints supprimés de la scène");
        }
    }

    /// <summary>
    /// Recrée les waypoints par défaut
    /// </summary>
    [ContextMenu("Recreate Default Waypoints")]
    public void RecreateDefaultWaypoints()
    {
        ClearWaypoints();
        CreateDefaultWaypoints();
        LogMessage("🔄 Waypoints par défaut recréés");
    }

    /// <summary>
    /// Affiche les informations de debug du RaceController
    /// </summary>
    [ContextMenu("Show Race Controller Info")]
    public void ShowRaceControllerInfo()
    {
        if (raceController == null)
        {
            raceController = FindObjectOfType<RaceController>();
        }

        if (raceController == null)
        {
            LogMessage("❌ RaceController non trouvé");
            return;
        }

        LogMessage("📊 === INFORMATIONS DU RACE CONTROLLER ===");
        LogMessage($"Course démarrée: {raceController.IsRaceStarted()}");
        LogMessage($"Session actuelle: {raceController.GetCurrentSession()}");
        LogMessage($"Tours totaux: {raceController.GetTotalLaps()}");
        LogMessage($"Nombre de coureurs: {raceController.GetRacers().Count}");
        LogMessage($"Nombre de bateaux: {raceController.GetBoats().Count}");
        
        var waypoints = GetWaypoints();
        if (waypoints != null)
        {
            LogMessage($"Waypoints: {waypoints.Count}");
            for (int i = 0; i < waypoints.Count; i++)
            {
                if (waypoints[i] != null)
                {
                    LogMessage($"  - Waypoint {i + 1}: {waypoints[i].name} à {waypoints[i].position}");
                }
                else
                {
                    LogMessage($"  - Waypoint {i + 1}: NULL ⚠️");
                }
            }
        }
        else
        {
            LogMessage("Waypoints: Aucun");
        }
        
        LogMessage("=== FIN DES INFORMATIONS ===");
    }

    /// <summary>
    /// Test de fonctionnement du RaceController
    /// </summary>
    [ContextMenu("Test Race Controller")]
    public void TestRaceController()
    {
        LogMessage("🧪 === TEST DU RACE CONTROLLER ===");

        if (raceController == null)
        {
            LogMessage("❌ RaceController non trouvé, création automatique...");
            SetupRaceController();
        }

        // Tester l'accès aux waypoints
        var waypoints = GetWaypoints();
        if (waypoints == null || waypoints.Count == 0)
        {
            LogMessage("⚠️ Aucun waypoint, création de waypoints de test...");
            CreateDefaultWaypoints();
        }

        // Tester les méthodes publiques
        try
        {
            bool isStarted = raceController.IsRaceStarted();
            int session = raceController.GetCurrentSession();
            int laps = raceController.GetTotalLaps();
            
            LogMessage($"✅ Méthodes publiques fonctionnelles");
            LogMessage($"   - Course démarrée: {isStarted}");
            LogMessage($"   - Session: {session}");
            LogMessage($"   - Tours: {laps}");
        }
        catch (System.Exception e)
        {
            LogMessage($"❌ Erreur lors du test: {e.Message}");
        }

        LogMessage("=== TEST TERMINÉ ===");
    }

    /// <summary>
    /// Affiche un message de log si les logs de debug sont activés
    /// </summary>
    private void LogMessage(string message)
    {
        if (showDebugLogs)
        {
            Debug.Log($"[RaceControllerHelper] {message}");
        }
    }

    private void OnDrawGizmosSelected()
    {
        // Visualiser la zone des waypoints par défaut
        Gizmos.color = Color.cyan;
        Gizmos.DrawWireSphere(centerPosition, waypointRadius);
        
        // Visualiser les positions des waypoints par défaut
        Gizmos.color = Color.yellow;
        for (int i = 0; i < defaultWaypointCount; i++)
        {
            float angle = (360f / defaultWaypointCount) * i * Mathf.Deg2Rad;
            Vector3 position = centerPosition + new Vector3(
                Mathf.Cos(angle) * waypointRadius,
                0,
                Mathf.Sin(angle) * waypointRadius
            );
            Gizmos.DrawWireSphere(position, 5f);
        }
    }
}
