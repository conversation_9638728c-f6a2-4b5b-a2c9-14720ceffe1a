using UnityEngine;
using Mapbox.Unity.Map;
using Mapbox.Utils;
using System.Collections.Generic;

/// <summary>
/// Classe principale pour la gestion des positions GPS des bateaux
/// </summary>
public class UnifiedBoatGPS : MonoBehaviour
{
    [Header("Map Settings")]
    [SerializeField] private AbstractMap map;
    [SerializeField] private float heightOffset = 1f;

    [Header("Path Settings")]
    [SerializeField] private bool showPaths = true;
    [SerializeField] private Color pathColor = Color.red;
    [SerializeField] private float lineWidth = 0.5f;
    [SerializeField] private int maxPathPoints = 100;

    [Header("Boat Movement")]
    [SerializeField] private float pathFollowDelay = 15f;        // Délai du GPS (adapté pour données toutes les 8-12s)
    [SerializeField] private float maxSpeed = 10f;               // Vitesse max
    [SerializeField] private float turningSpeed = 30f;          // Vitesse de rotation de base
    [SerializeField] private float inertiaFactor = 3f;          // Facteur d'inertie pour mouvement fluide (augmenté)

    [Header("Height Control (Y Position)")]
    [SerializeField] private float waterLevel = 0f;             // Niveau de l'eau (Y de référence)
    [SerializeField] private float boatHeightOffset = 0.5f;     // Hauteur du bateau au-dessus de l'eau
    [SerializeField] private bool enableRuntimeHeightControl = true; // Permettre l'ajustement en temps réel
    [SerializeField] private KeyCode increaseHeightKey = KeyCode.PageUp;   // Touche pour monter
    [SerializeField] private KeyCode decreaseHeightKey = KeyCode.PageDown; // Touche pour descendre
    [SerializeField] private float heightAdjustmentStep = 0.1f;  // Pas d'ajustement par pression de touche

    [Header("GPS Settings")]
    [SerializeField] private float gpsAccuracy = 2.0f;           // Précision du GPS en mètres (1.5-2m)
    [SerializeField] private float positionSmoothingFactor = 0.3f; // Facteur de lissage pour les petites variations GPS

    [Header("Rotation Settings")]
    [SerializeField] private float minHeadingSmoothTime = 0.8f;  // Temps minimum de lissage du cap (bateau lent)
    [SerializeField] private float maxHeadingSmoothTime = 0.2f;  // Temps maximum de lissage du cap (bateau rapide)

    [Header("Marine Effects")]
    [SerializeField] private float bobHeight = 0.02f;
    [SerializeField] private float bobSpeed = 1f;
    [SerializeField] private float rollAmount = 5f;

    [Header("Debug")]
    [SerializeField] private bool showSpeedLogs = true;
    [SerializeField] private bool showPositionLogs = false;
    [SerializeField] private float maxValidTeleportDistance = 100f; // Distance max en mètres pour une téléportation sans transition
    [SerializeField] private float teleportTransitionDuration = 1.5f; // Durée de la transition en secondes

    // Composants
    private GPSPositionConverter gpsConverter;
    private BoatMovementController movementController;
    private MarineEffectsController effectsController;

    // Variables internes
    private Vector2d? queuedInitialPosition = null;
    private Vector2d currentGPSPosition;
    private Vector2d lastValidGPSPosition;
    private float lastGPSTimestamp = 0f; // Timestamp de la dernière réception GPS
    private LineRenderer pathRenderer;
    private readonly List<Vector3> pathPoints = new();
    private static Transform pathsContainer; // Conteneur unique pour tous les chemins
    private bool _isReadyForTeleport = false; // Indique si la carte est initialisée et prête pour la téléportation
    private bool _teleportQueued = false; // Indique si une téléportation manuelle est en attente
    public bool _ignoreGPSUpdatesTemporarily = false; // Nouveau: Ignorer les mises à jour GPS après téléportation manuelle
    [SerializeField] private float _gpsUpdateIgnoreDuration = 1.0f; // Durée pendant laquelle ignorer les mises à jour GPS

    // Événements
    public event System.Action OnMapReady;

    private void Start()
    {
        InitializeComponents();
        if (showPaths) SetupPathRenderer();
    }

    private void InitializeComponents()
    {
        // Initialiser la carte si nécessaire
        if (!map)
        {
            map = FindFirstObjectByType<AbstractMap>();
            Debug.Log($"[UnifiedBoatGPS] Recherche de carte Mapbox : {(map != null ? "trouvée" : "non trouvée")}");
        }

        if (map == null)
        {
            Debug.LogError("[UnifiedBoatGPS] Pas de carte Mapbox trouvée!");
            return;
        }

        // Initialiser les composants
        gpsConverter = new GPSPositionConverter(map, heightOffset);
        movementController = new BoatMovementController(pathFollowDelay, maxSpeed, turningSpeed, inertiaFactor);
        effectsController = new MarineEffectsController(bobHeight, bobSpeed, rollAmount, heightOffset);

        // Configurer les paramètres
        movementController.MaxValidTeleportDistance = maxValidTeleportDistance;
        movementController.TeleportTransitionDuration = teleportTransitionDuration;
        movementController.GPSAccuracy = gpsAccuracy;
        movementController.PositionSmoothingFactor = positionSmoothingFactor;
        movementController.MinHeadingSmoothTime = minHeadingSmoothTime;
        movementController.MaxHeadingSmoothTime = maxHeadingSmoothTime;

        // S'abonner à l'événement d'initialisation de la carte
        map.OnInitialized += OnMapInitialized;

        // Si la carte est déjà initialisée, déclencher manuellement
        if (map.CenterLatitudeLongitude != default)
        {
            OnMapInitialized();
        }
    }

    private void OnMapInitialized()
    {
        OnMapReady?.Invoke();
        _isReadyForTeleport = true; // La carte est maintenant prête pour la téléportation

        // Traiter la téléportation manuelle en attente et la position initiale
        ProcessQueuedTeleportsForThisBoat();
    }

    /// <summary>
    /// Traite les téléportations et positions initiales en file d'attente pour cette instance de bateau.
    /// </summary>
    private void ProcessQueuedTeleportsForThisBoat()
    {
        if (_teleportQueued && _isReadyForTeleport)
        {
            ManualTeleportToLatestGPS();
            if (!_teleportQueued)
            {
                // _teleportQueued a été remis à false par ManualTeleportToLatestGPS
            }
            else
            {
                _teleportQueued = false;
            }
        }

        if (queuedInitialPosition.HasValue && _isReadyForTeleport)
        {
            SetInitialPosition(queuedInitialPosition.Value);
            queuedInitialPosition = null;
        }
    }

    /// <summary>
    /// Met en file d'attente une position initiale à définir lorsque la carte sera initialisée
    /// </summary>
    public void QueueInitialPosition(Vector2d initialGPSPosition)
    {
        queuedInitialPosition = initialGPSPosition;
        Debug.Log($"[UnifiedBoatGPS] Position initiale ({initialGPSPosition}) mise en file d'attente pour {gameObject.name}.");

        // Si la carte est déjà initialisée, définir la position immédiatement
        if (gpsConverter != null && gpsConverter.IsMapInitialized)
        {
            SetInitialPosition(initialGPSPosition);
            queuedInitialPosition = null;
        }
    }

    /// <summary>
    /// Définit la position initiale du bateau
    /// </summary>
    public void SetInitialPosition(Vector2d initialGPSPosition)
    {
        if (gpsConverter == null || !gpsConverter.IsMapInitialized || !_isReadyForTeleport)
        {
            QueueInitialPosition(initialGPSPosition);
            return;
        }

        if (!GPSPositionConverter.IsValidGPSCoordinate(initialGPSPosition))
        {
            return;
        }

        currentGPSPosition = initialGPSPosition;
        lastValidGPSPosition = initialGPSPosition;

        Vector3 initialUnityPosition = gpsConverter.GPSToUnityPosition(initialGPSPosition);
        movementController.TeleportImmediately(transform, initialUnityPosition, 0f);
    }

    /// <summary>
    /// Met à jour la position réelle du bateau à partir des coordonnées GPS
    /// </summary>
    public void UpdateRealPosition(Vector2d newPosition, float newHeading, float timestamp)
    {
        if (_ignoreGPSUpdatesTemporarily)
        {
            return;
        }

        if (gpsConverter == null || !gpsConverter.IsMapInitialized || map == null || map.CenterLatitudeLongitude == default)
        {
            return;
        }

        if (!GPSPositionConverter.IsValidGPSCoordinate(newPosition))
        {
            return;
        }

        Vector3 newUnityPosition = gpsConverter.GPSToUnityPosition(newPosition);

        // Appliquer le contrôle de hauteur Y
        newUnityPosition.y = waterLevel + boatHeightOffset;

        float speed = CalculateApproximateSpeed(newPosition, timestamp);
        movementController.AddNewPosition(newUnityPosition, newHeading, speed, timestamp);

        if (showPaths) AddPathPoint(newUnityPosition);

        currentGPSPosition = newPosition;
        lastValidGPSPosition = newPosition;
        lastGPSTimestamp = timestamp; // Stocker le timestamp pour le prochain calcul
    }

    /// <summary>
    /// Calcule la vitesse approximative basée sur la distance entre la position actuelle et la nouvelle position
    /// </summary>
    private float CalculateApproximateSpeed(Vector2d newPosition, float newTimestamp)
    {
        // Si c'est la première position, retourner 0
        if (currentGPSPosition == default || lastGPSTimestamp == 0f)
        {
            if (showSpeedLogs)
                Debug.Log($"[UnifiedBoatGPS] {gameObject.name}: Première position GPS, vitesse = 0");
            return 0f;
        }

        // Calculer la distance en mètres entre les deux positions GPS
        float distance = GPSPositionConverter.CalculateDistance(currentGPSPosition, newPosition);

        // Si la distance est inférieure à la précision du GPS, considérer que le bateau est immobile
        if (distance < gpsAccuracy)
        {
            if (showSpeedLogs)
                Debug.Log($"[UnifiedBoatGPS] {gameObject.name}: Distance trop faible ({distance:F2}m < {gpsAccuracy}m), vitesse = 0");
            return 0f;
        }

        // Calculer le temps écoulé depuis la dernière mise à jour GPS (en secondes)
        float timeElapsed = newTimestamp - lastGPSTimestamp;

        // Vérifier que le temps écoulé est valide
        if (timeElapsed <= 0f)
        {
            if (showSpeedLogs)
                Debug.LogWarning($"[UnifiedBoatGPS] {gameObject.name}: Temps écoulé invalide ({timeElapsed}s), utilisation de Time.deltaTime");
            timeElapsed = Time.deltaTime > 0 ? Time.deltaTime : 0.1f;
        }

        // Calculer la vitesse en m/s
        float speedMS = distance / timeElapsed;

        // Limiter la vitesse maximale
        float finalSpeed = Mathf.Min(speedMS, maxSpeed);

        if (showSpeedLogs)
        {
            Debug.Log($"[UnifiedBoatGPS] {gameObject.name}: Distance: {distance:F2}m, Temps: {timeElapsed:F2}s, " +
                     $"Vitesse calculée: {speedMS:F2} m/s ({speedMS * 1.944f:F1} nœuds), " +
                     $"Vitesse finale: {finalSpeed:F2} m/s ({finalSpeed * 1.944f:F1} nœuds)");
        }

        return finalSpeed;
    }

    private void Update()
    {
        if (gpsConverter == null || !gpsConverter.IsMapInitialized) return;

        // Contrôle de hauteur en temps réel
        if (enableRuntimeHeightControl && Application.isPlaying)
        {
            HandleHeightControl();
        }

        // Mettre à jour la cible et le mouvement
        movementController.UpdateTarget(Time.time);
        movementController.UpdateMovement(transform, Time.deltaTime);

        // Appliquer le contrôle de hauteur Y à la position finale
        Vector3 currentPos = transform.position;
        currentPos.y = waterLevel + boatHeightOffset;
        transform.position = currentPos;

        // Appliquer les effets marins
        effectsController.ApplyEffects(transform, movementController.CurrentSpeed, maxSpeed);
    }

    /// <summary>
    /// Gère le contrôle de hauteur en temps réel
    /// </summary>
    private void HandleHeightControl()
    {
        bool heightChanged = false;

        if (Input.GetKeyDown(increaseHeightKey))
        {
            boatHeightOffset += heightAdjustmentStep;
            heightChanged = true;
            Debug.Log($"[UnifiedBoatGPS] {gameObject.name}: Hauteur augmentée à {boatHeightOffset:F2}m (Y = {waterLevel + boatHeightOffset:F2})");
        }
        else if (Input.GetKeyDown(decreaseHeightKey))
        {
            boatHeightOffset -= heightAdjustmentStep;
            heightChanged = true;
            Debug.Log($"[UnifiedBoatGPS] {gameObject.name}: Hauteur diminuée à {boatHeightOffset:F2}m (Y = {waterLevel + boatHeightOffset:F2})");
        }

        // Afficher les instructions la première fois
        if (heightChanged && Time.time < 10f)
        {
            Debug.Log($"[UnifiedBoatGPS] Contrôles de hauteur: {increaseHeightKey} = Monter, {decreaseHeightKey} = Descendre");
        }
    }

    /// <summary>
    /// Retourne la vitesse actuelle en nœuds
    /// </summary>
    public float GetCurrentSpeed()
    {
        return movementController.CurrentSpeed * 1.944f; // Conversion en nœuds
    }

    /// <summary>
    /// Retourne la position GPS actuelle
    /// </summary>
    public Vector2d GetCurrentPosition()
    {
        return currentGPSPosition;
    }

    /// <summary>
    /// Déclenche manuellement une téléportation vers la dernière position GPS valide
    /// </summary>
    public void ManualTeleportToLatestGPS()
    {
        ManualTeleportToLatestGPS(_gpsUpdateIgnoreDuration * 10f);
    }

    /// <summary>
    /// Déclenche manuellement une téléportation avec une durée d'ignorance GPS personnalisée
    /// </summary>
    /// <param name="customIgnoreDuration">Durée personnalisée d'ignorance des mises à jour GPS</param>
    public void ManualTeleportToLatestGPS(float customIgnoreDuration)
    {
        if (!_isReadyForTeleport)
        {
            _teleportQueued = true;
            return;
        }

        Vector2d teleportTargetGPS = lastValidGPSPosition;

        if (teleportTargetGPS == default)
        {
            if (queuedInitialPosition.HasValue)
            {
                teleportTargetGPS = queuedInitialPosition.Value;
            }
            else
            {
                return;
            }
        }

        Vector3 targetPosition = gpsConverter.GPSToUnityPosition(teleportTargetGPS);

        movementController.TeleportImmediately(transform, targetPosition, transform.eulerAngles.y);
        currentGPSPosition = teleportTargetGPS;
        lastValidGPSPosition = teleportTargetGPS;
        _ignoreGPSUpdatesTemporarily = true;
        CancelInvoke(nameof(ResetGPSUpdateIgnoreFlag));
        Invoke(nameof(ResetGPSUpdateIgnoreFlag), customIgnoreDuration);
        _teleportQueued = false;
    }

    private void SetupPathRenderer()
    {
        if (!showPaths) return;

        // Créer ou obtenir le conteneur de chemins
        if (pathsContainer == null)
        {
            GameObject container = GameObject.Find("BoatPaths");
            if (container == null)
            {
                container = new GameObject("BoatPaths");
                // Placer le conteneur à la racine de la scène
                container.transform.SetParent(null);
            }
            pathsContainer = container.transform;
        }

        // Créer l'objet de chemin avec un nom unique
        GameObject pathObj = new($"BoatPath_{gameObject.name}");
        // Placer le chemin dans le conteneur dédié
        pathObj.transform.SetParent(pathsContainer);
        pathRenderer = pathObj.AddComponent<LineRenderer>();

        pathRenderer.startWidth = lineWidth;
        pathRenderer.endWidth = lineWidth;
        pathRenderer.material = new Material(Shader.Find("Sprites/Default"));
        pathRenderer.startColor = pathColor;
        pathRenderer.endColor = pathColor;
        pathRenderer.positionCount = 0;
        pathRenderer.useWorldSpace = true;
    }

    private void AddPathPoint(Vector3 newPoint)
    {
        if (!showPaths || pathRenderer == null) return;

        pathPoints.Add(newPoint);
        if (pathPoints.Count > maxPathPoints)
        {
            pathPoints.RemoveAt(0);
        }

        pathRenderer.positionCount = pathPoints.Count;
        pathRenderer.SetPositions(pathPoints.ToArray());
    }

    private void OnDestroy()
    {
        if (map != null)
        {
            map.OnInitialized -= OnMapInitialized;
        }

        // Nettoyer le chemin si nécessaire
        if (pathRenderer != null)
        {
            Destroy(pathRenderer.gameObject);
        }

        // Nettoyer le conteneur de chemins s'il est vide
        if (pathsContainer != null && pathsContainer.childCount == 0)
        {
            Destroy(pathsContainer.gameObject);
            pathsContainer = null;
        }
    }

    private void ResetGPSUpdateIgnoreFlag()
    {
        _ignoreGPSUpdatesTemporarily = false;
    }

    /// <summary>
    /// Active ou désactive les logs de vitesse pour debug
    /// </summary>
    [ContextMenu("Toggle Speed Logs")]
    public void ToggleSpeedLogs()
    {
        showSpeedLogs = !showSpeedLogs;
        Debug.Log($"[UnifiedBoatGPS] {gameObject.name}: Logs de vitesse {(showSpeedLogs ? "activés" : "désactivés")}");
    }

    /// <summary>
    /// Affiche les informations de vitesse actuelles
    /// </summary>
    [ContextMenu("Show Current Speed Info")]
    public void ShowCurrentSpeedInfo()
    {
        float currentSpeedKnots = GetCurrentSpeed();
        float currentSpeedMS = movementController.CurrentSpeed;
        Debug.Log($"[UnifiedBoatGPS] {gameObject.name}: Vitesse actuelle = {currentSpeedMS:F2} m/s ({currentSpeedKnots:F1} nœuds)");
        Debug.Log($"[UnifiedBoatGPS] {gameObject.name}: Position GPS = {currentGPSPosition}");
        Debug.Log($"[UnifiedBoatGPS] {gameObject.name}: Dernier timestamp = {lastGPSTimestamp:F2}s");
        Debug.Log($"[UnifiedBoatGPS] {gameObject.name}: Délai de suivi = {pathFollowDelay:F1}s");
        Debug.Log($"[UnifiedBoatGPS] {gameObject.name}: Points GPS en buffer = {(movementController != null ? "disponible" : "non disponible")}");
        Debug.Log($"[UnifiedBoatGPS] {gameObject.name}: Hauteur Y = {waterLevel + boatHeightOffset:F2}m (Eau: {waterLevel:F2}m + Offset: {boatHeightOffset:F2}m)");
    }

    /// <summary>
    /// Augmente la hauteur du bateau
    /// </summary>
    [ContextMenu("Increase Height")]
    public void IncreaseHeight()
    {
        boatHeightOffset += heightAdjustmentStep;
        Debug.Log($"[UnifiedBoatGPS] {gameObject.name}: Hauteur augmentée à {boatHeightOffset:F2}m");
    }

    /// <summary>
    /// Diminue la hauteur du bateau
    /// </summary>
    [ContextMenu("Decrease Height")]
    public void DecreaseHeight()
    {
        boatHeightOffset -= heightAdjustmentStep;
        Debug.Log($"[UnifiedBoatGPS] {gameObject.name}: Hauteur diminuée à {boatHeightOffset:F2}m");
    }

    /// <summary>
    /// Remet la hauteur à zéro (niveau de l'eau)
    /// </summary>
    [ContextMenu("Reset Height to Water Level")]
    public void ResetHeightToWaterLevel()
    {
        boatHeightOffset = 0f;
        Debug.Log($"[UnifiedBoatGPS] {gameObject.name}: Hauteur remise au niveau de l'eau");
    }

    /// <summary>
    /// Définit une hauteur personnalisée
    /// </summary>
    public void SetCustomHeight(float height)
    {
        boatHeightOffset = height;
        Debug.Log($"[UnifiedBoatGPS] {gameObject.name}: Hauteur définie à {boatHeightOffset:F2}m");
    }

    /// <summary>
    /// Définit le niveau de l'eau
    /// </summary>
    public void SetWaterLevel(float level)
    {
        waterLevel = level;
        Debug.Log($"[UnifiedBoatGPS] {gameObject.name}: Niveau de l'eau défini à {waterLevel:F2}m");
    }

    /// <summary>
    /// Obtient la hauteur Y actuelle du bateau
    /// </summary>
    public float GetCurrentYPosition()
    {
        return waterLevel + boatHeightOffset;
    }

    /// <summary>
    /// Ajuste les paramètres pour des données GPS lentes (8-12 secondes)
    /// </summary>
    [ContextMenu("Configure for Slow GPS (8-12s)")]
    public void ConfigureForSlowGPS()
    {
        pathFollowDelay = 15f;
        inertiaFactor = 3f;

        if (movementController != null)
        {
            movementController.PathFollowDelay = pathFollowDelay;
            movementController.InertiaFactor = inertiaFactor;
        }

        Debug.Log($"[UnifiedBoatGPS] {gameObject.name}: Configuré pour GPS lent - Délai: {pathFollowDelay}s, Inertie: {inertiaFactor}");
    }

    /// <summary>
    /// Ajuste les paramètres pour des données GPS rapides (1-3 secondes)
    /// </summary>
    [ContextMenu("Configure for Fast GPS (1-3s)")]
    public void ConfigureForFastGPS()
    {
        pathFollowDelay = 5f;
        inertiaFactor = 1f;

        if (movementController != null)
        {
            movementController.PathFollowDelay = pathFollowDelay;
            movementController.InertiaFactor = inertiaFactor;
        }

        Debug.Log($"[UnifiedBoatGPS] {gameObject.name}: Configuré pour GPS rapide - Délai: {pathFollowDelay}s, Inertie: {inertiaFactor}");
    }
}
