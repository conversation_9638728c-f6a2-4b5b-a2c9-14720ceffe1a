using UnityEngine;

public class RegattaHelicopterCamera : MonoBeh<PERSON><PERSON>
{
    [<PERSON><PERSON>("Suivi")]
    [SerializeField] private float height = 40f;
    [SerializeField] private float distance = 40f;
    [SerializeField] private float damping = 1f;
    [SerializeField] private float lookAheadDistance = 20f;

    [Header("Contr�le")]
    [SerializeField] private float heightOffset = 5f;
    [SerializeField] private float lateralOffset = 20f;

    private Transform target;
    private Vector3 velocity;

    public void SetTarget(Transform newTarget)
    {
        target = newTarget;
    }

    private void LateUpdate()
    {
        if (target == null) return;

        // Position cible avec offset lat�ral
        Vector3 targetPosition = target.position +
                               (Vector3.up * height) +
                               (-target.forward * distance) +
                               (target.right * lateralOffset);

        // Point de vis�e devant le bateau
        Vector3 lookAtPoint = target.position + (target.forward * lookAheadDistance) + (Vector3.up * heightOffset);

        // Mouvement fluide
        transform.position = Vector3.SmoothDamp(transform.position, targetPosition, ref velocity, damping);
        transform.LookAt(lookAtPoint);
    }
}