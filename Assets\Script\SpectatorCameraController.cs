using UnityEngine;

public class SpectatorCameraController : MonoBehaviour
{
    [Header("Cible")]
    public Transform targetBoat;
    public float heightOffset = 10f;
    public float distanceFromTarget = 15f;
    public float lookAheadDistance = 5f;

    [Header("Mouvement")]
    public float rotationSpeed = 100f;
    public float moveSpeed = 10f;
    public float smoothTime = 0.5f;

    [Header("Contraintes")]
    public float minHeight = 5f;
    public float maxHeight = 30f;
    public float minDistance = 5f;
    public float maxDistance = 50f;

    public enum CameraMode { Follow, Overhead, Side, Free }

    [Header("Modes")]
    public CameraMode currentMode = CameraMode.Follow;

    [Header("References")]
    [SerializeField] private RaceController raceController;

    // Variables priv�es pour le smoothing
    private Vector3 currentVelocity;
    private float currentRotationY;
    private float targetRotationY;

    private void Start()
    {
        if (raceController == null)
            raceController = FindObjectOfType<RaceController>();

        currentRotationY = transform.eulerAngles.y;
        targetRotationY = currentRotationY;

        // Trouver une cible initiale si possible parmi les bateaux actifs
        if (targetBoat == null && raceController != null)
        {
            var racers = raceController.GetRacers();
            if (racers != null && racers.Count > 0)
                targetBoat = racers[0].boat.transform;
        }

        Debug.Log("Camera Controls (Numpad):\n" +
                 "1: Follow\n" +
                 "2: Overhead\n" +
                 "3: Side\n" +
                 "4: Free");
    }

    private void LateUpdate()
    {
        if (targetBoat == null) FindNewTarget();
        if (targetBoat == null) return;

        HandleInput();
        UpdateCameraPosition();
    }

    private void FindNewTarget()
    {
        if (raceController != null)
        {
            // Chercher parmi les bateaux actifs dans la course
            var racers = raceController.GetRacers();
            if (racers != null && racers.Count > 0)
                targetBoat = racers[0].boat.transform;
        }
    }

    private void HandleInput()
    {
        // Changement de mode avec les touches num�riques
        if (Input.GetKeyDown(KeyCode.Keypad1)) currentMode = CameraMode.Follow;
        if (Input.GetKeyDown(KeyCode.Keypad2)) currentMode = CameraMode.Overhead;
        if (Input.GetKeyDown(KeyCode.Keypad3)) currentMode = CameraMode.Side;
        if (Input.GetKeyDown(KeyCode.Keypad4)) currentMode = CameraMode.Free;

        // Contr�les en mode libre
        if (currentMode == CameraMode.Free)
        {
            // Rotation avec le clic droit
            if (Input.GetMouseButton(1))
            {
                float mouseX = Input.GetAxis("Mouse X");
                targetRotationY += mouseX * rotationSpeed * Time.deltaTime;
            }

            // Zoom avec la molette
            float scroll = Input.GetAxis("Mouse ScrollWheel");
            distanceFromTarget = Mathf.Clamp(distanceFromTarget - scroll * 10f, minDistance, maxDistance);

            // Hauteur avec Q/E
            if (Input.GetKey(KeyCode.Q)) heightOffset -= moveSpeed * Time.deltaTime;
            if (Input.GetKey(KeyCode.E)) heightOffset += moveSpeed * Time.deltaTime;
            heightOffset = Mathf.Clamp(heightOffset, minHeight, maxHeight);
        }
    }

    private void UpdateCameraPosition()
    {
        Vector3 targetPosition = targetBoat.position;
        Vector3 desiredPosition = Vector3.zero;
        Quaternion desiredRotation = Quaternion.identity;

        switch (currentMode)
        {
            case CameraMode.Follow:
                // Position derri�re le bateau avec un peu de hauteur
                desiredPosition = targetPosition - targetBoat.forward * distanceFromTarget + Vector3.up * heightOffset;
                desiredRotation = Quaternion.LookRotation(targetBoat.forward + targetBoat.forward * lookAheadDistance, Vector3.up);
                break;

            case CameraMode.Overhead:
                // Vue de dessus
                desiredPosition = targetPosition + Vector3.up * maxHeight;
                desiredRotation = Quaternion.Euler(90f, 0f, 0f);
                break;

            case CameraMode.Side:
                // Vue de c�t�
                desiredPosition = targetPosition + targetBoat.right * distanceFromTarget + Vector3.up * heightOffset;
                desiredRotation = Quaternion.LookRotation(-targetBoat.right, Vector3.up);
                break;

            case CameraMode.Free:
                // Position libre autour du bateau
                float currentAngle = targetRotationY * Mathf.Deg2Rad;
                desiredPosition = targetPosition +
                    new Vector3(Mathf.Sin(currentAngle), 0, Mathf.Cos(currentAngle)) * distanceFromTarget +
                    Vector3.up * heightOffset;
                desiredRotation = Quaternion.LookRotation(targetPosition - desiredPosition);
                break;
        }

        // Application des positions avec smoothing
        transform.position = Vector3.SmoothDamp(transform.position, desiredPosition, ref currentVelocity, smoothTime);
        transform.rotation = Quaternion.Slerp(transform.rotation, desiredRotation, smoothTime * 2);
    }

    public void SetTarget(Transform newTarget)
    {
        if (newTarget != null)
        {
            targetBoat = newTarget;
        }
    }

    public void SetCameraMode(CameraMode newMode)
    {
        currentMode = newMode;
    }
}