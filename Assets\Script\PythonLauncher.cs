using UnityEngine;
using System.Diagnostics;
using System.IO;
using UnityEngine.UI;

/// <summary>
/// Lance des scripts Python depuis Unity
/// </summary>
public class PythonLauncher : MonoBehaviour
{
    [Header("Configuration Python")]
    [SerializeField] private string pythonExecutablePath = "python"; // Chemin vers python.exe
    [SerializeField] private string scriptPath = ""; // Chemin vers le script Python
    [SerializeField] private string arguments = ""; // Arguments optionnels
    [SerializeField] private bool showDebugLogs = true;

    [Header("Interface")]
    [SerializeField] private Button launchButton;
    [SerializeField] private Text statusText;

    // État
    private Process currentProcess;
    private bool isRunning = false;

    private void Start()
    {
        InitializePythonLauncher();
    }

    /// <summary>
    /// Initialise le lanceur Python
    /// </summary>
    private void InitializePythonLauncher()
    {
        // Configurer le bouton
        if (launchButton != null)
        {
            launchButton.onClick.AddListener(LaunchPythonScript);
            UpdateButtonState();
        }

        // Détecter automatiquement Python si pas configuré
        if (string.IsNullOrEmpty(pythonExecutablePath) || pythonExecutablePath == "python")
        {
            DetectPythonPath();
        }

        // Chercher automatiquement le script s'il n'est pas configuré
        if (string.IsNullOrEmpty(scriptPath))
        {
            FindMeshtasticScript();
        }

        UpdateStatus("Prêt à lancer Python");
        LogMessage("🐍 PythonLauncher initialisé");
    }

    /// <summary>
    /// Détecte automatiquement le chemin vers Python
    /// </summary>
    private void DetectPythonPath()
    {
        string[] possiblePaths = {
            "python",
            "python3",
            @"C:\Python39\python.exe",
            @"C:\Python310\python.exe",
            @"C:\Python311\python.exe",
            @"C:\Python312\python.exe",
            @"C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe",
            @"C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe",
            @"C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe",
            @"C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe"
        };

        foreach (string path in possiblePaths)
        {
            if (TestPythonPath(path))
            {
                pythonExecutablePath = path;
                LogMessage($"✅ Python détecté : {path}");
                return;
            }
        }

        LogMessage("⚠️ Python non détecté automatiquement. Configurez manuellement le chemin.");
    }

    /// <summary>
    /// Teste si un chemin Python est valide
    /// </summary>
    private bool TestPythonPath(string path)
    {
        try
        {
            ProcessStartInfo startInfo = new ProcessStartInfo
            {
                FileName = path,
                Arguments = "--version",
                UseShellExecute = false,
                RedirectStandardOutput = true,
                CreateNoWindow = true
            };

            using (Process process = Process.Start(startInfo))
            {
                process.WaitForExit(3000); // Timeout 3 secondes
                return process.ExitCode == 0;
            }
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Cherche automatiquement le script Meshtastic
    /// </summary>
    private void FindMeshtasticScript()
    {
        string[] possiblePaths = {
            Path.Combine(Application.dataPath, "..", "meshtastic_gps_tracker.py"),
            Path.Combine(Application.dataPath, "..", "Scripts", "meshtastic_gps_tracker.py"),
            Path.Combine(Application.dataPath, "..", "Python", "meshtastic_gps_tracker.py"),
            "meshtastic_gps_tracker.py"
        };

        foreach (string path in possiblePaths)
        {
            if (File.Exists(path))
            {
                scriptPath = Path.GetFullPath(path);
                LogMessage($"✅ Script Python trouvé : {scriptPath}");
                return;
            }
        }

        LogMessage("⚠️ Script Python non trouvé automatiquement. Configurez manuellement le chemin.");
    }

    /// <summary>
    /// Lance le script Python
    /// </summary>
    [ContextMenu("Launch Python Script")]
    public void LaunchPythonScript()
    {
        if (isRunning)
        {
            LogMessage("⚠️ Un script Python est déjà en cours d'exécution");
            return;
        }

        if (string.IsNullOrEmpty(scriptPath) || !File.Exists(scriptPath))
        {
            LogMessage("❌ Chemin du script Python invalide ou fichier non trouvé");
            UpdateStatus("Erreur : Script non trouvé");
            return;
        }

        try
        {
            ProcessStartInfo startInfo = new ProcessStartInfo
            {
                FileName = pythonExecutablePath,
                Arguments = $"\"{scriptPath}\" {arguments}",
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true,
                WorkingDirectory = Path.GetDirectoryName(scriptPath)
            };

            currentProcess = new Process { StartInfo = startInfo };
            
            // Événements pour capturer la sortie
            currentProcess.OutputDataReceived += OnOutputDataReceived;
            currentProcess.ErrorDataReceived += OnErrorDataReceived;
            currentProcess.Exited += OnProcessExited;
            currentProcess.EnableRaisingEvents = true;

            currentProcess.Start();
            currentProcess.BeginOutputReadLine();
            currentProcess.BeginErrorReadLine();

            isRunning = true;
            UpdateButtonState();
            UpdateStatus("Script Python en cours...");
            LogMessage($"🚀 Script Python lancé : {scriptPath}");
        }
        catch (System.Exception e)
        {
            LogMessage($"❌ Erreur lors du lancement : {e.Message}");
            UpdateStatus($"Erreur : {e.Message}");
            isRunning = false;
            UpdateButtonState();
        }
    }

    /// <summary>
    /// Arrête le script Python
    /// </summary>
    [ContextMenu("Stop Python Script")]
    public void StopPythonScript()
    {
        if (currentProcess != null && !currentProcess.HasExited)
        {
            try
            {
                currentProcess.Kill();
                LogMessage("🛑 Script Python arrêté");
                UpdateStatus("Script arrêté");
            }
            catch (System.Exception e)
            {
                LogMessage($"⚠️ Erreur lors de l'arrêt : {e.Message}");
            }
        }

        isRunning = false;
        UpdateButtonState();
    }

    /// <summary>
    /// Gestion de la sortie standard
    /// </summary>
    private void OnOutputDataReceived(object sender, DataReceivedEventArgs e)
    {
        if (!string.IsNullOrEmpty(e.Data))
        {
            LogMessage($"[Python] {e.Data}");
        }
    }

    /// <summary>
    /// Gestion des erreurs
    /// </summary>
    private void OnErrorDataReceived(object sender, DataReceivedEventArgs e)
    {
        if (!string.IsNullOrEmpty(e.Data))
        {
            LogMessage($"[Python Error] {e.Data}");
        }
    }

    /// <summary>
    /// Gestion de la fin du processus
    /// </summary>
    private void OnProcessExited(object sender, System.EventArgs e)
    {
        isRunning = false;
        
        // Utiliser Invoke pour mettre à jour l'UI depuis le thread principal
        if (this != null)
        {
            UnityMainThreadDispatcher.Instance().Enqueue(() => {
                UpdateButtonState();
                UpdateStatus("Script terminé");
                LogMessage("✅ Script Python terminé");
            });
        }
    }

    /// <summary>
    /// Met à jour l'état du bouton
    /// </summary>
    private void UpdateButtonState()
    {
        if (launchButton != null)
        {
            launchButton.interactable = !isRunning;
            
            Text buttonText = launchButton.GetComponentInChildren<Text>();
            if (buttonText != null)
            {
                buttonText.text = isRunning ? "Script en cours..." : "Lancer Python";
            }
        }
    }

    /// <summary>
    /// Met à jour le texte de statut
    /// </summary>
    private void UpdateStatus(string message)
    {
        if (statusText != null)
        {
            statusText.text = message;
        }
    }

    /// <summary>
    /// Affiche un message de log
    /// </summary>
    private void LogMessage(string message)
    {
        if (showDebugLogs)
        {
            UnityEngine.Debug.Log($"[PythonLauncher] {message}");
        }
    }

    /// <summary>
    /// Définit le chemin du script Python
    /// </summary>
    public void SetScriptPath(string path)
    {
        scriptPath = path;
        LogMessage($"Chemin du script défini : {path}");
    }

    /// <summary>
    /// Définit les arguments du script
    /// </summary>
    public void SetArguments(string args)
    {
        arguments = args;
        LogMessage($"Arguments définis : {args}");
    }

    /// <summary>
    /// Vérifie si le script est en cours d'exécution
    /// </summary>
    public bool IsRunning => isRunning;

    /// <summary>
    /// Nettoyage à la destruction
    /// </summary>
    private void OnDestroy()
    {
        StopPythonScript();
    }

    /// <summary>
    /// Nettoyage à la fermeture de l'application
    /// </summary>
    private void OnApplicationQuit()
    {
        StopPythonScript();
    }

    /// <summary>
    /// Test de la configuration Python
    /// </summary>
    [ContextMenu("Test Python Configuration")]
    public void TestPythonConfiguration()
    {
        LogMessage("🧪 === TEST CONFIGURATION PYTHON ===");
        LogMessage($"Exécutable Python : {pythonExecutablePath}");
        LogMessage($"Script Python : {scriptPath}");
        LogMessage($"Arguments : {arguments}");
        
        bool pythonValid = TestPythonPath(pythonExecutablePath);
        LogMessage($"Python valide : {(pythonValid ? "✅" : "❌")}");
        
        bool scriptExists = File.Exists(scriptPath);
        LogMessage($"Script existe : {(scriptExists ? "✅" : "❌")}");
        
        LogMessage("=== FIN TEST ===");
    }
}
