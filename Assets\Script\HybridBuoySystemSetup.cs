using UnityEngine;

/// <summary>
/// Configuration automatique du système hybride de placement des bouées
/// Combine le placement automatique (Meshtastic) et manuel (Unity UI)
/// </summary>
public class HybridBuoySystemSetup : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private bool setupOnStart = true;
    [SerializeField] private bool createUI = true;
    [SerializeField] private bool prioritizeManualBuoys = false;
    [SerializeField] private bool showDebugLogs = true;

    [Header("Raccourcis Clavier")]
    [SerializeField] private KeyCode setupSystemKey = KeyCode.F9;
    [SerializeField] private KeyCode toggleUIKey = KeyCode.B;
    [SerializeField] private KeyCode showInfoKey = KeyCode.I;

    private void Start()
    {
        if (setupOnStart)
        {
            SetupHybridSystem();
        }
    }

    private void Update()
    {
        if (Input.GetKeyDown(setupSystemKey))
        {
            SetupHybridSystem();
        }

        if (Input.GetKeyDown(showInfoKey))
        {
            ShowSystemInfo();
        }
    }

    /// <summary>
    /// Configure le système hybride complet
    /// </summary>
    [ContextMenu("Setup Hybrid Buoy System")]
    public void SetupHybridSystem()
    {
        LogMessage("🚀 === CONFIGURATION DU SYSTÈME HYBRIDE DE BOUÉES ===");

        // 1. Configurer le RaceLineManager
        SetupRaceLineManager();

        // 2. Configurer le ManualBuoyPlacer
        SetupManualBuoyPlacer();

        // 3. Créer l'interface utilisateur simple si demandé
        if (createUI)
        {
            SetupSimpleBuoyUI();
        }

        // 4. Vérifier la configuration
        VerifySystemSetup();

        LogMessage("=== SYSTÈME HYBRIDE CONFIGURÉ ===");
    }

    /// <summary>
    /// Configure le RaceLineManager
    /// </summary>
    private void SetupRaceLineManager()
    {
        var raceLineManager = FindObjectOfType<RaceLineManager>();
        if (raceLineManager == null)
        {
            GameObject raceLineObject = new GameObject("Race Line Manager");
            raceLineManager = raceLineObject.AddComponent<RaceLineManager>();
            LogMessage("✅ RaceLineManager créé");
        }
        else
        {
            LogMessage("✅ RaceLineManager existant trouvé");
        }

        // Configurer pour accepter les bouées manuelles
        SetPrivateField(raceLineManager, "acceptManualBuoys", true);
        SetPrivateField(raceLineManager, "prioritizeManualBuoys", prioritizeManualBuoys);
        SetPrivateField(raceLineManager, "showDebugLogs", showDebugLogs);

        LogMessage("✅ RaceLineManager configuré pour le mode hybride");
    }

    /// <summary>
    /// Configure le ManualBuoyPlacer
    /// </summary>
    private void SetupManualBuoyPlacer()
    {
        var manualPlacer = FindObjectOfType<ManualBuoyPlacer>();
        if (manualPlacer == null)
        {
            GameObject placerObject = new GameObject("Manual Buoy Placer");
            manualPlacer = placerObject.AddComponent<ManualBuoyPlacer>();
            LogMessage("✅ ManualBuoyPlacer créé");
        }
        else
        {
            LogMessage("✅ ManualBuoyPlacer existant trouvé");
        }

        // Configurer les paramètres
        SetPrivateField(manualPlacer, "enableManualPlacement", true);
        SetPrivateField(manualPlacer, "overrideAutomaticPlacement", prioritizeManualBuoys);
        SetPrivateField(manualPlacer, "showDebugLogs", showDebugLogs);

        LogMessage("✅ ManualBuoyPlacer configuré");
    }

    /// <summary>
    /// Configure l'interface utilisateur simple
    /// </summary>
    private void SetupSimpleBuoyUI()
    {
        var simpleBuoyUI = FindObjectOfType<SimpleBuoyUI>();
        if (simpleBuoyUI == null)
        {
            GameObject uiObject = new GameObject("Simple Buoy UI");
            simpleBuoyUI = uiObject.AddComponent<SimpleBuoyUI>();
            LogMessage("✅ Interface utilisateur simple créée");
        }
        else
        {
            LogMessage("✅ Interface utilisateur simple existante trouvée");
        }

        // Configurer l'UI simple
        SetPrivateField(simpleBuoyUI, "showDebugLogs", showDebugLogs);

        LogMessage("✅ Interface utilisateur simple configurée");
    }

    /// <summary>
    /// Vérifie la configuration du système
    /// </summary>
    private void VerifySystemSetup()
    {
        LogMessage("🔍 === VÉRIFICATION DU SYSTÈME ===");

        // Vérifier les composants principaux
        var raceLineManager = FindObjectOfType<RaceLineManager>();
        var manualPlacer = FindObjectOfType<ManualBuoyPlacer>();
        var simpleBuoyUI = FindObjectOfType<SimpleBuoyUI>();
        var gpsTracker = FindObjectOfType<MeshtasticGPSTracker>();

        LogMessage($"RaceLineManager: {(raceLineManager != null ? "✅" : "❌")}");
        LogMessage($"ManualBuoyPlacer: {(manualPlacer != null ? "✅" : "❌")}");
        LogMessage($"SimpleBuoyUI: {(simpleBuoyUI != null ? "✅" : "❌")}");
        LogMessage($"MeshtasticGPSTracker: {(gpsTracker != null ? "✅" : "⚠️")}");

        // Vérifier la carte Mapbox
        var map = FindObjectOfType<Mapbox.Unity.Map.AbstractMap>();
        LogMessage($"Carte Mapbox: {(map != null ? "✅" : "❌")}");

        // Afficher les modes de fonctionnement
        LogMessage("📋 === MODES DE FONCTIONNEMENT ===");
        LogMessage($"🤖 Placement automatique: {(gpsTracker != null ? "Disponible (Messages B1/B2)" : "Non disponible")}");
        LogMessage($"🖱️ Placement manuel: {(manualPlacer != null ? "Disponible (Interface Unity)" : "Non disponible")}");
        LogMessage($"🎛️ Interface utilisateur: {(simpleBuoyUI != null ? $"Disponible (Touche {toggleUIKey})" : "Non disponible")}");
        LogMessage($"🔄 Priorité: {(prioritizeManualBuoys ? "Bouées manuelles" : "Bouées automatiques")}");

        // Afficher les raccourcis clavier
        LogMessage("⌨️ === RACCOURCIS CLAVIER ===");
        LogMessage($"F1: Placer bouée de départ manuellement");
        LogMessage($"F2: Placer bouée d'arrivée manuellement");
        LogMessage($"F3: Basculer mode manuel");
        LogMessage($"{toggleUIKey}: Afficher/masquer interface");
        LogMessage($"{setupSystemKey}: Reconfigurer système");
        LogMessage($"{showInfoKey}: Afficher informations système");
    }

    /// <summary>
    /// Affiche les informations du système
    /// </summary>
    [ContextMenu("Show System Info")]
    public void ShowSystemInfo()
    {
        LogMessage("📊 === INFORMATIONS DU SYSTÈME ===");

        var raceLineManager = FindObjectOfType<RaceLineManager>();
        if (raceLineManager != null)
        {
            raceLineManager.ShowDebugInfo();
        }

        var manualPlacer = FindObjectOfType<ManualBuoyPlacer>();
        if (manualPlacer != null)
        {
            manualPlacer.ShowDebugInfo();
        }

        LogMessage("=== FIN DES INFORMATIONS ===");
    }

    /// <summary>
    /// Test complet du système
    /// </summary>
    [ContextMenu("Test Complete System")]
    public void TestCompleteSystem()
    {
        LogMessage("🧪 === TEST COMPLET DU SYSTÈME ===");

        // Test du placement manuel
        var manualPlacer = FindObjectOfType<ManualBuoyPlacer>();
        if (manualPlacer != null)
        {
            LogMessage("🧪 Test du placement manuel...");
            manualPlacer.SetManualStartPosition(43.123456, 5.654321);
            manualPlacer.SetManualFinishPosition(43.124456, 5.655321);
            manualPlacer.PlaceBothBuoys();
        }

        // Afficher l'interface simple
        var simpleBuoyUI = FindObjectOfType<SimpleBuoyUI>();
        if (simpleBuoyUI != null)
        {
            LogMessage("🧪 Affichage de l'interface utilisateur simple...");
            simpleBuoyUI.ShowUI();
        }

        LogMessage("=== TEST TERMINÉ ===");
    }

    /// <summary>
    /// Bascule la priorité des bouées
    /// </summary>
    [ContextMenu("Toggle Buoy Priority")]
    public void ToggleBuoyPriority()
    {
        prioritizeManualBuoys = !prioritizeManualBuoys;

        // Mettre à jour le RaceLineManager
        var raceLineManager = FindObjectOfType<RaceLineManager>();
        if (raceLineManager != null)
        {
            SetPrivateField(raceLineManager, "prioritizeManualBuoys", prioritizeManualBuoys);
        }

        // Mettre à jour le ManualBuoyPlacer
        var manualPlacer = FindObjectOfType<ManualBuoyPlacer>();
        if (manualPlacer != null)
        {
            SetPrivateField(manualPlacer, "overrideAutomaticPlacement", prioritizeManualBuoys);
        }

        LogMessage($"🔄 Priorité basculée vers: {(prioritizeManualBuoys ? "Bouées manuelles" : "Bouées automatiques")}");
    }

    /// <summary>
    /// Nettoie et reconfigure tout le système
    /// </summary>
    [ContextMenu("Clean and Reconfigure")]
    public void CleanAndReconfigure()
    {
        LogMessage("🧹 Nettoyage et reconfiguration complète...");

        // Supprimer les anciens composants
        var oldManagers = FindObjectsOfType<RaceLineManager>();
        var oldPlacers = FindObjectsOfType<ManualBuoyPlacer>();
        var oldUIs = FindObjectsOfType<BuoyPlacementUI>();

        foreach (var manager in oldManagers)
        {
            if (manager.gameObject != gameObject)
                DestroyImmediate(manager.gameObject);
        }

        foreach (var placer in oldPlacers)
        {
            if (placer.gameObject != gameObject)
                DestroyImmediate(placer.gameObject);
        }

        foreach (var ui in oldUIs)
        {
            if (ui.gameObject != gameObject)
                DestroyImmediate(ui.gameObject);
        }

        // Reconfigurer
        SetupHybridSystem();
    }

    /// <summary>
    /// Utilise la réflexion pour définir un champ privé
    /// </summary>
    private void SetPrivateField(object obj, string fieldName, object value)
    {
        var field = obj.GetType().GetField(fieldName,
            System.Reflection.BindingFlags.NonPublic |
            System.Reflection.BindingFlags.Instance);

        if (field != null)
        {
            field.SetValue(obj, value);
        }
        else
        {
            LogMessage($"⚠️ Champ '{fieldName}' non trouvé dans {obj.GetType().Name}");
        }
    }

    /// <summary>
    /// Affiche un message de log si les logs de debug sont activés
    /// </summary>
    private void LogMessage(string message)
    {
        if (showDebugLogs)
        {
            Debug.Log($"[HybridBuoySystemSetup] {message}");
        }
    }

    private void OnGUI()
    {
        if (!showDebugLogs) return;

        // Affichage des informations en overlay
        GUI.color = Color.white;
        GUILayout.BeginArea(new Rect(10, 10, 300, 150));
        GUILayout.BeginVertical("box");

        GUILayout.Label("🎯 SYSTÈME HYBRIDE DE BOUÉES", GUI.skin.label);
        GUILayout.Space(5);

        if (GUILayout.Button($"🔧 Configurer Système ({setupSystemKey})"))
        {
            SetupHybridSystem();
        }

        if (GUILayout.Button($"🖱️ Interface UI ({toggleUIKey})"))
        {
            var simpleBuoyUI = FindObjectOfType<SimpleBuoyUI>();
            if (simpleBuoyUI != null) simpleBuoyUI.ToggleUI();
        }

        if (GUILayout.Button($"📊 Infos Système ({showInfoKey})"))
        {
            ShowSystemInfo();
        }

        if (GUILayout.Button("🧪 Test Complet"))
        {
            TestCompleteSystem();
        }

        GUILayout.Label($"Priorité: {(prioritizeManualBuoys ? "Manuel" : "Auto")}", GUI.skin.label);

        GUILayout.EndVertical();
        GUILayout.EndArea();
    }
}
