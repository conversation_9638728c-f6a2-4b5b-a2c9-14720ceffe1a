# Système GPS-Waypoint pour Bateaux

## Vue d'ensemble

Ce système remplace les paramètres de simulation (baseSpeed, speedVariation, boost, etc.) par un mouvement basé sur les vraies données GPS du Meshtastic tracker. Les bateaux suivent maintenant des chemins générés à partir des coordonnées GPS réelles avec un mouvement fluide et réaliste.

## Architecture du Système

### 1. **BoatWaypointMover** (Refactorisé)
- **Supprimé** : Tous les paramètres de simulation artificielle
- **Ajouté** : Buffer GPS, calcul de vitesse réelle, pathfinding GPS
- **Fonctionnalités** :
  - Stocke les 5 derniers points GPS reçus
  - Calcule la vitesse réelle à partir des coordonnées GPS et du temps
  - Nécessite minimum 3 points GPS avant de commencer le mouvement
  - Crée un pathfinding fluide avec interpolation entre les points
  - Applique un décalage latéral pour éviter la superposition des bateaux

### 2. **GPSWaypointIntegrator**
- **Rôle** : Connecte automatiquement le système GPS existant avec le nouveau système de waypoints
- **Fonctionnalités** :
  - Détecte automatiquement tous les bateaux avec `UnifiedBoatGPS`
  - Ajoute et configure le `BoatWaypointMover` sur chaque bateau
  - Configure les paramètres de mouvement
  - Gère la transition entre les modes de mouvement

### 3. **GPSToWaypointAdapter**
- **Rôle** : Fait le lien entre `UnifiedBoatGPS` et `BoatWaypointMover`
- **Fonctionnalités** :
  - Intercepte les mises à jour GPS
  - Transfère les coordonnées GPS vers le système de waypoints
  - Surveille les changements de position GPS

### 4. **GPSWaypointDemo**
- **Rôle** : Système de test et démonstration
- **Fonctionnalités** :
  - Simule des données GPS pour tester le système
  - Génère des trajectoires de test
  - Permet de valider le fonctionnement

### 5. **GPSWaypointSetup**
- **Rôle** : Installation automatique du système complet
- **Fonctionnalités** :
  - Configure tous les composants en une seule fois
  - Vérifie la configuration
  - Permet le nettoyage et la reconfiguration

## Installation et Configuration

### Installation Automatique (Recommandée)

1. **Ajouter le script de setup** :
   ```csharp
   // Ajouter GPSWaypointSetup à un GameObject dans la scène
   GameObject setupObject = new GameObject("GPS Waypoint Setup");
   setupObject.AddComponent<GPSWaypointSetup>();
   ```

2. **Configurer les paramètres** dans l'inspecteur :
   - `Setup On Start` : true (configuration automatique au démarrage)
   - `Fleet Container` : Référence vers le conteneur des bateaux
   - `Max Speed` : Vitesse maximum en m/s (défaut: 15)
   - `Min GPS Points` : Nombre minimum de points GPS (défaut: 3)

3. **Lancer la configuration** :
   - Automatique au démarrage, ou
   - Clic droit → "Setup GPS Waypoint System"

### Installation Manuelle

1. **Ajouter l'intégrateur** :
   ```csharp
   GameObject integratorObject = new GameObject("GPS Waypoint Integrator");
   GPSWaypointIntegrator integrator = integratorObject.AddComponent<GPSWaypointIntegrator>();
   integrator.InitializeIntegration();
   ```

2. **Pour chaque bateau** :
   ```csharp
   // Le bateau doit avoir UnifiedBoatGPS
   BoatWaypointMover waypointMover = boat.AddComponent<BoatWaypointMover>();
   GPSToWaypointAdapter adapter = boat.AddComponent<GPSToWaypointAdapter>();
   adapter.Initialize(boat.GetComponent<UnifiedBoatGPS>(), waypointMover);
   ```

## Utilisation

### Envoi de Données GPS

Le système s'intègre automatiquement avec le `MeshtasticGPSTracker` existant. Les données GPS sont automatiquement transférées vers le système de waypoints.

**Méthode manuelle** :
```csharp
// Via l'adaptateur
GPSToWaypointAdapter adapter = boat.GetComponent<GPSToWaypointAdapter>();
adapter.ForceGPSUpdate(new Vector2d(latitude, longitude));

// Ou directement via le BoatWaypointMover
BoatWaypointMover mover = boat.GetComponent<BoatWaypointMover>();
mover.AddGPSPosition(new Vector2d(latitude, longitude), Time.time);
```

### Contrôle du Système

```csharp
GPSWaypointIntegrator integrator = FindObjectOfType<GPSWaypointIntegrator>();

// Activer/désactiver le mouvement par waypoints
integrator.SetWaypointMovementEnabled(true/false);

// Afficher les statistiques
integrator.LogIntegrationStats();

// Réinitialiser l'intégration
integrator.InitializeIntegration();
```

### Test et Démonstration

```csharp
GPSWaypointDemo demo = FindObjectOfType<GPSWaypointDemo>();

// Démarrer la démonstration
demo.StartDemo();

// Envoyer un point GPS de test
demo.SendSingleGPSPoint();

// Arrêter la démonstration
demo.StopDemo();
```

## Paramètres de Configuration

### BoatWaypointMover
- **maxSpeed** : Vitesse maximum en m/s (15)
- **rotationSpeed** : Vitesse de rotation (2)
- **pathFollowDelay** : Délai de suivi du chemin GPS (2s)
- **inertiaFactor** : Facteur d'inertie pour le mouvement fluide (3)
- **minGPSPoints** : Nombre minimum de points GPS pour commencer (3)
- **gpsAccuracy** : Précision GPS en mètres (2)
- **lateralOffset** : Décalage latéral pour éviter la superposition (2)

### Fonctionnement du Pathfinding

1. **Réception GPS** : Le système stocke les 5 derniers points GPS
2. **Calcul de vitesse** : Vitesse calculée à partir de la distance et du temps entre les points
3. **Validation** : Mouvement uniquement si distance > précision GPS
4. **Pathfinding** : Création de waypoints interpolés avec décalage latéral
5. **Mouvement** : Suivi fluide du chemin avec inertie et anticipation des virages

## Debugging et Monitoring

### Logs de Debug
```csharp
// Activer les logs détaillés
waypointMover.showDebugInfo = true;
adapter.showDebugLogs = true;
```

### Visualisation
```csharp
// Activer la visualisation des chemins GPS
waypointMover.showGPSPath = true;
```

### Statistiques en Temps Réel
```csharp
// Obtenir les statistiques d'un bateau
float speed = waypointMover.GetCurrentSpeedInKnots();
bool hasPath = waypointMover.HasValidPath();
int gpsPoints = waypointMover.GetGPSBufferCount();

// Statistiques de l'adaptateur
var stats = adapter.GetStats();
```

## Dépannage

### Problèmes Courants

1. **Les bateaux ne bougent pas** :
   - Vérifier que `minGPSPoints` points GPS ont été reçus
   - Vérifier que la carte Mapbox est initialisée
   - Vérifier les logs de debug

2. **Mouvement saccadé** :
   - Augmenter `inertiaFactor`
   - Vérifier la qualité des données GPS
   - Ajuster `gpsAccuracy`

3. **Bateaux superposés** :
   - Augmenter `lateralOffset`
   - Vérifier que le décalage latéral fonctionne

### Commandes de Debug

```csharp
// Dans l'inspecteur, clic droit sur les composants :
// - "Log Integration Stats" (GPSWaypointIntegrator)
// - "Log Adapter Stats" (GPSToWaypointAdapter)
// - "Show System Stats" (GPSWaypointSetup)
```

## Migration depuis l'Ancien Système

L'ancien système avec `baseSpeed`, `speedVariation`, `boost`, etc. est automatiquement remplacé. Aucune migration manuelle n'est nécessaire si vous utilisez le système d'installation automatique.

**Avantages du nouveau système** :
- ✅ Mouvement basé sur les vraies données GPS
- ✅ Vitesse calculée à partir des coordonnées réelles
- ✅ Pathfinding fluide et réaliste
- ✅ Évitement automatique de la superposition
- ✅ Intégration transparente avec le système existant
- ✅ Mouvement indépendant de l'état de la course
