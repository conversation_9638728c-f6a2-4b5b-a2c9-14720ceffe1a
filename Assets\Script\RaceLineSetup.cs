using UnityEngine;

/// <summary>
/// Script d'installation automatique pour le système de lignes de course
/// Configure automatiquement les lignes de départ et d'arrivée entre le bateau sécurité et les bouées B1/B2
/// </summary>
public class RaceLineSetup : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private bool setupOnStart = true;
    [SerializeField] private string securityBoatNodeId = "!da5394d0";
    [SerializeField] private bool showDebugLogs = true;
    
    [Header("Ligne de Départ")]
    [SerializeField] private bool enableStartLine = true;
    [SerializeField] private Color startLineColor = Color.green;
    [SerializeField] private float startLineWidth = 0.8f;
    
    [Header("Ligne d'Arrivée")]
    [SerializeField] private bool enableFinishLine = true;
    [SerializeField] private Color finishLineColor = Color.red;
    [SerializeField] private float finishLineWidth = 0.8f;
    
    [Header("Paramètres")]
    [SerializeField] private float lineHeight = 2f;

    private void Start()
    {
        if (setupOnStart)
        {
            SetupRaceLineSystem();
        }
    }

    /// <summary>
    /// Configure automatiquement le système de lignes de course
    /// </summary>
    [ContextMenu("Setup Race Line System")]
    public void SetupRaceLineSystem()
    {
        LogMessage("🏁 === CONFIGURATION DU SYSTÈME DE LIGNES DE COURSE ===");

        // 1. Vérifier que le RaceLineManager existe
        var raceLineManager = FindObjectOfType<RaceLineManager>();
        if (raceLineManager == null)
        {
            // Créer un nouveau GameObject pour le RaceLineManager
            GameObject raceLineObject = new GameObject("Race Line Manager");
            raceLineManager = raceLineObject.AddComponent<RaceLineManager>();
            LogMessage("✅ RaceLineManager créé");
        }
        else
        {
            LogMessage("✅ RaceLineManager existant trouvé");
        }

        // 2. Configurer le RaceLineManager
        ConfigureRaceLineManager(raceLineManager);

        // 3. Vérifier la configuration
        VerifySetup();

        LogMessage("=== CONFIGURATION TERMINÉE ===");
    }

    /// <summary>
    /// Configure les paramètres du RaceLineManager
    /// </summary>
    private void ConfigureRaceLineManager(RaceLineManager manager)
    {
        // Utiliser la réflexion pour configurer les champs privés
        SetPrivateField(manager, "securityBoatNodeId", securityBoatNodeId);
        SetPrivateField(manager, "showStartingLine", enableStartLine);
        SetPrivateField(manager, "showFinishLine", enableFinishLine);
        SetPrivateField(manager, "startLineColor", startLineColor);
        SetPrivateField(manager, "startLineWidth", startLineWidth);
        SetPrivateField(manager, "finishLineColor", finishLineColor);
        SetPrivateField(manager, "finishLineWidth", finishLineWidth);
        SetPrivateField(manager, "lineHeight", lineHeight);
        SetPrivateField(manager, "showDebugLogs", showDebugLogs);

        LogMessage("✅ RaceLineManager configuré");
    }

    /// <summary>
    /// Vérifie que la configuration est correcte
    /// </summary>
    private void VerifySetup()
    {
        LogMessage("🔍 Vérification de la configuration...");

        // Vérifier le RaceLineManager
        var raceLineManager = FindObjectOfType<RaceLineManager>();
        if (raceLineManager == null)
        {
            LogMessage("❌ RaceLineManager non trouvé !");
            return;
        }

        // Vérifier le MeshtasticGPSTracker
        var gpsTracker = FindObjectOfType<MeshtasticGPSTracker>();
        if (gpsTracker == null)
        {
            LogMessage("⚠️ MeshtasticGPSTracker non trouvé - nécessaire pour les événements de bouées");
        }
        else
        {
            LogMessage("✅ MeshtasticGPSTracker trouvé");
        }

        // Vérifier le RaceController
        var raceController = FindObjectOfType<RaceController>();
        if (raceController == null)
        {
            LogMessage("⚠️ RaceController non trouvé");
        }
        else
        {
            LogMessage("✅ RaceController trouvé");
        }

        // Rechercher le bateau sécurité
        bool securityBoatFound = FindSecurityBoat();
        if (securityBoatFound)
        {
            LogMessage("✅ Bateau sécurité trouvé");
        }
        else
        {
            LogMessage($"⚠️ Bateau sécurité avec NodeId {securityBoatNodeId} non trouvé");
        }

        // Rechercher les bouées existantes
        var startBuoy = GameObject.Find("StartBuoy");
        var finishBuoy = GameObject.Find("EndBuoy");
        
        LogMessage($"Bouée de départ (B1): {(startBuoy != null ? "✅ Trouvée" : "⚠️ Non trouvée")}");
        LogMessage($"Bouée d'arrivée (B2): {(finishBuoy != null ? "✅ Trouvée" : "⚠️ Non trouvée")}");

        LogMessage("=== RÉSUMÉ DE LA VÉRIFICATION ===");
        LogMessage($"- RaceLineManager: {(raceLineManager != null ? "✅" : "❌")}");
        LogMessage($"- MeshtasticGPSTracker: {(gpsTracker != null ? "✅" : "⚠️")}");
        LogMessage($"- RaceController: {(raceController != null ? "✅" : "⚠️")}");
        LogMessage($"- Bateau sécurité: {(securityBoatFound ? "✅" : "⚠️")}");
        LogMessage($"- Bouées: {(startBuoy != null && finishBuoy != null ? "✅" : "⚠️")}");
    }

    /// <summary>
    /// Recherche le bateau sécurité
    /// </summary>
    private bool FindSecurityBoat()
    {
        var gpsTracker = FindObjectOfType<MeshtasticGPSTracker>();
        if (gpsTracker == null) return false;

        // Utiliser la réflexion pour accéder à la liste des bateaux
        var boatsField = typeof(MeshtasticGPSTracker).GetField("boats", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (boatsField != null)
        {
            var boats = boatsField.GetValue(gpsTracker) as System.Collections.IList;
            if (boats != null)
            {
                foreach (var boat in boats)
                {
                    var nodeIdField = boat.GetType().GetField("associatedMacAddress");
                    if (nodeIdField != null)
                    {
                        var nodeId = nodeIdField.GetValue(boat) as string;
                        if (nodeId == securityBoatNodeId)
                        {
                            return true;
                        }
                    }
                }
            }
        }

        return false;
    }

    /// <summary>
    /// Utilise la réflexion pour définir un champ privé
    /// </summary>
    private void SetPrivateField(object obj, string fieldName, object value)
    {
        var field = obj.GetType().GetField(fieldName, 
            System.Reflection.BindingFlags.NonPublic | 
            System.Reflection.BindingFlags.Instance);
        
        if (field != null)
        {
            field.SetValue(obj, value);
        }
        else
        {
            LogMessage($"⚠️ Champ '{fieldName}' non trouvé dans {obj.GetType().Name}");
        }
    }

    /// <summary>
    /// Force la création des lignes de test
    /// </summary>
    [ContextMenu("Create Test Lines")]
    public void CreateTestLines()
    {
        LogMessage("🧪 Création de lignes de test...");

        var raceLineManager = FindObjectOfType<RaceLineManager>();
        if (raceLineManager == null)
        {
            LogMessage("❌ RaceLineManager non trouvé, création automatique...");
            SetupRaceLineSystem();
            raceLineManager = FindObjectOfType<RaceLineManager>();
        }

        if (raceLineManager != null)
        {
            // Forcer l'affichage des informations de debug
            raceLineManager.ShowDebugInfo();
        }
    }

    /// <summary>
    /// Nettoie et reconfigure le système
    /// </summary>
    [ContextMenu("Clean and Reconfigure")]
    public void CleanAndReconfigure()
    {
        LogMessage("🧹 Nettoyage et reconfiguration...");

        // Supprimer les anciens RaceLineManager
        var oldManagers = FindObjectsOfType<RaceLineManager>();
        foreach (var manager in oldManagers)
        {
            if (manager.gameObject != gameObject)
            {
                DestroyImmediate(manager.gameObject);
            }
        }

        // Reconfigurer
        SetupRaceLineSystem();
    }

    /// <summary>
    /// Test manuel de placement de bouées
    /// </summary>
    [ContextMenu("Test Buoy Placement")]
    public void TestBuoyPlacement()
    {
        LogMessage("🎯 Test de placement de bouées...");

        // Créer des bouées de test si elles n'existent pas
        var startBuoy = GameObject.Find("StartBuoy");
        var finishBuoy = GameObject.Find("EndBuoy");

        if (startBuoy == null)
        {
            startBuoy = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            startBuoy.name = "StartBuoy";
            startBuoy.transform.position = new Vector3(0, 0, 0);
            startBuoy.GetComponent<Renderer>().material.color = Color.green;
            LogMessage("✅ Bouée de départ de test créée");
        }

        if (finishBuoy == null)
        {
            finishBuoy = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            finishBuoy.name = "EndBuoy";
            finishBuoy.transform.position = new Vector3(100, 0, 0);
            finishBuoy.GetComponent<Renderer>().material.color = Color.red;
            LogMessage("✅ Bouée d'arrivée de test créée");
        }

        // Notifier le RaceLineManager
        var raceLineManager = FindObjectOfType<RaceLineManager>();
        if (raceLineManager != null)
        {
            raceLineManager.OnBuoysPlaced(startBuoy, finishBuoy);
            LogMessage("✅ Bouées de test envoyées au RaceLineManager");
        }
    }

    /// <summary>
    /// Affiche un message de log si les logs de debug sont activés
    /// </summary>
    private void LogMessage(string message)
    {
        if (showDebugLogs)
        {
            Debug.Log($"[RaceLineSetup] {message}");
        }
    }
}
