using UnityEngine;
using UnityEngine.UI;
using TMPro;
using Mapbox.Utils;

/// <summary>
/// Interface utilisateur pour le placement manuel des bouées
/// Permet de saisir les coordonnées GPS et de placer les bouées facilement
/// </summary>
public class BuoyPlacementUI : MonoBehaviour
{
    [Header("UI References")]
    [SerializeField] private GameObject uiPanel;
    [SerializeField] private TMP_InputField startLatitudeInput;
    [SerializeField] private TMP_InputField startLongitudeInput;
    [SerializeField] private TMP_InputField finishLatitudeInput;
    [SerializeField] private TMP_InputField finishLongitudeInput;
    [SerializeField] private Button placeStartButton;
    [SerializeField] private Button placeFinishButton;
    [SerializeField] private Button placeBothButton;
    [SerializeField] private Button clearButton;
    [SerializeField] private Toggle manualModeToggle;
    [SerializeField] private Toggle overrideToggle;
    [SerializeField] private TextMeshProUGUI statusText;

    [Header("Configuration")]
    [SerializeField] private KeyCode toggleUIKey = KeyCode.B;
    [SerializeField] private bool showUIOnStart = false;
    [SerializeField] private bool autoCreateUI = true;

    [Header("Default Positions")]
    [SerializeField] private Vector2d defaultStartPosition = new Vector2d(43.123456, 5.654321);
    [SerializeField] private Vector2d defaultFinishPosition = new Vector2d(43.124456, 5.655321);

    // Références
    private ManualBuoyPlacer manualPlacer;
    private Canvas canvas;

    private void Start()
    {
        InitializeUI();

        if (showUIOnStart)
        {
            ShowUI();
        }
        else
        {
            HideUI();
        }
    }

    private void Update()
    {
        if (Input.GetKeyDown(toggleUIKey))
        {
            ToggleUI();
        }
    }

    /// <summary>
    /// Initialise l'interface utilisateur
    /// </summary>
    private void InitializeUI()
    {
        // Trouver ou créer le ManualBuoyPlacer
        manualPlacer = FindObjectOfType<ManualBuoyPlacer>();
        if (manualPlacer == null)
        {
            GameObject placerObject = new GameObject("Manual Buoy Placer");
            manualPlacer = placerObject.AddComponent<ManualBuoyPlacer>();
            Debug.Log("[BuoyPlacementUI] ManualBuoyPlacer créé automatiquement");
        }

        // Créer l'UI automatiquement si nécessaire
        if (autoCreateUI && uiPanel == null)
        {
            CreateUI();
        }

        // Configurer les événements des boutons
        SetupButtonEvents();

        // Initialiser les valeurs par défaut
        SetDefaultValues();

        Debug.Log("[BuoyPlacementUI] Interface utilisateur initialisée");
    }

    /// <summary>
    /// Crée automatiquement l'interface utilisateur simple
    /// </summary>
    private void CreateUI()
    {
        // Vérifier s'il y a déjà un Canvas dans la scène
        canvas = FindObjectOfType<Canvas>();
        if (canvas == null)
        {
            // Créer le Canvas principal seulement s'il n'existe pas
            GameObject canvasObject = new GameObject("Main Canvas");
            canvas = canvasObject.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvas.sortingOrder = 10;

            CanvasScaler scaler = canvasObject.AddComponent<CanvasScaler>();
            scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
            scaler.referenceResolution = new Vector2(1920, 1080);

            canvasObject.AddComponent<GraphicRaycaster>();
        }

        // Créer un panel simple et compact
        CreateSimplePanel();

        Debug.Log("[BuoyPlacementUI] Interface utilisateur simple créée");
    }

    /// <summary>
    /// Crée un panel simple et compact
    /// </summary>
    private void CreateSimplePanel()
    {
        // Panel principal compact
        GameObject panelObject = new GameObject("Buoy Placement Panel");
        panelObject.transform.SetParent(canvas.transform, false);

        Image panelImage = panelObject.AddComponent<Image>();
        panelImage.color = new Color(0.1f, 0.1f, 0.1f, 0.9f);

        // Positionnement en haut à droite, compact
        RectTransform panelRect = panelObject.GetComponent<RectTransform>();
        panelRect.anchorMin = new Vector2(0.75f, 0.75f);
        panelRect.anchorMax = new Vector2(0.98f, 0.98f);
        panelRect.offsetMin = Vector2.zero;
        panelRect.offsetMax = Vector2.zero;

        uiPanel = panelObject;

        // Créer le contenu simple
        CreateSimpleContent(panelObject);
    }

    /// <summary>
    /// Crée le contenu simple de l'interface utilisateur
    /// </summary>
    private void CreateSimpleContent(GameObject parent)
    {
        // Layout vertical compact
        VerticalLayoutGroup layout = parent.AddComponent<VerticalLayoutGroup>();
        layout.padding = new RectOffset(8, 8, 8, 8);
        layout.spacing = 3;
        layout.childControlHeight = false;
        layout.childControlWidth = true;
        layout.childForceExpandHeight = false;
        layout.childForceExpandWidth = true;

        // Titre compact
        CreateLabel(parent, "🎯 BOUÉES", 12, Color.white);

        // Inputs compacts pour B1
        startLatitudeInput = CreateCompactInputField(parent, "Lat B1", defaultStartPosition.x.ToString("F6"));
        startLongitudeInput = CreateCompactInputField(parent, "Lon B1", defaultStartPosition.y.ToString("F6"));
        placeStartButton = CreateCompactButton(parent, "Placer B1", Color.green);

        // Inputs compacts pour B2
        finishLatitudeInput = CreateCompactInputField(parent, "Lat B2", defaultFinishPosition.x.ToString("F6"));
        finishLongitudeInput = CreateCompactInputField(parent, "Lon B2", defaultFinishPosition.y.ToString("F6"));
        placeFinishButton = CreateCompactButton(parent, "Placer B2", Color.red);

        // Boutons d'action compacts
        placeBothButton = CreateCompactButton(parent, "Placer Tout", Color.yellow);
        clearButton = CreateCompactButton(parent, "Effacer", Color.gray);

        // Status compact
        statusText = CreateLabel(parent, "Prêt", 10, Color.cyan);
    }

    /// <summary>
    /// Crée un label de texte
    /// </summary>
    private TextMeshProUGUI CreateLabel(GameObject parent, string text, int fontSize, Color color)
    {
        GameObject labelObject = new GameObject("Label");
        labelObject.transform.SetParent(parent.transform, false);

        TextMeshProUGUI label = labelObject.AddComponent<TextMeshProUGUI>();
        label.text = text;
        label.fontSize = fontSize;
        label.color = color;
        label.alignment = TextAlignmentOptions.Center;

        RectTransform rect = labelObject.GetComponent<RectTransform>();
        rect.sizeDelta = new Vector2(0, fontSize + 5);

        return label;
    }

    /// <summary>
    /// Crée un champ de saisie compact
    /// </summary>
    private TMP_InputField CreateCompactInputField(GameObject parent, string placeholder, string defaultValue)
    {
        GameObject inputObject = new GameObject($"Input_{placeholder}");
        inputObject.transform.SetParent(parent.transform, false);

        Image inputImage = inputObject.AddComponent<Image>();
        inputImage.color = new Color(0.2f, 0.2f, 0.2f, 0.8f);

        TMP_InputField inputField = inputObject.AddComponent<TMP_InputField>();

        // Créer le texte de saisie directement
        GameObject textObject = new GameObject("Text");
        textObject.transform.SetParent(inputObject.transform, false);
        TextMeshProUGUI inputText = textObject.AddComponent<TextMeshProUGUI>();
        inputText.fontSize = 10;
        inputText.color = Color.white;
        inputText.alignment = TextAlignmentOptions.Center;

        // Configurer les RectTransforms
        RectTransform textRect = textObject.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = new Vector2(5, 0);
        textRect.offsetMax = new Vector2(-5, 0);

        inputField.textComponent = inputText;
        inputField.text = defaultValue;

        RectTransform rect = inputObject.GetComponent<RectTransform>();
        rect.sizeDelta = new Vector2(0, 20);

        return inputField;
    }

    /// <summary>
    /// Crée un bouton compact
    /// </summary>
    private Button CreateCompactButton(GameObject parent, string text, Color color)
    {
        GameObject buttonObject = new GameObject($"Button_{text}");
        buttonObject.transform.SetParent(parent.transform, false);

        Image buttonImage = buttonObject.AddComponent<Image>();
        buttonImage.color = color * 0.8f;

        Button button = buttonObject.AddComponent<Button>();

        GameObject textObject = new GameObject("Text");
        textObject.transform.SetParent(buttonObject.transform, false);
        TextMeshProUGUI buttonText = textObject.AddComponent<TextMeshProUGUI>();
        buttonText.text = text;
        buttonText.fontSize = 10;
        buttonText.color = Color.white;
        buttonText.alignment = TextAlignmentOptions.Center;

        RectTransform textRect = textObject.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;

        RectTransform rect = buttonObject.GetComponent<RectTransform>();
        rect.sizeDelta = new Vector2(0, 22);

        return button;
    }



    /// <summary>
    /// Configure les événements des boutons
    /// </summary>
    private void SetupButtonEvents()
    {
        if (placeStartButton != null)
            placeStartButton.onClick.AddListener(PlaceStartBuoy);

        if (placeFinishButton != null)
            placeFinishButton.onClick.AddListener(PlaceFinishBuoy);

        if (placeBothButton != null)
            placeBothButton.onClick.AddListener(PlaceBothBuoys);

        if (clearButton != null)
            clearButton.onClick.AddListener(ClearBuoys);
    }

    /// <summary>
    /// Définit les valeurs par défaut
    /// </summary>
    private void SetDefaultValues()
    {
        if (startLatitudeInput != null)
            startLatitudeInput.text = defaultStartPosition.x.ToString("F6");

        if (startLongitudeInput != null)
            startLongitudeInput.text = defaultStartPosition.y.ToString("F6");

        if (finishLatitudeInput != null)
            finishLatitudeInput.text = defaultFinishPosition.x.ToString("F6");

        if (finishLongitudeInput != null)
            finishLongitudeInput.text = defaultFinishPosition.y.ToString("F6");
    }

    /// <summary>
    /// Place la bouée de départ
    /// </summary>
    private void PlaceStartBuoy()
    {
        if (TryParseCoordinates(startLatitudeInput.text, startLongitudeInput.text, out Vector2d position))
        {
            manualPlacer.SetManualStartPosition(position.x, position.y);
            manualPlacer.PlaceStartBuoyManually();
            UpdateStatus("✅ Bouée de départ placée");
        }
        else
        {
            UpdateStatus("❌ Coordonnées de départ invalides");
        }
    }

    /// <summary>
    /// Place la bouée d'arrivée
    /// </summary>
    private void PlaceFinishBuoy()
    {
        if (TryParseCoordinates(finishLatitudeInput.text, finishLongitudeInput.text, out Vector2d position))
        {
            manualPlacer.SetManualFinishPosition(position.x, position.y);
            manualPlacer.PlaceFinishBuoyManually();
            UpdateStatus("✅ Bouée d'arrivée placée");
        }
        else
        {
            UpdateStatus("❌ Coordonnées d'arrivée invalides");
        }
    }

    /// <summary>
    /// Place les deux bouées
    /// </summary>
    private void PlaceBothBuoys()
    {
        PlaceStartBuoy();
        PlaceFinishBuoy();
        UpdateStatus("✅ Toutes les bouées placées");
    }

    /// <summary>
    /// Efface toutes les bouées
    /// </summary>
    private void ClearBuoys()
    {
        manualPlacer.ClearManualBuoys();
        UpdateStatus("🗑️ Bouées effacées");
    }



    /// <summary>
    /// Essaie de parser les coordonnées GPS
    /// </summary>
    private bool TryParseCoordinates(string latText, string lonText, out Vector2d position)
    {
        position = Vector2d.zero;

        if (double.TryParse(latText, out double lat) && double.TryParse(lonText, out double lon))
        {
            // Vérifier que les coordonnées sont dans des plages valides
            if (lat >= -90 && lat <= 90 && lon >= -180 && lon <= 180)
            {
                position = new Vector2d(lat, lon);
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// Met à jour le texte de statut
    /// </summary>
    private void UpdateStatus(string message)
    {
        if (statusText != null)
        {
            statusText.text = message;
        }

        Debug.Log($"[BuoyPlacementUI] {message}");
    }

    /// <summary>
    /// Affiche l'interface utilisateur
    /// </summary>
    [ContextMenu("Show UI")]
    public void ShowUI()
    {
        if (uiPanel != null)
        {
            uiPanel.SetActive(true);
        }
    }

    /// <summary>
    /// Masque l'interface utilisateur
    /// </summary>
    [ContextMenu("Hide UI")]
    public void HideUI()
    {
        if (uiPanel != null)
        {
            uiPanel.SetActive(false);
        }
    }

    /// <summary>
    /// Bascule l'affichage de l'interface utilisateur
    /// </summary>
    [ContextMenu("Toggle UI")]
    public void ToggleUI()
    {
        if (uiPanel != null)
        {
            bool isActive = uiPanel.activeSelf;
            uiPanel.SetActive(!isActive);

            UpdateStatus(isActive ? "UI masquée" : "UI affichée");
        }
    }
}
