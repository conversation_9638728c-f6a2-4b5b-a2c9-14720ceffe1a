using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Interface simple pour contrôler la hauteur Y de tous les bateaux en temps réel
/// </summary>
public class HeightControlUI : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private bool showUI = false;
    [SerializeField] private KeyCode toggleUIKey = KeyCode.H;
    [SerializeField] private bool showDebugLogs = true;

    [Header("Contrôles Globaux")]
    [SerializeField] private KeyCode globalIncreaseKey = KeyCode.Plus;
    [SerializeField] private KeyCode globalDecreaseKey = KeyCode.Minus;
    [SerializeField] private float globalAdjustmentStep = 0.1f;

    // Variables d'interface
    private float globalWaterLevel = 0f;
    private float globalHeightOffset = 0.5f;
    private string statusMessage = "Contrôle de hauteur prêt";

    private void Update()
    {
        if (Input.GetKeyDown(toggleUIKey))
        {
            ToggleUI();
        }

        // Contrôles globaux
        if (Input.GetKeyDown(globalIncreaseKey))
        {
            AdjustAllBoatsHeight(globalAdjustmentStep);
        }
        else if (Input.GetKeyDown(globalDecreaseKey))
        {
            AdjustAllBoatsHeight(-globalAdjustmentStep);
        }
    }

    /// <summary>
    /// Interface utilisateur OnGUI
    /// </summary>
    private void OnGUI()
    {
        if (!showUI) return;

        // Style de base
        GUI.skin.box.normal.background = MakeTexture(2, 2, new Color(0.1f, 0.1f, 0.1f, 0.9f));
        GUI.skin.button.normal.background = MakeTexture(2, 2, new Color(0.3f, 0.3f, 0.3f, 0.8f));

        // Fenêtre principale compacte en bas à droite
        float windowWidth = 300;
        float windowHeight = 250;
        float windowX = Screen.width - windowWidth - 20;
        float windowY = Screen.height - windowHeight - 20;

        GUI.Box(new Rect(windowX, windowY, windowWidth, windowHeight), "");
        
        GUILayout.BeginArea(new Rect(windowX + 10, windowY + 10, windowWidth - 20, windowHeight - 20));
        
        // Titre
        GUILayout.Label("⚓ CONTRÔLE HAUTEUR BATEAUX", GUI.skin.label);
        GUILayout.Space(10);

        // Contrôles globaux
        GUILayout.Label("🌊 Contrôles Globaux", GUI.skin.label);
        
        GUILayout.BeginHorizontal();
        GUILayout.Label("Niveau eau:", GUILayout.Width(80));
        string waterLevelStr = GUILayout.TextField(globalWaterLevel.ToString("F2"), GUILayout.Width(60));
        if (float.TryParse(waterLevelStr, out float newWaterLevel))
        {
            globalWaterLevel = newWaterLevel;
        }
        if (GUILayout.Button("Appliquer", GUILayout.Width(60)))
        {
            SetAllBoatsWaterLevel(globalWaterLevel);
        }
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        GUILayout.Label("Hauteur:", GUILayout.Width(80));
        string heightStr = GUILayout.TextField(globalHeightOffset.ToString("F2"), GUILayout.Width(60));
        if (float.TryParse(heightStr, out float newHeight))
        {
            globalHeightOffset = newHeight;
        }
        if (GUILayout.Button("Appliquer", GUILayout.Width(60)))
        {
            SetAllBoatsHeight(globalHeightOffset);
        }
        GUILayout.EndHorizontal();

        GUILayout.Space(5);

        // Boutons d'ajustement rapide
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("↑ Monter Tous"))
        {
            AdjustAllBoatsHeight(globalAdjustmentStep);
        }
        if (GUILayout.Button("↓ Descendre Tous"))
        {
            AdjustAllBoatsHeight(-globalAdjustmentStep);
        }
        GUILayout.EndHorizontal();

        GUILayout.BeginHorizontal();
        if (GUILayout.Button("🌊 Niveau Eau"))
        {
            ResetAllBoatsToWaterLevel();
        }
        if (GUILayout.Button("📊 Infos"))
        {
            ShowAllBoatsHeightInfo();
        }
        GUILayout.EndHorizontal();

        GUILayout.Space(10);

        // Contrôles individuels
        GUILayout.Label("🚤 Contrôles Individuels", GUI.skin.label);
        
        var allBoats = FindObjectsOfType<UnifiedBoatGPS>();
        if (allBoats.Length > 0)
        {
            foreach (var boat in allBoats)
            {
                GUILayout.BeginHorizontal();
                GUILayout.Label(boat.gameObject.name, GUILayout.Width(100));
                
                if (GUILayout.Button("↑", GUILayout.Width(25)))
                {
                    boat.IncreaseHeight();
                }
                if (GUILayout.Button("↓", GUILayout.Width(25)))
                {
                    boat.DecreaseHeight();
                }
                if (GUILayout.Button("🌊", GUILayout.Width(25)))
                {
                    boat.ResetHeightToWaterLevel();
                }
                
                GUILayout.Label($"Y:{boat.GetCurrentYPosition():F2}", GUILayout.Width(50));
                GUILayout.EndHorizontal();
            }
        }
        else
        {
            GUILayout.Label("Aucun bateau avec UnifiedBoatGPS trouvé");
        }

        GUILayout.Space(10);

        // Status et instructions
        GUI.color = Color.cyan;
        GUILayout.Label($"Status: {statusMessage}", GUI.skin.label);
        GUILayout.Label($"Raccourcis: {globalIncreaseKey}//{globalDecreaseKey} = Ajuster tous", GUI.skin.label);
        GUILayout.Label($"Touche '{toggleUIKey}' pour masquer", GUI.skin.label);
        GUI.color = Color.white;

        GUILayout.EndArea();
    }

    /// <summary>
    /// Ajuste la hauteur de tous les bateaux
    /// </summary>
    private void AdjustAllBoatsHeight(float adjustment)
    {
        var allBoats = FindObjectsOfType<UnifiedBoatGPS>();
        int adjustedCount = 0;

        foreach (var boat in allBoats)
        {
            if (adjustment > 0)
                boat.IncreaseHeight();
            else
                boat.DecreaseHeight();
            adjustedCount++;
        }

        globalHeightOffset += adjustment;
        statusMessage = $"{adjustedCount} bateaux ajustés de {adjustment:F2}m";
        LogMessage(statusMessage);
    }

    /// <summary>
    /// Définit la hauteur de tous les bateaux
    /// </summary>
    private void SetAllBoatsHeight(float height)
    {
        var allBoats = FindObjectsOfType<UnifiedBoatGPS>();
        int setCount = 0;

        foreach (var boat in allBoats)
        {
            boat.SetCustomHeight(height);
            setCount++;
        }

        statusMessage = $"{setCount} bateaux définis à {height:F2}m";
        LogMessage(statusMessage);
    }

    /// <summary>
    /// Définit le niveau de l'eau pour tous les bateaux
    /// </summary>
    private void SetAllBoatsWaterLevel(float waterLevel)
    {
        var allBoats = FindObjectsOfType<UnifiedBoatGPS>();
        int setCount = 0;

        foreach (var boat in allBoats)
        {
            boat.SetWaterLevel(waterLevel);
            setCount++;
        }

        statusMessage = $"Niveau eau défini à {waterLevel:F2}m pour {setCount} bateaux";
        LogMessage(statusMessage);
    }

    /// <summary>
    /// Remet tous les bateaux au niveau de l'eau
    /// </summary>
    private void ResetAllBoatsToWaterLevel()
    {
        var allBoats = FindObjectsOfType<UnifiedBoatGPS>();
        int resetCount = 0;

        foreach (var boat in allBoats)
        {
            boat.ResetHeightToWaterLevel();
            resetCount++;
        }

        globalHeightOffset = 0f;
        statusMessage = $"{resetCount} bateaux remis au niveau de l'eau";
        LogMessage(statusMessage);
    }

    /// <summary>
    /// Affiche les informations de hauteur de tous les bateaux
    /// </summary>
    private void ShowAllBoatsHeightInfo()
    {
        var allBoats = FindObjectsOfType<UnifiedBoatGPS>();
        
        LogMessage("📊 === INFORMATIONS HAUTEUR BATEAUX ===");
        
        foreach (var boat in allBoats)
        {
            float currentY = boat.GetCurrentYPosition();
            LogMessage($"🚤 {boat.gameObject.name}: Y = {currentY:F2}m");
        }
        
        LogMessage($"🌊 Niveau eau global: {globalWaterLevel:F2}m");
        LogMessage($"⚓ Hauteur globale: {globalHeightOffset:F2}m");
        LogMessage("=== FIN INFORMATIONS ===");
        
        statusMessage = $"Infos affichées pour {allBoats.Length} bateaux";
    }

    /// <summary>
    /// Affiche/masque l'interface utilisateur
    /// </summary>
    [ContextMenu("Toggle UI")]
    public void ToggleUI()
    {
        showUI = !showUI;
        statusMessage = showUI ? "Interface affichée" : "Interface masquée";
        LogMessage(statusMessage);
    }

    /// <summary>
    /// Crée une texture de couleur unie
    /// </summary>
    private Texture2D MakeTexture(int width, int height, Color color)
    {
        Color[] pixels = new Color[width * height];
        for (int i = 0; i < pixels.Length; i++)
        {
            pixels[i] = color;
        }
        
        Texture2D texture = new Texture2D(width, height);
        texture.SetPixels(pixels);
        texture.Apply();
        return texture;
    }

    /// <summary>
    /// Affiche un message de log si les logs de debug sont activés
    /// </summary>
    private void LogMessage(string message)
    {
        if (showDebugLogs)
        {
            Debug.Log($"[HeightControlUI] {message}");
        }
    }
}
