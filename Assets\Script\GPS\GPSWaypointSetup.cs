using UnityEngine;

/// <summary>
/// Script d'installation automatique pour le système GPS-Waypoint
/// Configure automatiquement tous les composants nécessaires
/// </summary>
public class GPSWaypointSetup : MonoBehaviour
{
    [Header("Auto Setup")]
    [SerializeField] private bool setupOnStart = true;
    [SerializeField] private bool setupIntegratorAutomatically = true;
    [SerializeField] private bool setupDemoAutomatically = false;
    
    [Header("Configuration")]
    [SerializeField] private Transform fleetContainer;
    [SerializeField] private bool enableDebugLogs = true;
    [SerializeField] private bool enableGPSPathVisualization = true;
    
    [Header("Movement Settings")]
    [SerializeField] private float maxSpeed = 15f;
    [SerializeField] private float rotationSpeed = 2f;
    [SerializeField] private float pathFollowDelay = 2f;
    [SerializeField] private float inertiaFactor = 3f;
    [SerializeField] private int minGPSPoints = 3;
    [SerializeField] private float gpsAccuracy = 2f;
    [SerializeField] private float lateralOffset = 2f;

    private void Start()
    {
        if (setupOnStart)
        {
            SetupGPSWaypointSystem();
        }
    }

    /// <summary>
    /// Configure automatiquement tout le système GPS-Waypoint
    /// </summary>
    [ContextMenu("Setup GPS Waypoint System")]
    public void SetupGPSWaypointSystem()
    {
        Debug.Log("[GPSWaypointSetup] === CONFIGURATION DU SYSTÈME GPS-WAYPOINT ===");

        // 1. Configurer l'intégrateur
        if (setupIntegratorAutomatically)
        {
            SetupIntegrator();
        }

        // 2. Configurer la démonstration si demandé
        if (setupDemoAutomatically)
        {
            SetupDemo();
        }

        // 3. Vérifier la configuration
        VerifySetup();

        Debug.Log("[GPSWaypointSetup] === CONFIGURATION TERMINÉE ===");
    }

    /// <summary>
    /// Configure l'intégrateur GPS-Waypoint
    /// </summary>
    private void SetupIntegrator()
    {
        Debug.Log("[GPSWaypointSetup] Configuration de l'intégrateur...");

        // Chercher un intégrateur existant
        var existingIntegrator = FindObjectOfType<GPSWaypointIntegrator>();
        
        if (existingIntegrator == null)
        {
            // Créer un nouveau GameObject pour l'intégrateur
            GameObject integratorObject = new GameObject("GPS Waypoint Integrator");
            existingIntegrator = integratorObject.AddComponent<GPSWaypointIntegrator>();
            Debug.Log("[GPSWaypointSetup] Intégrateur créé");
        }
        else
        {
            Debug.Log("[GPSWaypointSetup] Intégrateur existant trouvé");
        }

        // Configurer l'intégrateur via réflexion
        ConfigureIntegrator(existingIntegrator);

        // Initialiser l'intégration
        existingIntegrator.InitializeIntegration();
    }

    /// <summary>
    /// Configure les paramètres de l'intégrateur
    /// </summary>
    private void ConfigureIntegrator(GPSWaypointIntegrator integrator)
    {
        var type = typeof(GPSWaypointIntegrator);

        // Configurer les champs via réflexion
        SetPrivateField(integrator, "enableWaypointMovement", true);
        SetPrivateField(integrator, "autoDetectBoats", true);
        SetPrivateField(integrator, "fleetContainer", fleetContainer);
        SetPrivateField(integrator, "maxSpeed", maxSpeed);
        SetPrivateField(integrator, "rotationSpeed", rotationSpeed);
        SetPrivateField(integrator, "pathFollowDelay", pathFollowDelay);
        SetPrivateField(integrator, "inertiaFactor", inertiaFactor);
        SetPrivateField(integrator, "minGPSPoints", minGPSPoints);
        SetPrivateField(integrator, "gpsAccuracy", gpsAccuracy);
        SetPrivateField(integrator, "lateralOffset", lateralOffset);
        SetPrivateField(integrator, "showDebugInfo", enableDebugLogs);
        SetPrivateField(integrator, "showGPSPath", enableGPSPathVisualization);

        Debug.Log("[GPSWaypointSetup] Intégrateur configuré avec les paramètres personnalisés");
    }

    /// <summary>
    /// Configure la démonstration
    /// </summary>
    private void SetupDemo()
    {
        Debug.Log("[GPSWaypointSetup] Configuration de la démonstration...");

        var existingDemo = FindObjectOfType<GPSWaypointDemo>();
        
        if (existingDemo == null)
        {
            GameObject demoObject = new GameObject("GPS Waypoint Demo");
            existingDemo = demoObject.AddComponent<GPSWaypointDemo>();
            Debug.Log("[GPSWaypointSetup] Démonstration créée");
        }
        else
        {
            Debug.Log("[GPSWaypointSetup] Démonstration existante trouvée");
        }

        // Configurer la démo
        SetPrivateField(existingDemo, "enableDemo", true);
        SetPrivateField(existingDemo, "autoStartDemo", false);
        SetPrivateField(existingDemo, "autoDetectBoats", true);
    }

    /// <summary>
    /// Vérifie que la configuration est correcte
    /// </summary>
    private void VerifySetup()
    {
        Debug.Log("[GPSWaypointSetup] Vérification de la configuration...");

        // Vérifier l'intégrateur
        var integrator = FindObjectOfType<GPSWaypointIntegrator>();
        if (integrator == null)
        {
            Debug.LogError("[GPSWaypointSetup] ❌ Aucun intégrateur trouvé !");
            return;
        }

        // Vérifier les bateaux avec UnifiedBoatGPS
        var gpsBoats = FindObjectsOfType<UnifiedBoatGPS>();
        Debug.Log($"[GPSWaypointSetup] ✓ {gpsBoats.Length} bateaux avec UnifiedBoatGPS trouvés");

        // Vérifier les bateaux avec BoatWaypointMover
        var waypointBoats = FindObjectsOfType<BoatWaypointMover>();
        Debug.Log($"[GPSWaypointSetup] ✓ {waypointBoats.Length} bateaux avec BoatWaypointMover trouvés");

        // Vérifier les adaptateurs
        var adapters = FindObjectsOfType<GPSToWaypointAdapter>();
        Debug.Log($"[GPSWaypointSetup] ✓ {adapters.Length} adaptateurs GPS trouvés");

        // Vérifier la carte Mapbox
        var map = FindObjectOfType<Mapbox.Unity.Map.AbstractMap>();
        if (map == null)
        {
            Debug.LogWarning("[GPSWaypointSetup] ⚠ Aucune carte Mapbox trouvée - nécessaire pour la conversion GPS");
        }
        else
        {
            Debug.Log("[GPSWaypointSetup] ✓ Carte Mapbox trouvée");
        }

        // Vérifier le tracker Meshtastic
        var tracker = FindObjectOfType<MeshtasticGPSTracker>();
        if (tracker == null)
        {
            Debug.LogWarning("[GPSWaypointSetup] ⚠ Aucun MeshtasticGPSTracker trouvé");
        }
        else
        {
            Debug.Log("[GPSWaypointSetup] ✓ MeshtasticGPSTracker trouvé");
        }

        Debug.Log("[GPSWaypointSetup] === RÉSUMÉ DE LA CONFIGURATION ===");
        Debug.Log($"- Intégrateur: {(integrator != null ? "✓" : "❌")}");
        Debug.Log($"- Bateaux GPS: {gpsBoats.Length}");
        Debug.Log($"- Bateaux Waypoint: {waypointBoats.Length}");
        Debug.Log($"- Adaptateurs: {adapters.Length}");
        Debug.Log($"- Carte Mapbox: {(map != null ? "✓" : "⚠")}");
        Debug.Log($"- Tracker Meshtastic: {(tracker != null ? "✓" : "⚠")}");
    }

    /// <summary>
    /// Utilise la réflexion pour définir un champ privé
    /// </summary>
    private void SetPrivateField(object obj, string fieldName, object value)
    {
        var field = obj.GetType().GetField(fieldName, 
            System.Reflection.BindingFlags.NonPublic | 
            System.Reflection.BindingFlags.Instance);
        
        if (field != null)
        {
            field.SetValue(obj, value);
        }
        else
        {
            Debug.LogWarning($"[GPSWaypointSetup] Champ '{fieldName}' non trouvé dans {obj.GetType().Name}");
        }
    }

    /// <summary>
    /// Nettoie et reconfigure tout le système
    /// </summary>
    [ContextMenu("Clean and Reconfigure")]
    public void CleanAndReconfigure()
    {
        Debug.Log("[GPSWaypointSetup] === NETTOYAGE ET RECONFIGURATION ===");

        // Supprimer les anciens composants
        var oldIntegrators = FindObjectsOfType<GPSWaypointIntegrator>();
        foreach (var integrator in oldIntegrators)
        {
            if (integrator.gameObject != gameObject)
            {
                DestroyImmediate(integrator.gameObject);
            }
        }

        var oldDemos = FindObjectsOfType<GPSWaypointDemo>();
        foreach (var demo in oldDemos)
        {
            if (demo.gameObject != gameObject)
            {
                DestroyImmediate(demo.gameObject);
            }
        }

        // Reconfigurer
        SetupGPSWaypointSystem();
    }

    /// <summary>
    /// Active/désactive le mouvement par waypoints
    /// </summary>
    [ContextMenu("Toggle Waypoint Movement")]
    public void ToggleWaypointMovement()
    {
        var integrator = FindObjectOfType<GPSWaypointIntegrator>();
        if (integrator != null)
        {
            integrator.ToggleWaypointMovement();
        }
    }

    /// <summary>
    /// Affiche les statistiques du système
    /// </summary>
    [ContextMenu("Show System Stats")]
    public void ShowSystemStats()
    {
        var integrator = FindObjectOfType<GPSWaypointIntegrator>();
        if (integrator != null)
        {
            integrator.LogIntegrationStats();
        }
        
        VerifySetup();
    }
}
