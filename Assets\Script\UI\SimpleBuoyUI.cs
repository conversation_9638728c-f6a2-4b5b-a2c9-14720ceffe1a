using UnityEngine;
using Mapbox.Utils;

/// <summary>
/// Interface utilisateur simple pour le placement manuel des bouées
/// Utilise OnGUI pour éviter les problèmes de chevauchement
/// </summary>
public class SimpleBuoyUI : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private bool showUI = false;
    [SerializeField] private KeyCode toggleUIKey = KeyCode.B;
    [SerializeField] private bool showDebugLogs = true;
    
    [Header("Positions par défaut")]
    [SerializeField] private Vector2d defaultStartPosition = new Vector2d(43.123456, 5.654321);
    [SerializeField] private Vector2d defaultFinishPosition = new Vector2d(43.124456, 5.655321);

    // Variables d'interface
    private string startLatText = "";
    private string startLonText = "";
    private string finishLatText = "";
    private string finishLonText = "";
    private string statusMessage = "Prêt";
    private Color statusColor = Color.white;

    // Références
    private ManualBuoyPlacer manualPlacer;

    private void Start()
    {
        InitializeUI();
    }

    private void Update()
    {
        if (Input.GetKeyDown(toggleUIKey))
        {
            ToggleUI();
        }
    }

    /// <summary>
    /// Initialise l'interface utilisateur
    /// </summary>
    private void InitializeUI()
    {
        // Trouver ou créer le ManualBuoyPlacer
        manualPlacer = FindObjectOfType<ManualBuoyPlacer>();
        if (manualPlacer == null)
        {
            GameObject placerObject = new GameObject("Manual Buoy Placer");
            manualPlacer = placerObject.AddComponent<ManualBuoyPlacer>();
            LogMessage("ManualBuoyPlacer créé automatiquement");
        }

        // Initialiser les valeurs par défaut
        startLatText = defaultStartPosition.x.ToString("F6");
        startLonText = defaultStartPosition.y.ToString("F6");
        finishLatText = defaultFinishPosition.x.ToString("F6");
        finishLonText = defaultFinishPosition.y.ToString("F6");

        LogMessage("Interface utilisateur simple initialisée");
    }

    /// <summary>
    /// Interface utilisateur OnGUI
    /// </summary>
    private void OnGUI()
    {
        if (!showUI) return;

        // Style de base
        GUI.skin.box.normal.background = MakeTexture(2, 2, new Color(0.1f, 0.1f, 0.1f, 0.9f));
        GUI.skin.button.normal.background = MakeTexture(2, 2, new Color(0.3f, 0.3f, 0.3f, 0.8f));
        GUI.skin.textField.normal.background = MakeTexture(2, 2, new Color(0.2f, 0.2f, 0.2f, 0.8f));

        // Fenêtre principale compacte en haut à droite
        float windowWidth = 280;
        float windowHeight = 320;
        float windowX = Screen.width - windowWidth - 20;
        float windowY = 20;

        GUI.Box(new Rect(windowX, windowY, windowWidth, windowHeight), "");
        
        GUILayout.BeginArea(new Rect(windowX + 10, windowY + 10, windowWidth - 20, windowHeight - 20));
        
        // Titre
        GUILayout.Label("🎯 PLACEMENT MANUEL DES BOUÉES", GUI.skin.label);
        GUILayout.Space(10);

        // Section Bouée de Départ
        GUI.color = Color.green;
        GUILayout.Label("📍 Bouée de Départ (B1)", GUI.skin.label);
        GUI.color = Color.white;
        
        GUILayout.BeginHorizontal();
        GUILayout.Label("Lat:", GUILayout.Width(30));
        startLatText = GUILayout.TextField(startLatText, GUILayout.Width(100));
        GUILayout.Label("Lon:", GUILayout.Width(30));
        startLonText = GUILayout.TextField(startLonText, GUILayout.Width(100));
        GUILayout.EndHorizontal();
        
        GUI.color = Color.green;
        if (GUILayout.Button("Placer B1"))
        {
            PlaceStartBuoy();
        }
        GUI.color = Color.white;
        
        GUILayout.Space(10);

        // Section Bouée d'Arrivée
        GUI.color = Color.red;
        GUILayout.Label("🏁 Bouée d'Arrivée (B2)", GUI.skin.label);
        GUI.color = Color.white;
        
        GUILayout.BeginHorizontal();
        GUILayout.Label("Lat:", GUILayout.Width(30));
        finishLatText = GUILayout.TextField(finishLatText, GUILayout.Width(100));
        GUILayout.Label("Lon:", GUILayout.Width(30));
        finishLonText = GUILayout.TextField(finishLonText, GUILayout.Width(100));
        GUILayout.EndHorizontal();
        
        GUI.color = Color.red;
        if (GUILayout.Button("Placer B2"))
        {
            PlaceFinishBuoy();
        }
        GUI.color = Color.white;
        
        GUILayout.Space(10);

        // Boutons d'action
        GUILayout.BeginHorizontal();
        GUI.color = Color.yellow;
        if (GUILayout.Button("Placer Tout"))
        {
            PlaceBothBuoys();
        }
        GUI.color = Color.gray;
        if (GUILayout.Button("Effacer"))
        {
            ClearBuoys();
        }
        GUI.color = Color.white;
        GUILayout.EndHorizontal();
        
        GUILayout.Space(10);

        // Boutons utilitaires
        GUILayout.BeginHorizontal();
        if (GUILayout.Button("Défaut"))
        {
            SetDefaultValues();
        }
        if (GUILayout.Button("Info"))
        {
            ShowInfo();
        }
        GUILayout.EndHorizontal();

        GUILayout.Space(10);

        // Status
        GUI.color = statusColor;
        GUILayout.Label($"Status: {statusMessage}", GUI.skin.label);
        GUI.color = Color.white;

        // Instructions
        GUILayout.Space(5);
        GUI.color = Color.cyan;
        GUILayout.Label($"Touche '{toggleUIKey}' pour masquer", GUI.skin.label);
        GUI.color = Color.white;

        GUILayout.EndArea();
    }

    /// <summary>
    /// Place la bouée de départ
    /// </summary>
    private void PlaceStartBuoy()
    {
        if (TryParseCoordinates(startLatText, startLonText, out Vector2d position))
        {
            manualPlacer.SetManualStartPosition(position.x, position.y);
            manualPlacer.PlaceStartBuoyManually();
            UpdateStatus("✅ Bouée de départ placée", Color.green);
        }
        else
        {
            UpdateStatus("❌ Coordonnées de départ invalides", Color.red);
        }
    }

    /// <summary>
    /// Place la bouée d'arrivée
    /// </summary>
    private void PlaceFinishBuoy()
    {
        if (TryParseCoordinates(finishLatText, finishLonText, out Vector2d position))
        {
            manualPlacer.SetManualFinishPosition(position.x, position.y);
            manualPlacer.PlaceFinishBuoyManually();
            UpdateStatus("✅ Bouée d'arrivée placée", Color.green);
        }
        else
        {
            UpdateStatus("❌ Coordonnées d'arrivée invalides", Color.red);
        }
    }

    /// <summary>
    /// Place les deux bouées
    /// </summary>
    private void PlaceBothBuoys()
    {
        bool startOk = TryParseCoordinates(startLatText, startLonText, out Vector2d startPos);
        bool finishOk = TryParseCoordinates(finishLatText, finishLonText, out Vector2d finishPos);

        if (startOk && finishOk)
        {
            manualPlacer.SetManualStartPosition(startPos.x, startPos.y);
            manualPlacer.SetManualFinishPosition(finishPos.x, finishPos.y);
            manualPlacer.PlaceBothBuoys();
            UpdateStatus("✅ Toutes les bouées placées", Color.green);
        }
        else
        {
            UpdateStatus("❌ Vérifiez les coordonnées", Color.red);
        }
    }

    /// <summary>
    /// Efface toutes les bouées
    /// </summary>
    private void ClearBuoys()
    {
        manualPlacer.ClearManualBuoys();
        UpdateStatus("🗑️ Bouées effacées", Color.yellow);
    }

    /// <summary>
    /// Remet les valeurs par défaut
    /// </summary>
    private void SetDefaultValues()
    {
        startLatText = defaultStartPosition.x.ToString("F6");
        startLonText = defaultStartPosition.y.ToString("F6");
        finishLatText = defaultFinishPosition.x.ToString("F6");
        finishLonText = defaultFinishPosition.y.ToString("F6");
        UpdateStatus("🔄 Valeurs par défaut restaurées", Color.cyan);
    }

    /// <summary>
    /// Affiche les informations
    /// </summary>
    private void ShowInfo()
    {
        if (manualPlacer != null)
        {
            manualPlacer.ShowDebugInfo();
        }
        UpdateStatus("📊 Infos affichées dans la console", Color.cyan);
    }

    /// <summary>
    /// Essaie de parser les coordonnées GPS
    /// </summary>
    private bool TryParseCoordinates(string latText, string lonText, out Vector2d position)
    {
        position = Vector2d.zero;
        
        if (double.TryParse(latText, out double lat) && double.TryParse(lonText, out double lon))
        {
            // Vérifier que les coordonnées sont dans des plages valides
            if (lat >= -90 && lat <= 90 && lon >= -180 && lon <= 180)
            {
                position = new Vector2d(lat, lon);
                return true;
            }
        }
        
        return false;
    }

    /// <summary>
    /// Met à jour le message de statut
    /// </summary>
    private void UpdateStatus(string message, Color color)
    {
        statusMessage = message;
        statusColor = color;
        LogMessage(message);
    }

    /// <summary>
    /// Affiche/masque l'interface utilisateur
    /// </summary>
    [ContextMenu("Toggle UI")]
    public void ToggleUI()
    {
        showUI = !showUI;
        UpdateStatus(showUI ? "Interface affichée" : "Interface masquée", Color.cyan);
    }

    /// <summary>
    /// Affiche l'interface utilisateur
    /// </summary>
    [ContextMenu("Show UI")]
    public void ShowUI()
    {
        showUI = true;
        UpdateStatus("Interface affichée", Color.cyan);
    }

    /// <summary>
    /// Masque l'interface utilisateur
    /// </summary>
    [ContextMenu("Hide UI")]
    public void HideUI()
    {
        showUI = false;
        UpdateStatus("Interface masquée", Color.cyan);
    }

    /// <summary>
    /// Crée une texture de couleur unie
    /// </summary>
    private Texture2D MakeTexture(int width, int height, Color color)
    {
        Color[] pixels = new Color[width * height];
        for (int i = 0; i < pixels.Length; i++)
        {
            pixels[i] = color;
        }
        
        Texture2D texture = new Texture2D(width, height);
        texture.SetPixels(pixels);
        texture.Apply();
        return texture;
    }

    /// <summary>
    /// Affiche un message de log si les logs de debug sont activés
    /// </summary>
    private void LogMessage(string message)
    {
        if (showDebugLogs)
        {
            Debug.Log($"[SimpleBuoyUI] {message}");
        }
    }
}
