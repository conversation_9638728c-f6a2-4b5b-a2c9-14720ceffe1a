using UnityEngine;
using Mapbox.Unity.Map;
using Mapbox.Utils;

#if UNITY_EDITOR
using UnityEditor;
#endif

public class StartBuoyPosition : MonoBehaviour
{
    [Header("GPS Coordinates")]
    [SerializeField] private double latitude = 49.03032;
    [SerializeField] private double longitude = -1.61893;

    private AbstractMap _map;

#if UNITY_EDITOR
    private void OnValidate()
    {
        if (_map == null)
            _map = FindFirstObjectByType<AbstractMap>();

        if (_map != null && _map.IsEditorPreviewEnabled)
        {
            transform.position = _map.GeoToWorldPosition(new Vector2d(latitude, longitude));
        }
    }

    private void OnDrawGizmos()
    {
        // Mettre � jour les coordonn�es si la position change
        if (_map != null && _map.IsEditorPreviewEnabled)
        {
            Vector3 currentPos = transform.position;
            Vector2d newCoords = _map.WorldToGeoPosition(currentPos);

            if (latitude != newCoords.x || longitude != newCoords.y)
            {
                Undo.RecordObject(this, "Update GPS Coordinates");
                latitude = newCoords.x;
                longitude = newCoords.y;
            }
        }

        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, 1f);
    }
#endif

    // Le reste du code pour le runtime
    void Start()
    {
        _map = FindFirstObjectByType<AbstractMap>();
        if (_map != null)
        {
            _map.OnInitialized += OnMapInitialized;
        }
    }

    private void OnMapInitialized()
    {
        transform.position = _map.GeoToWorldPosition(new Vector2d(latitude, longitude));
    }

    public Vector2d GetCoordinates()
    {
        return new Vector2d(latitude, longitude);
    }
}