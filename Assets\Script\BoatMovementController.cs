using UnityEngine;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Classe responsable de la gestion du mouvement fluide des bateaux
/// </summary>
public class BoatMovementController
{
    // Constantes pour le buffer et les waypoints
    private const int POSITION_BUFFER_SIZE = 5;
    private const int MIN_WAYPOINTS = 20;
    private const float MIN_WAYPOINT_DISTANCE = 0.5f;
    private const float WAYPOINT_REACHED_DISTANCE = 0.2f;

    // Buffers et listes
    private readonly Queue<PositionData> gpsBuffer = new(POSITION_BUFFER_SIZE);
    private readonly List<WaypointData> waypoints = new();

    // Variables d'état
    private int currentWaypointIndex = 0;
    private float lastUpdateTime = 0f;
    private Vector3 targetPosition;
    private bool hasValidPath = false;
    private float lastCalculationTime = 0f;

    // Paramètres configurables
    private readonly float maxSpeed;
    private readonly float turningSpeed;

    // Propriétés publiques
    public float CurrentSpeed { get; private set; }
    public float PathFollowDelay { get; set; }
    public float InertiaFactor { get; set; }
    public float GPSAccuracy { get; set; }
    public float PositionSmoothingFactor { get; set; }
    public float MaxValidTeleportDistance { get; set; }
    public float TeleportTransitionDuration { get; set; }
    public float MinHeadingSmoothTime { get; set; }
    public float MaxHeadingSmoothTime { get; set; }

    /// <summary>
    /// Structure pour stocker les données de position GPS
    /// </summary>
    private struct PositionData
    {
        public Vector3 position;
        public float heading;
        public float speed;
        public float timestamp;

        public PositionData(Vector3 pos, float head, float spd, float time)
        {
            position = pos;
            heading = head;
            speed = spd;
            timestamp = time;
        }
    }

    /// <summary>
    /// Structure pour stocker les données de waypoint
    /// </summary>
    private struct WaypointData
    {
        public Vector3 position;
        public float speed;
        public float arrivalTime;

        public WaypointData(Vector3 pos, float spd, float time)
        {
            position = pos;
            speed = spd;
            arrivalTime = time;
        }
    }

    /// <summary>
    /// Constructeur
    /// </summary>
    public BoatMovementController(float followDelay, float maxSpd, float turnSpd, float inertia)
    {
        PathFollowDelay = followDelay;
        maxSpeed = maxSpd;
        turningSpeed = turnSpd;
        InertiaFactor = inertia;
        CurrentSpeed = 0f;
        targetPosition = Vector3.zero;
    }

    /// <summary>
    /// Ajoute une nouvelle position GPS au buffer et recalcule le chemin
    /// </summary>
    public void AddNewPosition(Vector3 position, float heading, float speed, float timestamp)
    {
        // Vérifier si la position est significativement différente de la dernière
        bool isSignificantMovement = IsSignificantMovement(position);

        // Ajouter la nouvelle position au buffer
        if (gpsBuffer.Count >= POSITION_BUFFER_SIZE)
        {
            gpsBuffer.Dequeue();
        }

        // Limiter la vitesse à une valeur raisonnable
        float clampedSpeed = Mathf.Clamp(speed, 0f, maxSpeed);

        // Ajouter au buffer
        gpsBuffer.Enqueue(new PositionData(position, heading, clampedSpeed, timestamp));

        // Recalculer le path uniquement si on a assez de points et si le mouvement est significatif
        // ou si ça fait longtemps qu'on n'a pas recalculé
        bool shouldRecalculate = (gpsBuffer.Count >= 3 && isSignificantMovement) ||
                                (Time.time - lastCalculationTime > 5.0f);

        if (shouldRecalculate)
        {
            RecalculatePath();
            lastCalculationTime = Time.time;
        }

        // Mettre à jour le temps de la dernière mise à jour
        lastUpdateTime = timestamp;
    }

    /// <summary>
    /// Vérifie si le mouvement est significatif par rapport à la dernière position
    /// </summary>
    private bool IsSignificantMovement(Vector3 newPosition)
    {
        if (gpsBuffer.Count == 0) return true;

        var lastPosition = gpsBuffer.Last().position;
        float distance = Vector3.Distance(lastPosition, newPosition);

        // Considérer comme significatif si la distance est supérieure à la précision GPS
        return distance > GPSAccuracy;
    }

    /// <summary>
    /// Recalcule le chemin à partir des points GPS stockés
    /// </summary>
    private void RecalculatePath()
    {
        // Obtenir les points du buffer
        var points = gpsBuffer.ToArray();

        // Vider la liste des waypoints
        waypoints.Clear();

        // Si nous n'avons pas assez de points, utiliser une interpolation linéaire simple
        if (points.Length < 3)
        {
            // Ajouter directement les points comme waypoints
            for (int i = 0; i < points.Length; i++)
            {
                waypoints.Add(new WaypointData(
                    points[i].position,
                    points[i].speed,
                    points[i].timestamp + PathFollowDelay
                ));
            }
        }
        else
        {
            // Calculer la longueur totale du chemin pour estimer les temps d'arrivée
            float totalPathLength = 0f;
            for (int i = 1; i < points.Length; i++)
            {
                totalPathLength += Vector3.Distance(points[i-1].position, points[i].position);
            }

            // Calculer la vitesse moyenne
            float averageSpeed = 0f;
            for (int i = 0; i < points.Length; i++)
            {
                averageSpeed += points[i].speed;
            }
            averageSpeed /= points.Length;

            // Si la vitesse moyenne est trop faible, utiliser une valeur minimale
            if (averageSpeed < 0.5f) averageSpeed = 2.0f;

            // Créer une courbe de Bézier à partir des points GPS
            // Générer plus de points pour les courbes plus complexes
            int numPoints = Mathf.Max(MIN_WAYPOINTS, points.Length * 5);

            // Calculer les waypoints le long de la courbe
            List<Vector3> splinePoints = new List<Vector3>();

            // Générer les points de la spline
            for (int i = 0; i < numPoints; i++)
            {
                float t = i / (float)(numPoints - 1);
                Vector3 splinePoint = CalculateSplinePoint(points, t);
                splinePoints.Add(splinePoint);
            }

            // Filtrer les points trop proches
            List<Vector3> filteredPoints = new List<Vector3>();
            filteredPoints.Add(splinePoints[0]);

            for (int i = 1; i < splinePoints.Count; i++)
            {
                if (Vector3.Distance(filteredPoints[filteredPoints.Count - 1], splinePoints[i]) >= MIN_WAYPOINT_DISTANCE)
                {
                    filteredPoints.Add(splinePoints[i]);
                }
            }

            // Assurer qu'on a au moins le dernier point
            if (filteredPoints[filteredPoints.Count - 1] != splinePoints[splinePoints.Count - 1])
            {
                filteredPoints.Add(splinePoints[splinePoints.Count - 1]);
            }

            // Calculer la longueur totale du chemin filtré
            float filteredPathLength = 0f;
            for (int i = 1; i < filteredPoints.Count; i++)
            {
                filteredPathLength += Vector3.Distance(filteredPoints[i-1], filteredPoints[i]);
            }

            // Calculer le temps estimé pour parcourir le chemin
            float estimatedTime = filteredPathLength / averageSpeed;

            // Créer les waypoints avec des temps d'arrivée estimés
            float startTime = Time.time + PathFollowDelay;
            float timeIncrement = estimatedTime / filteredPoints.Count;

            for (int i = 0; i < filteredPoints.Count; i++)
            {
                float waypointSpeed = averageSpeed;

                // Ajuster la vitesse en fonction de la courbure
                if (i > 0 && i < filteredPoints.Count - 1)
                {
                    Vector3 prev = filteredPoints[i-1];
                    Vector3 curr = filteredPoints[i];
                    Vector3 next = filteredPoints[i+1];

                    Vector3 dir1 = (curr - prev).normalized;
                    Vector3 dir2 = (next - curr).normalized;

                    // Calculer l'angle entre les directions
                    float angle = Vector3.Angle(dir1, dir2);

                    // Réduire la vitesse dans les virages
                    float speedFactor = Mathf.Lerp(1.0f, 0.5f, angle / 90.0f);
                    waypointSpeed *= speedFactor;
                }

                // Ajouter le waypoint
                waypoints.Add(new WaypointData(
                    filteredPoints[i],
                    waypointSpeed,
                    startTime + (i * timeIncrement)
                ));
            }
        }

        // Réinitialiser l'index du waypoint actuel
        currentWaypointIndex = 0;
        hasValidPath = waypoints.Count > 0;

        if (hasValidPath)
        {
            targetPosition = waypoints[currentWaypointIndex].position;
        }
    }

    /// <summary>
    /// Calcule un point sur la courbe de Bézier
    /// </summary>
    private Vector3 CalculateSplinePoint(PositionData[] points, float t)
    {
        if (points.Length < 3) return points[0].position;

        // Utiliser une courbe de Catmull-Rom
        int p1, p2;
        float localT;

        if (points.Length == 3)
        {
            // Cas spécial pour 3 points
            if (t < 0.5f)
            {
                p1 = 0;
                p2 = 1;
                localT = t * 2f;
            }
            else
            {
                p1 = 1;
                p2 = 2;
                localT = (t - 0.5f) * 2f;
            }

            // Interpolation linéaire simple
            return Vector3.Lerp(points[p1].position, points[p2].position, localT);
        }

        // Pour 4 points ou plus, utiliser Catmull-Rom
        int numSegments = points.Length - 3;
        float segmentT = 1f / numSegments;

        int segment = Mathf.Min(Mathf.FloorToInt(t / segmentT), numSegments - 1);
        localT = (t - segment * segmentT) / segmentT;

        Vector3 p0 = points[segment].position;
        Vector3 p1p = points[segment + 1].position;
        Vector3 p2p = points[segment + 2].position;
        Vector3 p3 = points[segment + 3].position;

        float lt2 = localT * localT;
        float lt3 = lt2 * localT;

        Vector3 point = 0.5f * (
            (-lt3 + 2*lt2 - localT) * p0 +
            (3*lt3 - 5*lt2 + 2) * p1p +
            (-3*lt3 + 4*lt2 + localT) * p2p +
            (lt3 - lt2) * p3
        );

        return point;
    }

    /// <summary>
    /// Met à jour la cible actuelle en fonction du temps écoulé
    /// </summary>
    public void UpdateTarget(float currentTime)
    {
        if (!hasValidPath || waypoints.Count == 0) return;

        // Calculer le temps écoulé depuis la dernière mise à jour GPS
        float timeSinceLastUpdate = currentTime - lastUpdateTime;

        // Si nous avons dépassé le délai de suivi (adapté pour GPS lent), rester sur le dernier point
        // Avec des données GPS toutes les 8-12s, on attend jusqu'à 20s avant de s'arrêter
        if (timeSinceLastUpdate > Mathf.Max(PathFollowDelay * 1.5f, 20f))
        {
            // Si nous avons des points, rester sur le dernier
            if (waypoints.Count > 0)
            {
                targetPosition = waypoints[^1].position;
                currentWaypointIndex = waypoints.Count - 1;
            }
            return;
        }

        // Sinon, suivre le chemin normalement
        if (currentWaypointIndex < waypoints.Count)
        {
            targetPosition = waypoints[currentWaypointIndex].position;
        }
    }

    /// <summary>
    /// Met à jour le mouvement du bateau en fonction de la cible actuelle
    /// </summary>
    public void UpdateMovement(Transform transform, float deltaTime)
    {
        if (!hasValidPath || waypoints.Count == 0) return;

        // Obtenir le waypoint actuel
        Vector3 currentTarget = targetPosition;
        Vector3 direction = (currentTarget - transform.position).normalized;
        float distance = Vector3.Distance(transform.position, currentTarget);

        // Si la distance est très faible, considérer que nous sommes arrivés
        if (distance < WAYPOINT_REACHED_DISTANCE)
        {
            // Passer au waypoint suivant si possible
            if (currentWaypointIndex < waypoints.Count - 1)
            {
                currentWaypointIndex++;
                targetPosition = waypoints[currentWaypointIndex].position;
            }
            else if (gpsBuffer.Count > 0)
            {
                // Si nous sommes au dernier waypoint, rester immobile
                CurrentSpeed = 0f;
                return;
            }
        }
        else
        {
            // Calculer la rotation avec un lissage basé sur la vitesse
            if (direction != Vector3.zero)
            {
                // Calculer l'angle cible
                Quaternion targetRotation = Quaternion.LookRotation(direction);

                // Ajuster la vitesse de rotation en fonction de la vitesse actuelle
                float speedRatio = Mathf.Clamp01(CurrentSpeed / maxSpeed);
                float currentTurnSpeed = Mathf.Lerp(turningSpeed * 0.5f, turningSpeed, speedRatio);

                // Appliquer la rotation avec lissage
                transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, currentTurnSpeed * deltaTime);
            }

            // Ajuster la vitesse en fonction de la distance et de l'inertie
            float targetSpeed;

            // Si nous sommes proches du dernier waypoint, ralentir progressivement
            if (currentWaypointIndex == waypoints.Count - 1)
            {
                targetSpeed = Mathf.Min(distance / PathFollowDelay, maxSpeed) * 0.8f;
            }
            else
            {
                // Utiliser la vitesse du waypoint actuel
                targetSpeed = waypoints[currentWaypointIndex].speed;
            }

            // Appliquer l'inertie pour un mouvement plus fluide
            CurrentSpeed = Mathf.Lerp(CurrentSpeed, targetSpeed, InertiaFactor * deltaTime);

            // Déplacer vers le waypoint
            float step = CurrentSpeed * deltaTime;
            transform.position = Vector3.MoveTowards(transform.position, currentTarget, step);
        }
    }

    /// <summary>
    /// Téléporte immédiatement le bateau à une position donnée
    /// </summary>
    public void TeleportImmediately(Transform transform, Vector3 position, float heading)
    {
        // Définir la position et la rotation en une seule opération
        transform.SetPositionAndRotation(position, Quaternion.Euler(0, heading, 0));

        // Réinitialiser les variables
        CurrentSpeed = 0f;
        waypoints.Clear();
        gpsBuffer.Clear();
        currentWaypointIndex = 0;
        hasValidPath = false;
        targetPosition = position;
    }


}


