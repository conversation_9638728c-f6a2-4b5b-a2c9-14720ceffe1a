Using pre-set license
Built from '2022.3/staging' branch; Version is '2022.3.47f1 (88c277b85d21) revision 8962679'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'fr' Physical Memory: 13234 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2022.3.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
C:/PL/Projets Unity/RegattaVisionV5
-logFile
Logs/AssetImportWorker2.log
-srvPort
60480
Successfully changed project path to: C:/PL/Projets Unity/RegattaVisionV5
C:/PL/Projets Unity/RegattaVisionV5
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [10432]  Target information:

Player connection [10432]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 1377355240 [EditorId] 1377355240 [Version] 1048832 [Id] WindowsEditor(7,MiniPC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [10432]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1377355240 [EditorId] 1377355240 [Version] 1048832 [Id] WindowsEditor(7,MiniPC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [10432] Host joined multi-casting on [***********:54997]...
Player connection [10432] Host joined alternative multi-casting on [***********:34997]...
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
Refreshing native plugins compatible for Editor in 36.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.47f1 (88c277b85d21)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/PL/Projets Unity/RegattaVisionV5/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: AMD Radeon(TM) Graphics (ID=0x1638)
    Vendor:   ATI
    VRAM:     6617 MB
    Driver:   31.0.21921.1000
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56748
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/MetroSupport/UnityEditor.UWP.Extensions.dll
Registered in 0.019337 seconds.
- Loaded All Assemblies, in  0.462 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 286 ms
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.674 seconds
Domain Reload Profiling: 1135ms
	BeginReloadAssembly (161ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (70ms)
	LoadAllAssembliesAndSetupDomain (165ms)
		LoadAssemblies (160ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (162ms)
			TypeCache.Refresh (160ms)
				TypeCache.ScanAssembly (146ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (675ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (620ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (400ms)
			SetLoadedEditorAssemblies (8ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (3ms)
			ProcessInitializeOnLoadAttributes (154ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.904 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.604 seconds
Domain Reload Profiling: 6505ms
	BeginReloadAssembly (277ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (41ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (4516ms)
		LoadAssemblies (3664ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1026ms)
			TypeCache.Refresh (961ms)
				TypeCache.ScanAssembly (872ms)
			ScanForSourceGeneratedMonoScriptInfo (43ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1605ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1253ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (27ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (222ms)
			ProcessInitializeOnLoadAttributes (882ms)
			ProcessInitializeOnLoadMethodAttributes (99ms)
			AfterProcessingInitializeOnLoad (16ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.14 seconds
Refreshing native plugins compatible for Editor in 81.79 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6836 Unused Serialized files (Serialized files now loaded: 0)
Unloading 47 unused Assets / (2.8 MB). Loaded Objects now: 7310.
Memory consumption went from 289.5 MB to 286.6 MB.
Total: 9.550500 ms (FindLiveObjects: 0.701300 ms CreateObjectMapping: 0.661500 ms MarkObjects: 6.442700 ms  DeleteObjects: 1.743000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Import Request.
  Time since last request: 105112.037611 seconds.
  path: Assets/Script/SoundManager.cs
  artifactKey: Guid(98b4705e507f4044497b50e68802af30) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/SoundManager.cs using Guid(98b4705e507f4044497b50e68802af30) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '3c976f46925aa10a25987a82aad889d6') in 0.004852 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Script/ResultsRow.cs
  artifactKey: Guid(d212fc0ac57edd642852d8e77e06d871) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/ResultsRow.cs using Guid(d212fc0ac57edd642852d8e77e06d871) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '6b1321a37b8a16f5f600ec0eca8398e2') in 0.000653 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Script/TeamInfoEditor.cs
  artifactKey: Guid(4ed3ca0ad65a541468dfaceaee092099) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/TeamInfoEditor.cs using Guid(4ed3ca0ad65a541468dfaceaee092099) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'f704a3312805a4e8dd57d421920e7d93') in 0.000982 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Script/RankingRow.cs
  artifactKey: Guid(79b0089ac7e74154fb3a0ab5929cf93b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/RankingRow.cs using Guid(79b0089ac7e74154fb3a0ab5929cf93b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'f0733960952f23dc18393f92d5df73d2') in 0.000677 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/Script/README_Race_Lines_System.md
  artifactKey: Guid(dfe22d81c021e9f40b5cb8a06b5539e2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/README_Race_Lines_System.md using Guid(dfe22d81c021e9f40b5cb8a06b5539e2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '517cc57c1d579f60d7453c64b69be101') in 0.010813 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Script/PythonLauncher.cs
  artifactKey: Guid(c4bc81084f42ea34c8371558cb766f8f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/PythonLauncher.cs using Guid(c4bc81084f42ea34c8371558cb766f8f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'f7084303ec5e337d1e452d8c85eb478b') in 0.000869 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Script/UI_PanelController.cs
  artifactKey: Guid(20e81e330630cfd47b5cd99f67508d2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/UI_PanelController.cs using Guid(20e81e330630cfd47b5cd99f67508d2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '6898ccd0445a57535a75f44fd8bdfb7e') in 0.000873 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Script/UIBoatSelection.cs
  artifactKey: Guid(e4604c333e7ec4f44a59ed616d126d28) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/UIBoatSelection.cs using Guid(e4604c333e7ec4f44a59ed616d126d28) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'f8dcff3136b90db762cb1ededb7bf348') in 0.000897 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Script/RaceLineManager.cs
  artifactKey: Guid(b06e90d1305099f41878feeed23f834c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/RaceLineManager.cs using Guid(b06e90d1305099f41878feeed23f834c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'e0f06067ee80a8458c3362ca21af67b1') in 0.000803 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Script/UIBoatEntry.cs
  artifactKey: Guid(1ebbe5834747219409d1f8ff92b54436) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/UIBoatEntry.cs using Guid(1ebbe5834747219409d1f8ff92b54436) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'bf15690be486345d4e74e064f5f53860') in 0.001094 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Script/RaceEvents.cs
  artifactKey: Guid(ccf18d8656e4c00469560df81cf911fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/RaceEvents.cs using Guid(ccf18d8656e4c00469560df81cf911fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '0af610a4eb972dc846168197887ff19b') in 0.000770 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
