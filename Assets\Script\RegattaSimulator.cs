using UnityEngine;
using System.Collections.Generic;
using Mapbox.Utils;
using Mapbox.Map;
using System;
using Debug = UnityEngine.Debug;
using Random = UnityEngine.Random;

public class RegattaSimulator : MonoBehaviour
{
    [Header("Simulation Parameters")]
    [SerializeField] private float updateInterval = 0.5f;
    [SerializeField] private float startDelay = 2f;
    [SerializeField] private float minDistanceThreshold = 0.1f;

    [Header("Meshtastic Simulation")]
    [SerializeField] private float captureInterval = 2f;
    [SerializeField] private float transmissionInterval = 20f;

    [Header("Navigation Settings")]
    [SerializeField] private float baseSpeed = 6f;
    [SerializeField] private float speedVariation = 2f;
    [SerializeField] private float turnProbability = 0.2f;
    [SerializeField] private float minTurnAngle = 20f;
    [SerializeField] private float maxTurnAngle = 60f;
    [SerializeField] private float turnDuration = 5f;

    [Header("Course Setup")]
    [SerializeField] private StartBuoyPosition startBuoy;
    [SerializeField] public List<RegattaBoatData> boats = new List<RegattaBoatData>();



    // Constantes
    private const float KNOTS_TO_MS = 0.514444f;
    private const float EARTH_RADIUS = 6371000f;

    private float timer;
    private bool isSimulationReady = false;
    private System.Random random;

    private Vector2d StartPosition => startBuoy != null ? startBuoy.GetCoordinates() : new Vector2d(0, 0);

    private void Start()
    {
        random = new System.Random();
        timer = updateInterval;
        Invoke(nameof(StartSimulation), startDelay);
    }

    private void StartSimulation()
    {
        if (startBuoy == null)
        {
            Debug.LogError("La bou�e de d�part n'est pas d�finie!");
            return;
        }

        Debug.Log("D�marrage de la simulation de r�gate...");
        InitializeBoats();
        isSimulationReady = true;
    }

    private void InitializeBoat(RegattaBoatData boat)
    {
        if (startBuoy == null)
        {
            Debug.LogError("Pas de bou�e de d�part d�finie!");
            return;
        }

        boat.currentPosition = StartPosition;
        boat.heading = Random.Range(0f, 360f);
        boat.targetHeading = boat.heading;
        boat.speed = baseSpeed + Random.Range(-1f, 1f);
        boat.turnTimer = 0f;
        boat.isInTurn = false;
        boat.targetSpeed = boat.speed;

        Debug.Log($"Bateau {boat.boatId} initialis� - Position: {boat.currentPosition}, Cap: {boat.heading}�");
    }

    private void InitializeBoats()
    {
        Debug.Log("Initialisation des bateaux...");
        boats.RemoveAll(boat => boat == null || boat.boatObject == null);

        foreach (var boat in boats)
        {
            InitializeBoat(boat);
        }
    }

    private void Update()
    {
        if (!isSimulationReady) return;

        timer -= Time.deltaTime;
        if (timer <= 0)
        {
            UpdateBoatsPositions();
            timer = updateInterval;
        }
    }

    private void UpdateBoatsPositions()
    {
        foreach (var boat in boats)
        {
            if (boat == null || boat.boatObject == null) continue;

            float speedMS = boat.speed * KNOTS_TO_MS;
            float distanceToMove = speedMS * updateInterval;

            if (distanceToMove < minDistanceThreshold) continue;

            UpdateBoatNavigation(boat);
            SimulateBoatMovement(boat, distanceToMove);
            SendSimulatedGPSData(boat);
        }
    }

    private void UpdateBoatNavigation(RegattaBoatData boat)
    {
        if (boat.isInTurn)
        {
            boat.turnTimer += updateInterval;
            float turnProgress = boat.turnTimer / turnDuration;

            if (turnProgress >= 1f)
            {
                boat.isInTurn = false;
                boat.heading = boat.targetHeading;
                boat.turnTimer = 0f;
                boat.speed = Mathf.Max(boat.speed * 0.8f, boat.targetSpeed * 0.6f);
            }
            else
            {
                boat.heading = Mathf.LerpAngle(boat.heading, boat.targetHeading, turnProgress);
                float turnSpeedFactor = 1f - (turnProgress * 0.4f);
                boat.speed = Mathf.Lerp(boat.speed, boat.targetSpeed * turnSpeedFactor, 0.1f);
            }
        }
        else if (Random.value < turnProbability * updateInterval)
        {
            float turnAngle = Random.Range(minTurnAngle, maxTurnAngle);
            if (Random.value < 0.5f) turnAngle = -turnAngle;
            boat.targetHeading = (boat.heading + turnAngle + 360f) % 360f;
            boat.isInTurn = true;
            boat.turnTimer = 0f;
            boat.speed = Mathf.Max(boat.speed * 0.9f, boat.targetSpeed * 0.7f);
        }
        else if (Random.value < 0.1f * updateInterval)
        {
            float windFactor = Mathf.Sin(Time.time * 0.1f) * 0.3f + 0.7f;
            boat.targetSpeed = baseSpeed * windFactor + Random.Range(-speedVariation, speedVariation);
            boat.speed = Mathf.Lerp(boat.speed, boat.targetSpeed, 0.02f * updateInterval);
        }
    }

    private void SimulateBoatMovement(RegattaBoatData boat, float distance)
    {
        float headingRad = boat.heading * Mathf.Deg2Rad;
        double latRad = boat.currentPosition.x * Mathf.Deg2Rad;
        double lonRad = boat.currentPosition.y * Mathf.Deg2Rad;

        double newLatRad = System.Math.Asin(
            System.Math.Sin(latRad) * System.Math.Cos(distance / EARTH_RADIUS) +
            System.Math.Cos(latRad) * System.Math.Sin(distance / EARTH_RADIUS) * System.Math.Cos(headingRad)
        );

        double newLonRad = lonRad + System.Math.Atan2(
            System.Math.Sin(headingRad) * System.Math.Sin(distance / EARTH_RADIUS) * System.Math.Cos(latRad),
            System.Math.Cos(distance / EARTH_RADIUS) - System.Math.Sin(latRad) * System.Math.Sin(newLatRad)
        );

        boat.currentPosition = new Vector2d(
            newLatRad * Mathf.Rad2Deg,
            newLonRad * Mathf.Rad2Deg
        );
    }

    private void SendSimulatedGPSData(RegattaBoatData boat)
    {
        var gpsSystem = boat.boatObject.GetComponent<UnifiedBoatGPS>();
        if (gpsSystem != null)
        {
            // ATTENTION : Ceci écrase les contrôles de hauteur manuels !
            gpsSystem.UpdateRealPosition(boat.currentPosition, boat.heading, Time.time);

            if (Time.frameCount % 300 == 0) // Log toutes les 5 secondes environ
            {
                Debug.LogWarning($"[RegattaSimulator] ⚠️ SIMULATION ACTIVE - écrase les contrôles manuels pour {boat.boatId}");
            }
        }
        else
        {
            Debug.LogError($"Le bateau {boat.boatId} n'a pas de composant UnifiedBoatGPS");
        }
    }

    public void AddBoat(string boatId, GameObject boatObject, Vector2d startPos)
    {
        if (boatObject == null)
        {
            Debug.LogError($"Tentative d'ajout d'un bateau avec un GameObject null: {boatId}");
            return;
        }

        var gpsSystem = boatObject.GetComponent<UnifiedBoatGPS>();
        if (gpsSystem == null)
        {
            Debug.LogError($"Le bateau {boatId} n'a pas de composant UnifiedBoatGPS");
            return;
        }

        // Initialiser le FloatingBoatUI ici
        var floatingUI = boatObject.GetComponentInChildren<FloatingBoatUI>();
        if (floatingUI != null)
        {
            Debug.Log($"Setting up FloatingUI for boat {boatId}");
            floatingUI.SetTarget(boatObject.transform);
            floatingUI.SetTeamName(boatId);
        }
        else
        {
            Debug.LogError($"FloatingUI not found for boat {boatId}");
        }

        RegattaBoatData newBoat = new RegattaBoatData(boatId, boatObject, StartPosition);

        if (isSimulationReady)
        {
            InitializeBoat(newBoat);
        }

        boats.Add(newBoat);
        Debug.Log($"Nouveau bateau ajout�: {boatId} � la position {StartPosition}");
    }

    public void SetBaseSpeed(float newSpeed)
    {
        baseSpeed = Mathf.Max(0f, newSpeed);
        foreach (var boat in boats)
        {
            boat.targetSpeed = baseSpeed + Random.Range(-speedVariation, speedVariation);
        }
        Debug.Log($"Vitesse de base mise � jour: {baseSpeed} n�uds");
    }

    /// <summary>
    /// Désactive temporairement la simulation pour permettre les contrôles manuels
    /// </summary>
    [ContextMenu("Disable Simulation Temporarily")]
    public void DisableSimulationTemporarily()
    {
        isSimulationReady = false;
        Debug.LogWarning("[RegattaSimulator] ⚠️ Simulation désactivée temporairement pour permettre les contrôles manuels");
    }

    /// <summary>
    /// Réactive la simulation
    /// </summary>
    [ContextMenu("Enable Simulation")]
    public void EnableSimulation()
    {
        isSimulationReady = true;
        Debug.Log("[RegattaSimulator] ✅ Simulation réactivée");
    }

    /// <summary>
    /// Vérifie si la simulation est active
    /// </summary>
    public bool IsSimulationActive()
    {
        return isSimulationReady;
    }
}
