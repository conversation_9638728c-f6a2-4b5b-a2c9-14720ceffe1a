{"format": 1, "restore": {"c:\\PL\\Projets Unity\\RegattaVisionV5\\Assembly-CSharp.csproj": {}}, "projects": {"c:\\PL\\Projets Unity\\RegattaVisionV5\\Assembly-CSharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "c:\\PL\\Projets Unity\\RegattaVisionV5\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "c:\\PL\\Projets Unity\\RegattaVisionV5\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "c:\\PL\\Projets Unity\\RegattaVisionV5\\Temp\\obj\\\\Assembly-CSharp\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.105\\RuntimeIdentifierGraph.json"}}}}}