# Système de Lignes de Course

## Vue d'ensemble

Ce système crée automatiquement les lignes de départ et d'arrivée entre le bateau sécurité et les bouées B1/B2. Il remplace l'ancien système qui créait une ligne entre les deux bouées.

## Architecture du Système

### 1. **RaceLineManager**
- **Rôle** : Gère les lignes de départ et d'arrivée en temps réel
- **Fonctionnalités** :
  - Détecte automatiquement le bateau sécurité (NodeId: `!da5394d0`)
  - S'abonne aux événements de placement des bouées B1/B2
  - Crée deux lignes distinctes :
    - **Ligne de départ** : Bateau sécurité ↔ Bouée B1 (verte)
    - **Ligne d'arrivée** : Bateau sécurité ↔ Bouée B2 (rouge)
  - Met à jour les lignes en temps réel quand le bateau sécurité bouge

### 2. **RaceLineSetup**
- **Rôle** : Installation et configuration automatique du système
- **Fonctionnalités** :
  - Configuration automatique au démarrage
  - Vérification de la configuration
  - Outils de test et de debug

## Installation et Configuration

### Installation Automatique (Recommandée)

1. **Ajouter le script de setup** :
   ```csharp
   // Ajouter RaceLineSetup à un GameObject dans la scène
   GameObject setupObject = new GameObject("Race Line Setup");
   setupObject.AddComponent<RaceLineSetup>();
   ```

2. **Configuration automatique** :
   - Le système se configure automatiquement au démarrage
   - Ou utiliser le menu contextuel : "Setup Race Line System"

### Installation Manuelle

1. **Créer le RaceLineManager** :
   ```csharp
   GameObject raceLineObject = new GameObject("Race Line Manager");
   RaceLineManager manager = raceLineObject.AddComponent<RaceLineManager>();
   ```

2. **Le système s'abonne automatiquement** aux événements du `MeshtasticGPSTracker`

## Fonctionnement

### Séquence d'Initialisation

1. **Détection du bateau sécurité** : Le système recherche le bateau avec NodeId `!da5394d0`
2. **Abonnement aux événements** : S'abonne aux événements de placement des bouées
3. **Réception des bouées** : Quand B1 et B2 sont placées via les messages Meshtastic
4. **Création des lignes** : Crée automatiquement les deux lignes
5. **Mise à jour temps réel** : Les lignes suivent le mouvement du bateau sécurité

### Placement des Bouées

Les bouées sont placées via les messages Meshtastic :
- **Message "B1"** → Place la bouée de départ
- **Message "B2"** → Place la bouée d'arrivée

### Types de Lignes

1. **Ligne de Départ** (Verte) :
   - Relie le bateau sécurité à la bouée B1
   - Utilisée pour détecter le départ des bateaux

2. **Ligne d'Arrivée** (Rouge) :
   - Relie le bateau sécurité à la bouée B2
   - Utilisée pour détecter l'arrivée des bateaux

## Utilisation

### Contrôles Disponibles

#### **RaceLineManager** :
```csharp
RaceLineManager manager = FindObjectOfType<RaceLineManager>();

// Activer/désactiver les lignes
manager.ToggleStartLine();
manager.ToggleFinishLine();

// Afficher les informations de debug
manager.ShowDebugInfo();

// Réinitialiser le système
manager.ResetRaceLines();
```

#### **RaceLineSetup** :
```csharp
RaceLineSetup setup = FindObjectOfType<RaceLineSetup>();

// Configurer le système
setup.SetupRaceLineSystem();

// Créer des lignes de test
setup.CreateTestLines();

// Test de placement de bouées
setup.TestBuoyPlacement();
```

### Menus Contextuels

#### **RaceLineManager** :
- **"Toggle Start Line"** : Active/désactive la ligne de départ
- **"Toggle Finish Line"** : Active/désactive la ligne d'arrivée
- **"Reset Race Lines"** : Réinitialise tout le système
- **"Show Debug Info"** : Affiche les informations de debug

#### **RaceLineSetup** :
- **"Setup Race Line System"** : Configure automatiquement le système
- **"Create Test Lines"** : Crée des lignes de test
- **"Test Buoy Placement"** : Crée des bouées de test
- **"Clean and Reconfigure"** : Nettoie et reconfigure

## Configuration

### Paramètres du RaceLineManager

```csharp
[Header("Configuration")]
public string securityBoatNodeId = "!da5394d0";  // NodeId du bateau sécurité
public bool showStartingLine = true;              // Afficher ligne de départ
public bool showFinishLine = true;                // Afficher ligne d'arrivée

[Header("Ligne de Départ")]
public Color startLineColor = Color.green;        // Couleur ligne de départ
public float startLineWidth = 0.8f;               // Largeur ligne de départ

[Header("Ligne d'Arrivée")]
public Color finishLineColor = Color.red;         // Couleur ligne d'arrivée
public float finishLineWidth = 0.8f;              // Largeur ligne d'arrivée

[Header("Paramètres")]
public float lineHeight = 2f;                     // Hauteur des lignes au-dessus de l'eau
```

## Debugging et Monitoring

### Logs de Debug

```csharp
// Activer les logs détaillés
manager.showDebugLogs = true;
```

### Vérification du Système

```csharp
// Vérifier la configuration
setup.VerifySetup();

// Afficher les informations de debug
manager.ShowDebugInfo();
```

### Informations Affichées

- État du bateau sécurité (trouvé/non trouvé)
- État des bouées B1 et B2
- État des lignes (actives/inactives)
- État d'initialisation du système

## Dépannage

### Problèmes Courants

1. **Les lignes ne s'affichent pas** :
   - Vérifier que le bateau sécurité est détecté
   - Vérifier que les bouées B1/B2 sont placées
   - Vérifier les logs de debug

2. **Le bateau sécurité n'est pas détecté** :
   - Vérifier le NodeId dans la configuration (`!da5394d0`)
   - Vérifier que le bateau sécurité est dans le système Meshtastic
   - Utiliser "Show Debug Info" pour diagnostiquer

3. **Les bouées ne sont pas détectées** :
   - Vérifier que les messages B1/B2 sont envoyés
   - Vérifier les logs du MeshtasticGPSTracker
   - Utiliser "Test Buoy Placement" pour tester

### Commandes de Debug

```csharp
// Dans l'inspecteur, clic droit sur les composants :
// - "Show Debug Info" (RaceLineManager)
// - "Setup Race Line System" (RaceLineSetup)
// - "Test Buoy Placement" (RaceLineSetup)
```

## Migration depuis l'Ancien Système

L'ancien système `StartingLine` créait une ligne entre les deux bouées. Le nouveau système crée deux lignes distinctes entre le bateau sécurité et chaque bouée.

**Avantages du nouveau système** :
- ✅ Lignes de départ ET d'arrivée distinctes
- ✅ Utilise le bateau sécurité comme point de référence mobile
- ✅ Mise à jour temps réel des lignes
- ✅ Configuration automatique
- ✅ Meilleure intégration avec le système Meshtastic
- ✅ Debugging et monitoring améliorés

## Intégration avec le Système Existant

Le système s'intègre automatiquement avec :
- **MeshtasticGPSTracker** : Pour les événements de bouées
- **RaceController** : Pour la gestion de course
- **UnifiedBoatGPS** : Pour le mouvement du bateau sécurité

Aucune modification manuelle n'est nécessaire dans les autres systèmes.
