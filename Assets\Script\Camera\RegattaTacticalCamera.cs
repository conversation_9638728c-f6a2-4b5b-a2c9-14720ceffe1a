using UnityEngine;
using System.Linq;

public class RegattaTacticalCamera : MonoBeh<PERSON><PERSON>
{
    [Header("Settings")]
    [SerializeField] private float baseHeight = 50f;
    [SerializeField] private float tiltAngle = 45f;
    [SerializeField] private float damping = 2f;

    [Header("Dynamic Adjustment")]
    [SerializeField] private float minHeight = 30f;
    [SerializeField] private float maxHeight = 80f;
    [SerializeField] private float heightMultiplier = 0.7f;
    [SerializeField] private float minSize = 30f;
    [SerializeField] private float padding = 1.2f;

    [Header("Camera Movement")]
    [SerializeField] private float orbitSpeed = 0.05f;        // Vitesse de rotation autour de la flotte
    [SerializeField] private float orbitRadius = 10f;         // Rayon du mouvement de la cam�ra
    [SerializeField] private float heightVariation = 5f;      // Amplitude de la variation de hauteur
    [SerializeField] private float heightSpeed = 0.1f;        // Vitesse de la variation de hauteur

    [SerializeField] private RaceController raceController;
    private Vector3 velocity;
    private Vector3 targetPosition;
    private float currentTime;

    private void LateUpdate()
    {
        if (raceController == null) return;

        // Utiliser seulement les bateaux actifs dans la course, pas tous les bateaux de la scène
        var racers = raceController.GetRacers();
        if (racers == null || racers.Count == 0) return;

        // Extraire les GameObjects des bateaux actifs
        var boats = racers.Select(r => r.boat).Where(b => b != null).ToList();

        currentTime += Time.deltaTime;

        // Calculer la bo�te englobante de la flotte
        Vector3 minBounds = new Vector3(float.MaxValue, 0, float.MaxValue);
        Vector3 maxBounds = new Vector3(float.MinValue, 0, float.MinValue);
        Vector3 fleetCenter = Vector3.zero;

        foreach (var boat in boats.Where(b => b != null))
        {
            Vector3 pos = boat.transform.position;
            minBounds.x = Mathf.Min(minBounds.x, pos.x);
            minBounds.z = Mathf.Min(minBounds.z, pos.z);
            maxBounds.x = Mathf.Max(maxBounds.x, pos.x);
            maxBounds.z = Mathf.Max(maxBounds.z, pos.z);
            fleetCenter += pos;
        }
        fleetCenter /= boats.Count;

        // Calculer la taille de la zone � couvrir
        float width = Mathf.Max(maxBounds.x - minBounds.x, minSize);
        float length = Mathf.Max(maxBounds.z - minBounds.z, minSize);
        float size = Mathf.Max(width, length) * padding;

        // Ajuster la hauteur en fonction de la taille de la flotte
        float adjustedHeight = Mathf.Clamp(
            baseHeight + (size * heightMultiplier),
            minHeight,
            maxHeight
        );

        // Ajouter un mouvement orbital doux
        float orbitX = Mathf.Sin(currentTime * orbitSpeed) * orbitRadius;
        float orbitZ = Mathf.Cos(currentTime * orbitSpeed) * orbitRadius;

        // Ajouter une variation de hauteur
        float heightOffset = Mathf.Sin(currentTime * heightSpeed) * heightVariation;

        // Position de base de la cam�ra
        Vector3 basePosition = new Vector3(
            fleetCenter.x,
            adjustedHeight + heightOffset,
            fleetCenter.z - (adjustedHeight * 0.5f)
        );

        // Ajouter le mouvement orbital � la position de base
        targetPosition = basePosition + new Vector3(orbitX, 0, orbitZ);

        // Mouvement smooth
        transform.position = Vector3.SmoothDamp(
            transform.position,
            targetPosition,
            ref velocity,
            damping
        );

        // Rotation pour toujours regarder le centre de la flotte
        Vector3 directionToCenter = fleetCenter - transform.position;
        Quaternion targetRotation = Quaternion.LookRotation(directionToCenter);
        transform.rotation = Quaternion.Euler(
            tiltAngle,
            targetRotation.eulerAngles.y,
            0
        );

#if UNITY_EDITOR
        Debug.DrawLine(new Vector3(minBounds.x, 0, minBounds.z), new Vector3(maxBounds.x, 0, minBounds.z), Color.red);
        Debug.DrawLine(new Vector3(maxBounds.x, 0, minBounds.z), new Vector3(maxBounds.x, 0, maxBounds.z), Color.red);
        Debug.DrawLine(new Vector3(maxBounds.x, 0, maxBounds.z), new Vector3(minBounds.x, 0, maxBounds.z), Color.red);
        Debug.DrawLine(new Vector3(minBounds.x, 0, maxBounds.z), new Vector3(minBounds.x, 0, minBounds.z), Color.red);
#endif
    }

    private void OnDrawGizmosSelected()
    {
        Gizmos.color = Color.yellow;
        Vector3 forward = Quaternion.Euler(tiltAngle, 0, 0) * Vector3.forward;
        Gizmos.DrawRay(transform.position, forward * 50f);
    }
}