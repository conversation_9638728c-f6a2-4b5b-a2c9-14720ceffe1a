{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 14060, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 14060, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 14060, "tid": 435848, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 14060, "tid": 435848, "ts": 1748419921608568, "dur": 22, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 14060, "tid": 435848, "ts": 1748419921608612, "dur": 11, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 14060, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 14060, "tid": 1, "ts": 1748419918390436, "dur": 3605, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 14060, "tid": 1, "ts": 1748419918394051, "dur": 90857, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 14060, "tid": 1, "ts": 1748419918484912, "dur": 103904, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 14060, "tid": 435848, "ts": 1748419921608627, "dur": 23, "ph": "X", "name": "", "args": {}}, {"pid": 14060, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918390375, "dur": 14026, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918404404, "dur": 3203489, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918404419, "dur": 47, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918404475, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918404480, "dur": 299, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918404787, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918404793, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918404859, "dur": 8, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918404870, "dur": 3489, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918408370, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918408375, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918408459, "dur": 5, "ph": "X", "name": "ProcessMessages 1002", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918408467, "dur": 58, "ph": "X", "name": "ReadAsync 1002", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918408531, "dur": 5, "ph": "X", "name": "ProcessMessages 1222", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918408538, "dur": 55, "ph": "X", "name": "ReadAsync 1222", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918408597, "dur": 4, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918408604, "dur": 64, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918408677, "dur": 6, "ph": "X", "name": "ProcessMessages 1057", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918408687, "dur": 63, "ph": "X", "name": "ReadAsync 1057", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918408758, "dur": 7, "ph": "X", "name": "ProcessMessages 1064", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918408768, "dur": 53, "ph": "X", "name": "ReadAsync 1064", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918408825, "dur": 2, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918408829, "dur": 53, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918408889, "dur": 5, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918408897, "dur": 66, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918408968, "dur": 4, "ph": "X", "name": "ProcessMessages 1316", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918408975, "dur": 52, "ph": "X", "name": "ReadAsync 1316", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409036, "dur": 6, "ph": "X", "name": "ProcessMessages 1262", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409044, "dur": 45, "ph": "X", "name": "ReadAsync 1262", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409093, "dur": 3, "ph": "X", "name": "ProcessMessages 1273", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409098, "dur": 35, "ph": "X", "name": "ReadAsync 1273", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409138, "dur": 3, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409143, "dur": 42, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409191, "dur": 2, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409195, "dur": 39, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409237, "dur": 3, "ph": "X", "name": "ProcessMessages 1073", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409243, "dur": 33, "ph": "X", "name": "ReadAsync 1073", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409279, "dur": 2, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409284, "dur": 33, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409321, "dur": 2, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409325, "dur": 38, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409367, "dur": 2, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409371, "dur": 51, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409427, "dur": 3, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409432, "dur": 52, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409488, "dur": 3, "ph": "X", "name": "ProcessMessages 1097", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409493, "dur": 27, "ph": "X", "name": "ReadAsync 1097", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409523, "dur": 2, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409527, "dur": 35, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409565, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409569, "dur": 43, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409616, "dur": 3, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409621, "dur": 40, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409665, "dur": 3, "ph": "X", "name": "ProcessMessages 830", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409670, "dur": 44, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409719, "dur": 3, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409724, "dur": 43, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409771, "dur": 3, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409775, "dur": 44, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409824, "dur": 3, "ph": "X", "name": "ProcessMessages 996", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409829, "dur": 40, "ph": "X", "name": "ReadAsync 996", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409873, "dur": 3, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409878, "dur": 39, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409922, "dur": 3, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409927, "dur": 45, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409976, "dur": 3, "ph": "X", "name": "ProcessMessages 1062", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918409980, "dur": 44, "ph": "X", "name": "ReadAsync 1062", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410028, "dur": 3, "ph": "X", "name": "ProcessMessages 1038", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410033, "dur": 40, "ph": "X", "name": "ReadAsync 1038", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410077, "dur": 3, "ph": "X", "name": "ProcessMessages 932", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410084, "dur": 45, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410134, "dur": 3, "ph": "X", "name": "ProcessMessages 967", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410139, "dur": 36, "ph": "X", "name": "ReadAsync 967", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410178, "dur": 2, "ph": "X", "name": "ProcessMessages 972", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410182, "dur": 39, "ph": "X", "name": "ReadAsync 972", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410225, "dur": 3, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410230, "dur": 44, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410278, "dur": 3, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410283, "dur": 42, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410329, "dur": 3, "ph": "X", "name": "ProcessMessages 754", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410334, "dur": 41, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410378, "dur": 3, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410383, "dur": 43, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410430, "dur": 3, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410435, "dur": 45, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410484, "dur": 3, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410489, "dur": 42, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410535, "dur": 3, "ph": "X", "name": "ProcessMessages 1072", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410540, "dur": 51, "ph": "X", "name": "ReadAsync 1072", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410595, "dur": 3, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410600, "dur": 43, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410648, "dur": 3, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410653, "dur": 39, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410697, "dur": 3, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410702, "dur": 37, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410744, "dur": 1, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410747, "dur": 44, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410794, "dur": 3, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410801, "dur": 43, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410848, "dur": 3, "ph": "X", "name": "ProcessMessages 713", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410853, "dur": 40, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410897, "dur": 3, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410901, "dur": 37, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410943, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410947, "dur": 34, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410984, "dur": 2, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918410988, "dur": 38, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411031, "dur": 2, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411036, "dur": 37, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411076, "dur": 2, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411081, "dur": 40, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411125, "dur": 2, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411129, "dur": 35, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411167, "dur": 3, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411172, "dur": 40, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411216, "dur": 2, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411221, "dur": 43, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411268, "dur": 2, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411273, "dur": 38, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411314, "dur": 3, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411319, "dur": 40, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411363, "dur": 3, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411368, "dur": 41, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411414, "dur": 3, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411419, "dur": 42, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411465, "dur": 3, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411470, "dur": 80, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411553, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411556, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411604, "dur": 3, "ph": "X", "name": "ProcessMessages 788", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411609, "dur": 42, "ph": "X", "name": "ReadAsync 788", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411655, "dur": 3, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411660, "dur": 44, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411709, "dur": 3, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411714, "dur": 30, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411747, "dur": 2, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411751, "dur": 23, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411777, "dur": 2, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411780, "dur": 38, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411823, "dur": 3, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411828, "dur": 33, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411865, "dur": 2, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411869, "dur": 42, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411916, "dur": 3, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411921, "dur": 45, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411969, "dur": 3, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918411974, "dur": 36, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412015, "dur": 2, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412019, "dur": 40, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412064, "dur": 2, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412068, "dur": 42, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412114, "dur": 3, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412120, "dur": 55, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412178, "dur": 2, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412182, "dur": 43, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412230, "dur": 3, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412235, "dur": 45, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412284, "dur": 3, "ph": "X", "name": "ProcessMessages 885", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412289, "dur": 41, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412334, "dur": 3, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412339, "dur": 35, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412378, "dur": 2, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412382, "dur": 43, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412430, "dur": 3, "ph": "X", "name": "ProcessMessages 865", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412435, "dur": 41, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412480, "dur": 3, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412486, "dur": 31, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412520, "dur": 2, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412524, "dur": 37, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412565, "dur": 2, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412569, "dur": 43, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412617, "dur": 3, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412622, "dur": 42, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412668, "dur": 3, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412674, "dur": 44, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412722, "dur": 3, "ph": "X", "name": "ProcessMessages 791", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412727, "dur": 43, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412774, "dur": 3, "ph": "X", "name": "ProcessMessages 893", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412779, "dur": 40, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412823, "dur": 3, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412827, "dur": 37, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412868, "dur": 2, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412872, "dur": 43, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412920, "dur": 3, "ph": "X", "name": "ProcessMessages 874", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412925, "dur": 35, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412974, "dur": 2, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918412979, "dur": 36, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413019, "dur": 3, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413024, "dur": 43, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413072, "dur": 2, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413077, "dur": 43, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413123, "dur": 3, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413129, "dur": 38, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413170, "dur": 3, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413175, "dur": 39, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413218, "dur": 2, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413222, "dur": 41, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413268, "dur": 3, "ph": "X", "name": "ProcessMessages 894", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413273, "dur": 47, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413324, "dur": 3, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413329, "dur": 29, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413362, "dur": 2, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413366, "dur": 218, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413588, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413592, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413644, "dur": 3, "ph": "X", "name": "ProcessMessages 993", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413649, "dur": 42, "ph": "X", "name": "ReadAsync 993", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413695, "dur": 3, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413700, "dur": 40, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413743, "dur": 2, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413748, "dur": 34, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413786, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413791, "dur": 46, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413841, "dur": 3, "ph": "X", "name": "ProcessMessages 892", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413846, "dur": 42, "ph": "X", "name": "ReadAsync 892", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413892, "dur": 3, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413897, "dur": 39, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413940, "dur": 2, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413944, "dur": 40, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413989, "dur": 3, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918413994, "dur": 39, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414037, "dur": 2, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414042, "dur": 43, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414089, "dur": 3, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414094, "dur": 38, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414135, "dur": 2, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414139, "dur": 43, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414187, "dur": 3, "ph": "X", "name": "ProcessMessages 952", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414192, "dur": 45, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414242, "dur": 3, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414247, "dur": 27, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414278, "dur": 2, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414282, "dur": 54, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414341, "dur": 2, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414346, "dur": 43, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414394, "dur": 3, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414399, "dur": 45, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414449, "dur": 3, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414455, "dur": 41, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414501, "dur": 3, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414507, "dur": 48, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414559, "dur": 3, "ph": "X", "name": "ProcessMessages 895", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414565, "dur": 46, "ph": "X", "name": "ReadAsync 895", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414616, "dur": 3, "ph": "X", "name": "ProcessMessages 825", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414622, "dur": 40, "ph": "X", "name": "ReadAsync 825", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414666, "dur": 3, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414672, "dur": 30, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414705, "dur": 2, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414710, "dur": 36, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414750, "dur": 2, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414754, "dur": 47, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414807, "dur": 3, "ph": "X", "name": "ProcessMessages 968", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414813, "dur": 31, "ph": "X", "name": "ReadAsync 968", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414847, "dur": 2, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414852, "dur": 36, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414892, "dur": 2, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414896, "dur": 38, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414937, "dur": 2, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414942, "dur": 40, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414985, "dur": 2, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918414989, "dur": 39, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415033, "dur": 2, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415037, "dur": 43, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415085, "dur": 2, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415089, "dur": 48, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415142, "dur": 4, "ph": "X", "name": "ProcessMessages 820", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415149, "dur": 45, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415197, "dur": 3, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415202, "dur": 37, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415244, "dur": 2, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415248, "dur": 43, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415295, "dur": 3, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415300, "dur": 33, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415336, "dur": 2, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415341, "dur": 38, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415382, "dur": 2, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415387, "dur": 44, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415436, "dur": 2, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415442, "dur": 50, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415498, "dur": 4, "ph": "X", "name": "ProcessMessages 978", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415505, "dur": 55, "ph": "X", "name": "ReadAsync 978", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415564, "dur": 3, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415569, "dur": 37, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415609, "dur": 2, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415614, "dur": 50, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415669, "dur": 3, "ph": "X", "name": "ProcessMessages 885", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415674, "dur": 45, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415723, "dur": 3, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415729, "dur": 39, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415772, "dur": 3, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415777, "dur": 30, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415811, "dur": 2, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415815, "dur": 44, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415863, "dur": 3, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415868, "dur": 47, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415919, "dur": 3, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415924, "dur": 35, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415963, "dur": 2, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918415968, "dur": 42, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416014, "dur": 3, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416019, "dur": 43, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416067, "dur": 3, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416072, "dur": 40, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416117, "dur": 3, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416121, "dur": 36, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416162, "dur": 2, "ph": "X", "name": "ProcessMessages 211", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416166, "dur": 36, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416206, "dur": 2, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416211, "dur": 40, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416255, "dur": 3, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416260, "dur": 38, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416302, "dur": 3, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416307, "dur": 40, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416352, "dur": 2, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416356, "dur": 31, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416390, "dur": 2, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416394, "dur": 36, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416434, "dur": 2, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416439, "dur": 34, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416477, "dur": 2, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416482, "dur": 40, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416527, "dur": 2, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416531, "dur": 38, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416572, "dur": 3, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416577, "dur": 41, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416622, "dur": 2, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416627, "dur": 39, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416670, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416675, "dur": 38, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416716, "dur": 2, "ph": "X", "name": "ProcessMessages 1155", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416722, "dur": 40, "ph": "X", "name": "ReadAsync 1155", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416765, "dur": 3, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416770, "dur": 41, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416815, "dur": 3, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416820, "dur": 38, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416862, "dur": 2, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416866, "dur": 41, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416912, "dur": 3, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416918, "dur": 35, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416956, "dur": 3, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918416961, "dur": 36, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417001, "dur": 2, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417005, "dur": 39, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417048, "dur": 2, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417052, "dur": 40, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417097, "dur": 3, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417102, "dur": 33, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417138, "dur": 2, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417142, "dur": 38, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417184, "dur": 3, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417190, "dur": 43, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417238, "dur": 2, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417242, "dur": 49, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417295, "dur": 3, "ph": "X", "name": "ProcessMessages 833", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417300, "dur": 44, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417349, "dur": 3, "ph": "X", "name": "ProcessMessages 928", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417355, "dur": 68, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417429, "dur": 4, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417437, "dur": 54, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417495, "dur": 4, "ph": "X", "name": "ProcessMessages 1182", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417501, "dur": 48, "ph": "X", "name": "ReadAsync 1182", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417556, "dur": 4, "ph": "X", "name": "ProcessMessages 955", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417563, "dur": 51, "ph": "X", "name": "ReadAsync 955", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417618, "dur": 2, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417623, "dur": 45, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417673, "dur": 3, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417678, "dur": 70, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417753, "dur": 3, "ph": "X", "name": "ProcessMessages 1015", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417759, "dur": 50, "ph": "X", "name": "ReadAsync 1015", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417814, "dur": 3, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417819, "dur": 46, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417870, "dur": 3, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417874, "dur": 50, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417929, "dur": 3, "ph": "X", "name": "ProcessMessages 962", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417935, "dur": 44, "ph": "X", "name": "ReadAsync 962", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417983, "dur": 3, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918417988, "dur": 57, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418050, "dur": 3, "ph": "X", "name": "ProcessMessages 985", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418056, "dur": 45, "ph": "X", "name": "ReadAsync 985", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418104, "dur": 2, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418109, "dur": 45, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418158, "dur": 3, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418163, "dur": 41, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418210, "dur": 2, "ph": "X", "name": "ProcessMessages 168", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418214, "dur": 44, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418262, "dur": 2, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418266, "dur": 42, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418312, "dur": 3, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418317, "dur": 44, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418366, "dur": 3, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418371, "dur": 62, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418438, "dur": 3, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418443, "dur": 45, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418491, "dur": 2, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418496, "dur": 49, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418549, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418554, "dur": 52, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418610, "dur": 3, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418615, "dur": 47, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418667, "dur": 2, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418671, "dur": 43, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418718, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418722, "dur": 36, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418762, "dur": 2, "ph": "X", "name": "ProcessMessages 105", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418768, "dur": 46, "ph": "X", "name": "ReadAsync 105", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418818, "dur": 2, "ph": "X", "name": "ProcessMessages 890", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418823, "dur": 40, "ph": "X", "name": "ReadAsync 890", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418867, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418890, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418949, "dur": 5, "ph": "X", "name": "ProcessMessages 1589", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918418956, "dur": 40, "ph": "X", "name": "ReadAsync 1589", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419000, "dur": 2, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419004, "dur": 42, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419051, "dur": 2, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419055, "dur": 47, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419107, "dur": 3, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419113, "dur": 43, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419160, "dur": 2, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419165, "dur": 54, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419222, "dur": 2, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419227, "dur": 48, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419280, "dur": 2, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419284, "dur": 55, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419344, "dur": 3, "ph": "X", "name": "ProcessMessages 945", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419349, "dur": 42, "ph": "X", "name": "ReadAsync 945", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419396, "dur": 3, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419401, "dur": 43, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419448, "dur": 2, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419453, "dur": 41, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419498, "dur": 2, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419502, "dur": 48, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419554, "dur": 2, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419559, "dur": 41, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419604, "dur": 2, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419609, "dur": 42, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419654, "dur": 2, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419658, "dur": 49, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419713, "dur": 4, "ph": "X", "name": "ProcessMessages 757", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419721, "dur": 51, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419777, "dur": 3, "ph": "X", "name": "ProcessMessages 229", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419784, "dur": 63, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419853, "dur": 4, "ph": "X", "name": "ProcessMessages 826", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419860, "dur": 61, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419927, "dur": 4, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918419935, "dur": 64, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420004, "dur": 4, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420011, "dur": 54, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420070, "dur": 3, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420076, "dur": 51, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420133, "dur": 4, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420140, "dur": 60, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420206, "dur": 4, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420214, "dur": 62, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420284, "dur": 5, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420294, "dur": 68, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420367, "dur": 3, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420374, "dur": 64, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420443, "dur": 5, "ph": "X", "name": "ProcessMessages 1017", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420452, "dur": 59, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420515, "dur": 5, "ph": "X", "name": "ProcessMessages 883", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420523, "dur": 59, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420587, "dur": 4, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420596, "dur": 63, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420664, "dur": 5, "ph": "X", "name": "ProcessMessages 917", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420672, "dur": 56, "ph": "X", "name": "ReadAsync 917", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420733, "dur": 4, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420740, "dur": 65, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420811, "dur": 6, "ph": "X", "name": "ProcessMessages 1253", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420819, "dur": 63, "ph": "X", "name": "ReadAsync 1253", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420890, "dur": 4, "ph": "X", "name": "ProcessMessages 969", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420897, "dur": 57, "ph": "X", "name": "ReadAsync 969", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420959, "dur": 3, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918420966, "dur": 54, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421025, "dur": 4, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421032, "dur": 73, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421119, "dur": 6, "ph": "X", "name": "ProcessMessages 893", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421134, "dur": 66, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421208, "dur": 4, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421215, "dur": 43, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421265, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421272, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421336, "dur": 4, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421343, "dur": 52, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421401, "dur": 4, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421408, "dur": 52, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421465, "dur": 4, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421472, "dur": 44, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421520, "dur": 3, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421526, "dur": 55, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421586, "dur": 3, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421592, "dur": 52, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421648, "dur": 4, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421655, "dur": 49, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421709, "dur": 3, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421716, "dur": 50, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421770, "dur": 3, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421777, "dur": 50, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421832, "dur": 3, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421838, "dur": 49, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421892, "dur": 3, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421898, "dur": 59, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421966, "dur": 6, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918421975, "dur": 63, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422044, "dur": 5, "ph": "X", "name": "ProcessMessages 812", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422053, "dur": 50, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422106, "dur": 3, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422113, "dur": 43, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422161, "dur": 3, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422167, "dur": 43, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422214, "dur": 1, "ph": "X", "name": "ProcessMessages 173", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422217, "dur": 40, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422260, "dur": 3, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422265, "dur": 38, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422306, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422311, "dur": 68, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422387, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422392, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422464, "dur": 5, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422472, "dur": 50, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422526, "dur": 4, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422533, "dur": 68, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422608, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422614, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422674, "dur": 6, "ph": "X", "name": "ProcessMessages 1031", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422684, "dur": 52, "ph": "X", "name": "ReadAsync 1031", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422741, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422747, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422819, "dur": 5, "ph": "X", "name": "ProcessMessages 1072", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422828, "dur": 50, "ph": "X", "name": "ReadAsync 1072", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422884, "dur": 3, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422890, "dur": 61, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422957, "dur": 4, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918422965, "dur": 45, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423014, "dur": 4, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423021, "dur": 54, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423081, "dur": 3, "ph": "X", "name": "ProcessMessages 195", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423087, "dur": 59, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423151, "dur": 5, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423160, "dur": 38, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423203, "dur": 3, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423209, "dur": 60, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423275, "dur": 5, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423284, "dur": 48, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423338, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423343, "dur": 63, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423412, "dur": 5, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423421, "dur": 52, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423478, "dur": 3, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423485, "dur": 58, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423548, "dur": 4, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423556, "dur": 55, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423616, "dur": 4, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423624, "dur": 58, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423688, "dur": 4, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423695, "dur": 53, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423755, "dur": 5, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423763, "dur": 55, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423823, "dur": 4, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423831, "dur": 56, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423893, "dur": 5, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423901, "dur": 61, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423968, "dur": 4, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918423975, "dur": 61, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424041, "dur": 4, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424048, "dur": 32, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424084, "dur": 2, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424089, "dur": 45, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424140, "dur": 2, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424146, "dur": 59, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424210, "dur": 5, "ph": "X", "name": "ProcessMessages 883", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424218, "dur": 47, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424271, "dur": 3, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424278, "dur": 57, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424339, "dur": 4, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424347, "dur": 54, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424406, "dur": 4, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424414, "dur": 54, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424473, "dur": 3, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424480, "dur": 60, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424547, "dur": 4, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424554, "dur": 57, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424616, "dur": 2, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424620, "dur": 50, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424679, "dur": 5, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424687, "dur": 53, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424747, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424751, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424807, "dur": 3, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424812, "dur": 41, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424858, "dur": 3, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424863, "dur": 51, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424918, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424921, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424985, "dur": 4, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918424991, "dur": 52, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425047, "dur": 3, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425052, "dur": 49, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425106, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425110, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425162, "dur": 4, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425168, "dur": 41, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425215, "dur": 3, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425220, "dur": 38, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425263, "dur": 2, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425267, "dur": 39, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425311, "dur": 3, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425315, "dur": 43, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425363, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425368, "dur": 24, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425395, "dur": 1, "ph": "X", "name": "ProcessMessages 97", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425397, "dur": 36, "ph": "X", "name": "ReadAsync 97", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425436, "dur": 2, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425441, "dur": 45, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425490, "dur": 3, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425495, "dur": 39, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425539, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425542, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425602, "dur": 5, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425611, "dur": 58, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425673, "dur": 4, "ph": "X", "name": "ProcessMessages 1123", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425679, "dur": 42, "ph": "X", "name": "ReadAsync 1123", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425726, "dur": 3, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425731, "dur": 41, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425776, "dur": 2, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425780, "dur": 26, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425809, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425812, "dur": 43, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425860, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425863, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425921, "dur": 3, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425926, "dur": 38, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425969, "dur": 3, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918425974, "dur": 36, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426014, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426017, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426064, "dur": 3, "ph": "X", "name": "ProcessMessages 730", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426069, "dur": 38, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426111, "dur": 3, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426116, "dur": 44, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426165, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426168, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426217, "dur": 4, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426223, "dur": 29, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426254, "dur": 2, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426258, "dur": 42, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426304, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426308, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426350, "dur": 3, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426355, "dur": 37, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426396, "dur": 3, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426401, "dur": 48, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426453, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426456, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426503, "dur": 3, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426508, "dur": 34, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426546, "dur": 3, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426551, "dur": 41, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426599, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426602, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426640, "dur": 2, "ph": "X", "name": "ProcessMessages 795", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426644, "dur": 37, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426685, "dur": 2, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426690, "dur": 45, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426739, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426744, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426789, "dur": 3, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426794, "dur": 33, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426831, "dur": 2, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426836, "dur": 42, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426882, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426886, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426926, "dur": 2, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426930, "dur": 37, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426972, "dur": 3, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918426976, "dur": 43, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427024, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427027, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427076, "dur": 3, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427081, "dur": 38, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427123, "dur": 2, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427128, "dur": 38, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427169, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427172, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427217, "dur": 3, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427221, "dur": 38, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427263, "dur": 3, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427268, "dur": 40, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427312, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427316, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427352, "dur": 2, "ph": "X", "name": "ProcessMessages 826", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427357, "dur": 34, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427395, "dur": 2, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427399, "dur": 55, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427459, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427462, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427508, "dur": 3, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427513, "dur": 29, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427545, "dur": 2, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427548, "dur": 55, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427608, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427611, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427658, "dur": 3, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427663, "dur": 35, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427703, "dur": 3, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427707, "dur": 41, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427753, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427756, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427797, "dur": 3, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427802, "dur": 37, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427843, "dur": 3, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427848, "dur": 43, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427895, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427898, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427935, "dur": 2, "ph": "X", "name": "ProcessMessages 866", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427940, "dur": 34, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427977, "dur": 2, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918427982, "dur": 55, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428041, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428044, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428091, "dur": 3, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428097, "dur": 36, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428137, "dur": 2, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428142, "dur": 45, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428191, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428194, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428241, "dur": 3, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428246, "dur": 38, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428289, "dur": 3, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428295, "dur": 36, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428335, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428339, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428379, "dur": 2, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428384, "dur": 37, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428427, "dur": 3, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428432, "dur": 61, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428500, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428508, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428577, "dur": 4, "ph": "X", "name": "ProcessMessages 1214", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428583, "dur": 44, "ph": "X", "name": "ReadAsync 1214", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428631, "dur": 1, "ph": "X", "name": "ProcessMessages 118", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428634, "dur": 54, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428693, "dur": 3, "ph": "X", "name": "ProcessMessages 901", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428699, "dur": 39, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428743, "dur": 2, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428747, "dur": 26, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428776, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428779, "dur": 44, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428827, "dur": 3, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428832, "dur": 42, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428878, "dur": 3, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428883, "dur": 54, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428942, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428945, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428993, "dur": 3, "ph": "X", "name": "ProcessMessages 882", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918428998, "dur": 36, "ph": "X", "name": "ReadAsync 882", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429038, "dur": 2, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429042, "dur": 55, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429102, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429106, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429155, "dur": 3, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429160, "dur": 40, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429206, "dur": 4, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429213, "dur": 54, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429271, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429274, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429323, "dur": 3, "ph": "X", "name": "ProcessMessages 908", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429328, "dur": 34, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429366, "dur": 2, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429370, "dur": 43, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429417, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429421, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429469, "dur": 3, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429474, "dur": 38, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429516, "dur": 3, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429520, "dur": 41, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429565, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429568, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429612, "dur": 3, "ph": "X", "name": "ProcessMessages 812", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429617, "dur": 37, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429658, "dur": 3, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429663, "dur": 47, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429715, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429718, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429756, "dur": 2, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429763, "dur": 33, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429801, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429805, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429838, "dur": 1, "ph": "X", "name": "ProcessMessages 104", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429842, "dur": 39, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429885, "dur": 3, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429890, "dur": 42, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429936, "dur": 3, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429941, "dur": 35, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429980, "dur": 3, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918429985, "dur": 35, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430025, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430030, "dur": 44, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430078, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430081, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430128, "dur": 3, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430133, "dur": 36, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430173, "dur": 2, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430177, "dur": 50, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430232, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430236, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430286, "dur": 3, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430291, "dur": 34, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430329, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430333, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430370, "dur": 4, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430376, "dur": 35, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430416, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430419, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430467, "dur": 4, "ph": "X", "name": "ProcessMessages 806", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430473, "dur": 38, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430515, "dur": 3, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430520, "dur": 45, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430569, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430572, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430616, "dur": 3, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430621, "dur": 40, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430665, "dur": 3, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430670, "dur": 42, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430716, "dur": 3, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430721, "dur": 37, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430762, "dur": 3, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430767, "dur": 35, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430806, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430810, "dur": 82, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430896, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430900, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430946, "dur": 3, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430952, "dur": 40, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918430995, "dur": 2, "ph": "X", "name": "ProcessMessages 788", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918431000, "dur": 41, "ph": "X", "name": "ReadAsync 788", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918431046, "dur": 3, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918431051, "dur": 38, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918431092, "dur": 3, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918431097, "dur": 35, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918431136, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918431141, "dur": 52, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918431198, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918431201, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918431260, "dur": 5, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918431269, "dur": 123, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918431399, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918431407, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918431457, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918431464, "dur": 450, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918431924, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918431930, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918431992, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918431999, "dur": 49, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432054, "dur": 4, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432061, "dur": 50, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432120, "dur": 5, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432129, "dur": 57, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432196, "dur": 6, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432206, "dur": 57, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432270, "dur": 6, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432280, "dur": 52, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432337, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432345, "dur": 47, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432400, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432407, "dur": 52, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432468, "dur": 6, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432478, "dur": 62, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432546, "dur": 6, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432556, "dur": 56, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432621, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432630, "dur": 54, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432692, "dur": 5, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432700, "dur": 44, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432752, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432757, "dur": 51, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432815, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432822, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432876, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432883, "dur": 49, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432941, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918432948, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433006, "dur": 6, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433016, "dur": 64, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433087, "dur": 6, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433097, "dur": 53, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433155, "dur": 5, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433163, "dur": 43, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433213, "dur": 5, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433222, "dur": 57, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433288, "dur": 7, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433299, "dur": 40, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433344, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433352, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433393, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433400, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433454, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433462, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433497, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433504, "dur": 44, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433556, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433564, "dur": 59, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433630, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433637, "dur": 54, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433700, "dur": 6, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433710, "dur": 41, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433756, "dur": 6, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433767, "dur": 45, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433819, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433828, "dur": 48, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433883, "dur": 6, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433893, "dur": 53, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433954, "dur": 6, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918433964, "dur": 43, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434012, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434020, "dur": 50, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434077, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434084, "dur": 54, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434146, "dur": 7, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434157, "dur": 54, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434219, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434228, "dur": 64, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434300, "dur": 6, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434310, "dur": 51, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434367, "dur": 6, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434377, "dur": 50, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434435, "dur": 6, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434446, "dur": 56, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434509, "dur": 6, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434519, "dur": 38, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434562, "dur": 5, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434570, "dur": 44, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434621, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434628, "dur": 56, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434691, "dur": 6, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434701, "dur": 54, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434763, "dur": 7, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434775, "dur": 58, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434839, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434848, "dur": 51, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434906, "dur": 6, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434915, "dur": 55, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434988, "dur": 6, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918434998, "dur": 53, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918435060, "dur": 6, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918435070, "dur": 51, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918435127, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918435136, "dur": 48, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918435192, "dur": 4, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918435199, "dur": 49, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918435255, "dur": 4, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918435262, "dur": 49, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918435318, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918435326, "dur": 54, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918435387, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918435396, "dur": 49, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918435452, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918435461, "dur": 51, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918435518, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918435527, "dur": 50, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918435584, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918435591, "dur": 401, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918435999, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918436005, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918436062, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918436068, "dur": 6434, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918442513, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918442521, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918442589, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918442598, "dur": 328, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918442932, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918442938, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918442996, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918443004, "dur": 50, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918443063, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918443069, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918443180, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918443187, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918443247, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918443253, "dur": 5868, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918449132, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918449140, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918449210, "dur": 5, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918449219, "dur": 67, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918449296, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918449302, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918449366, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918449372, "dur": 274, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918449654, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918449660, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918449728, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918449735, "dur": 2187, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918451931, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918451937, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918452004, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918452012, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918452074, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918452081, "dur": 169, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918452258, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918452264, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918452307, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918452313, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918452376, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918452385, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918452443, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918452450, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918452512, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918452519, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918452612, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918452619, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918452678, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918452685, "dur": 632, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918453325, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918453332, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918453399, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918453406, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918453468, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918453476, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918453552, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918453556, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918453617, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918453625, "dur": 133, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918453766, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918453772, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918453829, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918453836, "dur": 97, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918453939, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918453944, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918454006, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918454013, "dur": 53, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918454073, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918454079, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918454139, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918454146, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918454210, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918454218, "dur": 80, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918454306, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918454312, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918454371, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918454378, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918454413, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918454418, "dur": 67, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918454491, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918454496, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918454551, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918454558, "dur": 83, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918454649, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918454656, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918454715, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918454723, "dur": 447, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918455177, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918455183, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918455240, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918455247, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918455305, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918455313, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918455387, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918455393, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918455433, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918455440, "dur": 66, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918455513, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918455519, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918455578, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918455585, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918455629, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918455635, "dur": 333, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918455976, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918455983, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918456025, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918456033, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918456106, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918456112, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918456169, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918456177, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918456239, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918456247, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918456305, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918456313, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918456371, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918456380, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918456439, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918456446, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918456498, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918456503, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918456559, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918456566, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918456625, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918456634, "dur": 461, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918457102, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918457107, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918457161, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918457168, "dur": 153, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918457329, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918457336, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918457395, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918457402, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918457468, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918457476, "dur": 168, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918457653, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918457661, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918457725, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918457732, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918457791, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918457799, "dur": 557, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918458370, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918458378, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918458426, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918458433, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918458508, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918458514, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918458572, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918458578, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918458663, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918458670, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918458732, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918458739, "dur": 1211, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918459959, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918459966, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918460036, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918460044, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918460109, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918460116, "dur": 166, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918460291, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918460298, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918460362, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918460370, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918460434, "dur": 6, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918460445, "dur": 509, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918460963, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918460970, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918461042, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918461049, "dur": 2034, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918463092, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918463099, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918463165, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918463172, "dur": 164, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918463343, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918463348, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918463408, "dur": 4, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918463414, "dur": 48, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918463468, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918463474, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918463588, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918463593, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918463672, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918463678, "dur": 273, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918463959, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918463964, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464022, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464027, "dur": 47, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464080, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464084, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464123, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464128, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464184, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464191, "dur": 168, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464366, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464372, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464413, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464419, "dur": 287, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464714, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464719, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464759, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464764, "dur": 48, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464818, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464823, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464873, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464878, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464916, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464921, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464971, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918464976, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918465024, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918465029, "dur": 166, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918465202, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918465207, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918465247, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918465253, "dur": 93, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918465355, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918465364, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918465423, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918465428, "dur": 375, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918465812, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918465819, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918465879, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918465886, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918465945, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918465951, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466008, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466014, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466069, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466076, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466130, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466138, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466192, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466199, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466254, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466261, "dur": 120, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466387, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466393, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466452, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466459, "dur": 98, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466564, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466569, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466624, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466631, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466685, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466691, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466747, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466753, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466807, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466814, "dur": 52, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466874, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466881, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466945, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918466953, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467026, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467031, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467095, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467102, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467143, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467148, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467261, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467266, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467332, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467340, "dur": 85, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467430, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467435, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467505, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467513, "dur": 163, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467683, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467689, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467747, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467755, "dur": 52, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467812, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467817, "dur": 80, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467906, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467912, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467968, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918467973, "dur": 47, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468024, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468027, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468149, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468154, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468188, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468193, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468293, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468297, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468333, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468338, "dur": 86, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468430, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468437, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468497, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468505, "dur": 59, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468571, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468577, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468642, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468647, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468699, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468703, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468752, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468757, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468797, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468801, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468840, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918468844, "dur": 465, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918469318, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918469324, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918469390, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918469398, "dur": 90, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918469496, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918469503, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918469566, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918469571, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918469618, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918469622, "dur": 448, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918470077, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918470083, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918470136, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918470142, "dur": 1044, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918471192, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918471196, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918471258, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918471267, "dur": 43313, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918514592, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918514600, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918514668, "dur": 38, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918514709, "dur": 6272, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918520993, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918521001, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918521049, "dur": 5, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918521057, "dur": 315, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918521382, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918521388, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918521422, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918521426, "dur": 1461, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918522896, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918522902, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918522939, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918522945, "dur": 49, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918523002, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918523009, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918523070, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918523078, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918523112, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918523117, "dur": 388, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918523512, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918523517, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918523548, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918523553, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918523616, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918523620, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918523669, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918523676, "dur": 1129, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918524812, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918524817, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918524850, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918524857, "dur": 81, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918524945, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918524951, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918524985, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918524990, "dur": 123, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918525120, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918525125, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918525156, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918525161, "dur": 131, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918525300, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918525305, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918525335, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918525341, "dur": 263, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918525613, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918525619, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918525675, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918525681, "dur": 489, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918526178, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918526184, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918526234, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918526241, "dur": 448, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918526695, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918526700, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918526732, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918526736, "dur": 321, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918527064, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918527069, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918527119, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918527125, "dur": 61, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918527193, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918527200, "dur": 222, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918527427, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918527432, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918527482, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918527489, "dur": 343, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918527842, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918527848, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918527882, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918527887, "dur": 708, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918528603, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918528608, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918528661, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918528667, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918528700, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918528704, "dur": 423, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529133, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529138, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529191, "dur": 5, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529201, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529240, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529247, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529286, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529294, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529332, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529338, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529388, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529396, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529454, "dur": 5, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529462, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529509, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529516, "dur": 35, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529557, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529564, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529617, "dur": 5, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529626, "dur": 33, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529663, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529669, "dur": 34, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529711, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529718, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529753, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529759, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529802, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529808, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529862, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529870, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529919, "dur": 6, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529929, "dur": 41, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529974, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918529981, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918530034, "dur": 5, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918530043, "dur": 54, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918530102, "dur": 5, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918530111, "dur": 51, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918530170, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918530179, "dur": 69, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918530255, "dur": 6, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918530265, "dur": 46, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918530315, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918530324, "dur": 110, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918530443, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918530450, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918530488, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918530495, "dur": 167, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918530671, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918530676, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918530708, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918530712, "dur": 275, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918530991, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918530995, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918531039, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918531043, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918531089, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419918531095, "dur": 605221, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419919136331, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419919136339, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419919136378, "dur": 1472, "ph": "X", "name": "ProcessMessages 2513", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419919137856, "dur": 969213, "ph": "X", "name": "ReadAsync 2513", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920107084, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920107092, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920107137, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920107143, "dur": 2358, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920109511, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920109519, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920109553, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920109558, "dur": 277587, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920387158, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920387166, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920387206, "dur": 35, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920387244, "dur": 14535, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920401794, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920401802, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920401844, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920401850, "dur": 2120, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920403981, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920403987, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920404046, "dur": 42, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920404091, "dur": 186258, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920590366, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920590375, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920590415, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920590421, "dur": 1085, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920591517, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920591524, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920591589, "dur": 43, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920591634, "dur": 52234, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920643882, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920643889, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920643926, "dur": 287, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419920644222, "dur": 603345, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921247581, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921247588, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921247625, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921247632, "dur": 69761, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921317407, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921317415, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921317457, "dur": 35, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921317496, "dur": 13593, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921331101, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921331108, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921331143, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921331149, "dur": 888, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921332047, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921332053, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921332104, "dur": 37, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921332144, "dur": 263637, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921595797, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921595805, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921595908, "dur": 8, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921595920, "dur": 1069, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921596999, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921597005, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921597073, "dur": 53, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921597131, "dur": 568, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921597706, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921597711, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921597752, "dur": 3, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 14060, "tid": 25769803776, "ts": 1748419921597758, "dur": 10125, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 14060, "tid": 435848, "ts": 1748419921608654, "dur": 8818, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 14060, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 14060, "tid": 21474836480, "ts": 1748419918390319, "dur": 198526, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 14060, "tid": 21474836480, "ts": 1748419918588848, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 14060, "tid": 21474836480, "ts": 1748419918588853, "dur": 102, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 14060, "tid": 435848, "ts": 1748419921617478, "dur": 19, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 14060, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 14060, "tid": 17179869184, "ts": 1748419918386406, "dur": 3221524, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 14060, "tid": 17179869184, "ts": 1748419918386593, "dur": 3253, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 14060, "tid": 17179869184, "ts": 1748419921607933, "dur": 82, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 14060, "tid": 17179869184, "ts": 1748419921607951, "dur": 25, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 14060, "tid": 17179869184, "ts": 1748419921608018, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 14060, "tid": 435848, "ts": 1748419921617502, "dur": 24, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748419918404730, "dur": 2260, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419918407002, "dur": 1017, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419918408185, "dur": 91, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748419918408276, "dur": 358, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419918421476, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748419918408656, "dur": 22878, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419918431550, "dur": 3165818, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419921597370, "dur": 368, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419921597914, "dur": 71, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419921598023, "dur": 2530, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748419918408843, "dur": 22720, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918431585, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918431778, "dur": 577, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1748419918431716, "dur": 639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_C1B7931C83CAC136.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419918432669, "dur": 357, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_1D361BA92457D64F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419918433030, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419918433028, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_140A21A116E2DF85.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419918433219, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_140A21A116E2DF85.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419918433394, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419918433805, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419918434889, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419918434954, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419918435017, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419918435074, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419918435226, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419918435334, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419918435391, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419918435520, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419918435624, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419918435684, "dur": 245, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419918436094, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419918436306, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419918436799, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419918437160, "dur": 930, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419918438245, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419918438428, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419918438780, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419918442242, "dur": 455, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\MonoBehaviourTest\\IMonoBehaviourTest.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918442831, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\StacktraceFilter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918433551, "dur": 9591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419918443147, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918443311, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419918445598, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\GUI\\TestListTreeView\\TestListTreeViewGUI.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918448770, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\UnityTestProtocol\\TestState.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918448874, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\UnityTestProtocol\\UnityTestProtocolListener.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918448984, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\UnityTestProtocol\\UnityTestProtocolStarter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918449055, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\UnityTestProtocol\\UtpDebuglogger.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918449137, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\UnityTestProtocol\\UtpMessageReporter.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918443495, "dur": 5717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419918449213, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918449438, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918449615, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419918450792, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\ContentSizeFitterEditor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918450874, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\DropdownEditor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918451286, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\PropertyDrawers\\ColorBlockDrawer.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918449962, "dur": 1777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419918451741, "dur": 484, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918452255, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918452391, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419918453499, "dur": 246, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputActionParameters.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918453747, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputActionPhase.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918453799, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputActionProperty.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918453851, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputActionRebindingExtensions.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918453910, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputActionReference.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918454041, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputActionSetupExtensions.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918454110, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputActionState.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918454299, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputActionTrace.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918454453, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputActionType.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918454513, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputBinding.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918454577, "dur": 186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputBindingComposite.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918454765, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputBindingCompositeContext.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918454834, "dur": 407, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputBindingResolver.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918455464, "dur": 251, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\Interactions\\TapInteraction.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918455830, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\CommonUsages.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918455952, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\DeltaControl.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918456446, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\KeyControl.cs"}}, {"pid": 12345, "tid": 1, "ts": ****************, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Devices\\Commands\\DisableDeviceCommand.cs"}}, {"pid": 12345, "tid": 1, "ts": ****************, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Devices\\Commands\\InitiateUserAccountPairingCommand.cs"}}, {"pid": 12345, "tid": 1, "ts": ****************, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Devices\\Commands\\QueryKeyboardLayoutCommand.cs"}}, {"pid": 12345, "tid": 1, "ts": ****************, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Devices\\Commands\\SetSamplingFrequencyCommand.cs"}}, {"pid": 12345, "tid": 1, "ts": ****************, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Devices\\Commands\\UseWindowsGamingInputCommand.cs"}}, {"pid": 12345, "tid": 1, "ts": ****************, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Devices\\Mouse.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918460074, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Editor\\Internal\\AdvancedDropdown\\AdvancedDropdownDataSource.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918461440, "dur": 184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Editor\\UITKAssetEditor\\Views\\ActionsTreeView.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918462226, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Events\\DeviceRemoveEvent.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918462469, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Events\\InputEventListener.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918462643, "dur": 391, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\InputMetrics.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419918452567, "dur": 10730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419918463298, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918463471, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419918463647, "dur": 1302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419918464950, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918465056, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419918465182, "dur": 874, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419918466056, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918466153, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419918466258, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419918466640, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918466746, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419918466879, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419918467255, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918467402, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419918467568, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419918467958, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918468086, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419918468212, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419918468535, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918468644, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419918468752, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419918469047, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918469135, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918469716, "dur": 49474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918519198, "dur": 2040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419918521239, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918521346, "dur": 1861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419918523209, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918523425, "dur": 2114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419918525540, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918525623, "dur": 1974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419918527598, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918527716, "dur": 437, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419918529681, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419918528157, "dur": 2124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419918530286, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918530549, "dur": 776, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419918531371, "dur": 3066024, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918408940, "dur": 22665, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918431627, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419918431701, "dur": 756, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1748419918431611, "dur": 854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_8F12278583549999.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419918432502, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_8F12278583549999.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419918432858, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918433018, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419918433016, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_C8C0272C381EE5BB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419918433510, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918433936, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419918434580, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419918434896, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918435059, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918435206, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419918435284, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918435510, "dur": 196, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419918435707, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7045246832102146154.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419918435847, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918436909, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918437502, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918438064, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918438622, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918439273, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918439839, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918440424, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918441006, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918441703, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918442809, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918443742, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918444339, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918444994, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918445695, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918446349, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918447040, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918447726, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918448428, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918449109, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918449888, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918450510, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918451084, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918452638, "dur": 936, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Utilities\\XColor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419918451684, "dur": 2407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918454092, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419918455328, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\AssetOverlays\\Cache\\LocalStatusCache.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419918455506, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\AssetsUtils\\GetSelectedPaths.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419918456298, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Configuration\\CloudEdition\\Welcome\\WaitingSignInPanel.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419918456358, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Configuration\\ConfigurePartialWorkspace.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419918456457, "dur": 246, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Configuration\\CredentialsUIImpl.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419918456751, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Configuration\\MissingEncryptionPasswordPromptHandler.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419918456850, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Configuration\\TeamEdition\\TeamEditionConfigurationWindow.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419918457373, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\EnumExtensions.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419918457476, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\ExternalLink.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419918457671, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Gluon\\ProgressOperationHandler.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419918457861, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Gluon\\UpdateReport\\UpdateReportDialog.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419918458751, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Preferences\\PendingChangesOptionsFoldout.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419918459066, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Tool\\BringWindowToFront.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419918460078, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\UI\\GuiEnabled.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419918461335, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\UI\\UnityMenuItem.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419918462670, "dur": 380, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Views\\CreateWorkspace\\PerformInitialCheckin.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419918463281, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Views\\Diff\\MergeCategoryTreeViewItem.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419918454263, "dur": 9327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419918463591, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918463731, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419918463902, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419918464404, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918464517, "dur": 2231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918466753, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419918466906, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918467115, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Profiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419918467626, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918467797, "dur": 1911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918469708, "dur": 49486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918519195, "dur": 1985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419918521182, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918521328, "dur": 3892, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419918525956, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.DataAnnotations.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419918521279, "dur": 5996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419918527276, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918527468, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419918527385, "dur": 2216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419918529602, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419918530559, "dur": 617567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419919148130, "dur": 958894, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419919148129, "dur": 960305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419920109611, "dur": 180, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419920109841, "dur": 277612, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419920401630, "dur": 188965, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419920401628, "dur": 188970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419920590621, "dur": 1209, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419920591836, "dur": 1005549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918409028, "dur": 22624, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918431671, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918431745, "dur": 515, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1748419918431658, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_3B5B0D1C936F55AC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419918432308, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918432306, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_0CB3585AEAFFAB32.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419918432373, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918432461, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918432458, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_99F5CB938A8C7E39.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419918432632, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918432630, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D9274EFAD2DC078D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419918432773, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918432836, "dur": 266, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918432834, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_3C31BD4786B00292.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419918433246, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_5379AC35045B5B90.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419918433332, "dur": 619, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_5379AC35045B5B90.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ****************, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": ****************, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": ****************, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Mapbox\\Core\\Plugins\\Mapbox\\MapboxAccounts\\net35\\MapboxAccountsUnity.dll"}}, {"pid": 12345, "tid": 3, "ts": ****************, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Mapbox\\Core\\Plugins\\Mapbox\\vector-tile-cs\\net46\\Mapbox.VectorTile.Geometry.dll"}}, {"pid": 12345, "tid": 3, "ts": ****************, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Mapbox\\Core\\Plugins\\Mapbox\\vector-tile-cs\\net46\\Mapbox.VectorTile.PbfReader.dll"}}, {"pid": 12345, "tid": 3, "ts": ****************, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Mapbox\\Core\\Plugins\\Mapbox\\vector-tile-cs\\net46\\Mapbox.VectorTile.VectorTileReader.dll"}}, {"pid": 12345, "tid": 3, "ts": ****************, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Mapbox\\Core\\Plugins\\ThirdParty\\Mapbox.Json\\Net35\\Mapbox.Json.dll"}}, {"pid": 12345, "tid": 3, "ts": ****************, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918434965, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918435036, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918435152, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918435266, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918435333, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918435393, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918435449, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918435616, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918435671, "dur": 262, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918436081, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918436283, "dur": 886, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918438245, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918438520, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918438614, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\mscorlib.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918438784, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918438835, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918439126, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Transactions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918441681, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\SetPropertyUtility.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419918434161, "dur": 8105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748419918442267, "dur": 520, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918442869, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918443735, "dur": 1879, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\IntermediateTextureMode.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419918445615, "dur": 3741, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\ForwardRendererData.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419918449391, "dur": 1952, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\External\\LibTessDotNet\\Tess.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419918443676, "dur": 8056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918451816, "dur": 509, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Utilities\\IIdentifiable.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419918453727, "dur": 2377, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Unity\\SingletonAttribute.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419918456396, "dur": 1365, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Unity\\MacroScriptableObject.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419918451733, "dur": 6028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918457762, "dur": 2410, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\AttributeUtility.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419918457762, "dur": 3487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918461250, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918462298, "dur": 989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918463287, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918463479, "dur": 1032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918464511, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918465070, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918465128, "dur": 1618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918466748, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419918466887, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918466986, "dur": 886, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748419918467873, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918468034, "dur": 1655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918469689, "dur": 49484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918519182, "dur": 2020, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419918521203, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918521327, "dur": 733, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918521311, "dur": 2559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419918523871, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918523945, "dur": 1908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Google.Android.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419918525853, "dur": 632, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918527446, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918526497, "dur": 2184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Simulator.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419918528682, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918530437, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Pipelines.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419918528919, "dur": 2314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419918531234, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419918531367, "dur": 3065996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918408893, "dur": 22685, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918431588, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918431771, "dur": 435, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1748419918431712, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_1D4B133AF68A73C6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419918432357, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918432459, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_9CE78850A812468C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419918432528, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_0C40C310871AF0F2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419918432784, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_64F63BE84EA9CE17.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419918432843, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748419918432841, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_E11C5609C2206DA8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419918432964, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Gradle.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748419918432962, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_B2A85112FF760EF8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419918433246, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918433338, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748419918433336, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_4E55F340B0300207.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419918433396, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918433920, "dur": 426, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419918434467, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419918434582, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419918434637, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.UI.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419918434840, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918435018, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419918435269, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918435618, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918435747, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8213421625503935943.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419918435812, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918435876, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918436681, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918437295, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918437877, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918438467, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918439143, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918439734, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918440284, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918440872, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918441419, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918442260, "dur": 1494, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@14.0.11\\Editor\\Models\\Blocks\\Implementations\\Spawn\\VFXSpawnerConstantRate.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748419918442035, "dur": 2055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918444090, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918444647, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918445181, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918445753, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918446292, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918446839, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918447422, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918447953, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918448494, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918449079, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918450053, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918450632, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918451292, "dur": 516, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Control\\SwitchOnEnum.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748419918451207, "dur": 1544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918452753, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419918453641, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Animation\\ICurvesOwner.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748419918453909, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\AssetUpgrade\\TimelineUpgrade.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748419918454303, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Evaluation\\InfiniteRuntimeClip.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748419918454358, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Evaluation\\IntervalTree.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748419918454553, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Evaluation\\RuntimeElement.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748419918454863, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Events\\Signals\\SignalEmitter.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748419918454929, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Events\\Signals\\SignalReceiver.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748419918454998, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Events\\SignalTrack.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748419918455489, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Scripting\\PlayableTrack.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748419918455605, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\TimelineAsset.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748419918455696, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\TimelineAsset_CreateRemove.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748419918455931, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Utilities\\AnimationPreviewUtilities.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748419918456061, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Utilities\\FrameRate.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748419918452924, "dur": 3480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419918456405, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918456552, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419918457424, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Manipulators\\Move\\MoveItemModeMix.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748419918456732, "dur": 1194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419918457927, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918458053, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918459133, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918459704, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918460623, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918461447, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918461603, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918462294, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918463315, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918463474, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419918463661, "dur": 713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419918464375, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918464511, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419918464680, "dur": 762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419918465442, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918465536, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419918465659, "dur": 1380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419918467040, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918467167, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419918467340, "dur": 1347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419918468688, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918468795, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419918468966, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419918469545, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918469665, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419918469804, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419918470287, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918470390, "dur": 942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419918471338, "dur": 151, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419918471599, "dur": 665090, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419919148453, "dur": 958124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748419919148122, "dur": 958634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419920107250, "dur": 79, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419920107346, "dur": 536824, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419920649671, "dur": 595209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748419920649669, "dur": 596814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748419921247655, "dur": 179, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419921247852, "dur": 69846, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748419921331233, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748419921331232, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748419921331393, "dur": 969, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748419921332365, "dur": 265021, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918408927, "dur": 22663, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918431613, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748419918431708, "dur": 701, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1748419918431595, "dur": 815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_42F6E26E5392E818.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419918432444, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_42F6E26E5392E818.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419918432787, "dur": 235, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_B9BA886FA66D1477.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419918433027, "dur": 219, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748419918433025, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_6B72B6C78467EF61.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419918433914, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748419918434597, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748419918435001, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748419918435812, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918436607, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918437210, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918437813, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918438354, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918438936, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918439519, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918440070, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918440639, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918441714, "dur": 2102, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@14.0.11\\Editor\\Models\\Operators\\Implementations\\Sequential3D.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748419918441197, "dur": 2690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918443887, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918444419, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918444969, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918445499, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918446044, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918446596, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918447143, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918447744, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918448271, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918448828, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918449491, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918450063, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918450617, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918451184, "dur": 1430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918452615, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419918453254, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.profiling.core@1.0.2\\Runtime\\ProfilerCategory.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748419918453327, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.profiling.core@1.0.2\\Runtime\\ProfilerCounter.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748419918453394, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.profiling.core@1.0.2\\Runtime\\ProfilerCounterValue.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748419918453478, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.profiling.core@1.0.2\\Runtime\\ProfilerMarker1Param.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748419918453563, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.profiling.core@1.0.2\\Runtime\\ProfilerMarker2Params.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748419918453622, "dur": 296, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.profiling.core@1.0.2\\Runtime\\ProfilerMarker3Params.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748419918452780, "dur": 1150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419918453931, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918454115, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419918454304, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419918454499, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419918454682, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419918454852, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419918455445, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918455700, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748419918455560, "dur": 672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419918456232, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918456347, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918456648, "dur": 795, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748419918457619, "dur": 2136, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\ExclusiveOrHandler.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748419918457445, "dur": 3124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918460574, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918461625, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918462355, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918463174, "dur": 321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918463496, "dur": 1017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918464513, "dur": 2240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918466754, "dur": 2917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918469671, "dur": 49505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918519178, "dur": 2005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419918521184, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918521274, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419918521340, "dur": 1843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419918523184, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918523267, "dur": 1914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419918525183, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918525478, "dur": 1907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419918527385, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918528163, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748419918527754, "dur": 2306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419918530061, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918530230, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419918530591, "dur": 1871042, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419920401637, "dur": 409, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748419920401635, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748419920402102, "dur": 2185, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1748419920404290, "dur": 1193080, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918408982, "dur": 22637, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918431643, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748419918431704, "dur": 521, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1748419918431626, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4C09D1522AE95AC4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419918432284, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748419918432282, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_86E0155FB5F99D0A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419918432983, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748419918432981, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_0C0EDCF4D8373D10.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419918433116, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918433353, "dur": 1020, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918434379, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918434452, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918434514, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918434583, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918434775, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918434831, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Profiler.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748419918435033, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918435360, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918435555, "dur": 744, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918436308, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918436863, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918437451, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918438061, "dur": 498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918438559, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918439112, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918439605, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918440117, "dur": 512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918440629, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918441135, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918441646, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918442256, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918443296, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918443826, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918444375, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918444919, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918445453, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918445973, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918446516, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918447059, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918447595, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918448132, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918448692, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918449235, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918449836, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918450402, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918450977, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918451564, "dur": 1855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918453457, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419918454305, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Runtime\\Attributes\\MinAttribute.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748419918454395, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Runtime\\Attributes\\PostProcessAttribute.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748419918454548, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Runtime\\Effects\\AutoExposure.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748419918454615, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Runtime\\Effects\\Bloom.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748419918454693, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Runtime\\Effects\\ChromaticAberration.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748419918454836, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Runtime\\Effects\\ColorGrading.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748419918454890, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Runtime\\Effects\\DepthOfField.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748419918455019, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Runtime\\Effects\\Fog.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748419918455179, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Runtime\\Effects\\MotionBlur.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748419918455338, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Runtime\\Effects\\SubpixelMorphologicalAntialiasing.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748419918455482, "dur": 871, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Runtime\\Monitors\\HistogramMonitor.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748419918456355, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Runtime\\Monitors\\LightMeterMonitor.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748419918456739, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Runtime\\PostProcessEffectSettings.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748419918457402, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Runtime\\Utils\\TargetPool.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748419918453673, "dur": 3903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419918457581, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918457729, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419918457957, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419918458546, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918458668, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918458723, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918460071, "dur": 637, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsObjectProcessor.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748419918459458, "dur": 1251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918460709, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918461590, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918462141, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918462674, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918462964, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918463485, "dur": 1023, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918464510, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419918464668, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419918464995, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918465062, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419918465198, "dur": 1144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419918466343, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918466443, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419918466557, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419918467136, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918467239, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419918467357, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419918467820, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918468083, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419918468269, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419918468746, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918468936, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918469671, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419918469859, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918469916, "dur": 49265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918519186, "dur": 2023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419918521211, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918521357, "dur": 1902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419918523260, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918523371, "dur": 1995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419918525371, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918525438, "dur": 1933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.UI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419918527371, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918527553, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.UI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419918529680, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Mapbox\\Core\\Plugins\\ThirdParty\\Mapbox.IO.Compression\\net35\\Mapbox.IO.Compression.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748419918527757, "dur": 2043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419918529800, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918529927, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918530111, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918530305, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918530363, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419918530599, "dur": 2800639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419921331241, "dur": 264785, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748419921331240, "dur": 264789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748419921596055, "dur": 1263, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419918409015, "dur": 22620, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918431661, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419918431731, "dur": 578, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1748419918431642, "dur": 669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_F372D6AB03EB9C8E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419918432801, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419918432799, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_5F7426AD1A2DD658.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419918432989, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419918432987, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_0D59EE4A35C68DB6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419918433302, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918433371, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918433447, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419918433567, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918433690, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918433751, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918433862, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419918433921, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918433987, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918434052, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918434111, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918434395, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918434693, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918434842, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918434926, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419918435831, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918436619, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918437178, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918437765, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918438338, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918438920, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918439486, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918440062, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918440642, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918441191, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918441796, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918442788, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918443531, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918444158, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918444713, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918445578, "dur": 2257, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Manipulators\\Utils\\EditModeReplaceUtils.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748419918445257, "dur": 2780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918448037, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918448588, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918449174, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918449973, "dur": 1369, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.searcher@4.9.2\\Editor\\Searcher\\SearcherDatabaseBase.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748419918449722, "dur": 1909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918451631, "dur": 1689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918453322, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419918453648, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918453709, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419918454266, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918454379, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918454453, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419918454620, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419918454807, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419918455382, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918455700, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419918455487, "dur": 696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419918456184, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918456294, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918456503, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918456795, "dur": 1234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918458132, "dur": 1642, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Listeners\\UIInterfaces\\UnityOnSubmitMessageListener.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748419918458030, "dur": 2488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918460671, "dur": 1830, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Attributes\\VisualScriptingHelpURLAttribute.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748419918460518, "dur": 2673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918463192, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918463473, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419918463658, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419918464280, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918464444, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918464519, "dur": 2233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918466753, "dur": 655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918467409, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419918467596, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419918468121, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918468294, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419918468459, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419918468908, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918469075, "dur": 600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918469676, "dur": 51543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918521220, "dur": 1885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Google.Android.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748419918523106, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918525141, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419918523430, "dur": 1940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748419918525371, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918525459, "dur": 1889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748419918527349, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918527414, "dur": 1905, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748419918529320, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918529975, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918530073, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918530540, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419918531039, "dur": 3066326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918409067, "dur": 22598, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918431687, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748419918431804, "dur": 485, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1748419918431671, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_61E29B66A87F06EA.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419918432337, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748419918432335, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_1FF66EA0847C44F7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419918432438, "dur": 217, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_1FF66EA0847C44F7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419918432976, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Types.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748419918432974, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_B19AEDDBA5CF00C4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419918433127, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918433197, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_B19AEDDBA5CF00C4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419918433905, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748419918434269, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918434472, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748419918434882, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918435837, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918436611, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918437176, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918437767, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918438321, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918438891, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918439493, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918440048, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918440612, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918441174, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918441761, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918442777, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918443329, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918443881, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918444442, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918445008, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918445543, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918446063, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918446620, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918447154, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918447817, "dur": 2203, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Graphs\\IMaterialSlotHasValue.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748419918447758, "dur": 2771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918450530, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918451128, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918452187, "dur": 1830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918454018, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419918454161, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918454914, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Runtime\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748419918454965, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Runtime\\FastAction.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748419918455083, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Runtime\\ITextPreProcessor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748419918455250, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Runtime\\TextMeshProUGUI.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748419918455484, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Runtime\\TMP_Character.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748419918455833, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Runtime\\TMP_FontFeaturesCommon.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748419918455885, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Runtime\\TMP_FontFeatureTable.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748419918456061, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Runtime\\TMP_InputValidator.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748419918456346, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Runtime\\TMP_PackageResourceImporter.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748419918456422, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.9\\Scripts\\Runtime\\TMP_ResourcesManager.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748419918454258, "dur": 3046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419918457305, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918457453, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419918457705, "dur": 440, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748419918457640, "dur": 1033, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419918458674, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918458828, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918458975, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918459666, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918460565, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918461339, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918461918, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918462630, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918463345, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918463480, "dur": 1032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918464512, "dur": 1648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918466162, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419918466304, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918466360, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419918466946, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918467108, "dur": 2591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918469700, "dur": 49501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918519203, "dur": 1980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419918521184, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918521373, "dur": 1873, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419918523247, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918523327, "dur": 1849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419918525177, "dur": 2283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918527460, "dur": 2227, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419918529692, "dur": 90, "ph": "X", "name": "CheckPristineOutputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748419918529783, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918529993, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918530185, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AdaptivePerformance.Profiler.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1748419918530368, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918530473, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918530553, "dur": 58724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918589279, "dur": 5902, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419918595183, "dur": 3002210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918409101, "dur": 22579, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918431701, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419918431755, "dur": 573, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1748419918431686, "dur": 644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_4764A97CA20C124C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748419918432498, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419918432496, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_DE2A9AC6D714602F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748419918432565, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918432722, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419918432720, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_183139F0BCFA56D6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748419918432959, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918433087, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419918433086, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B7E76160FABBEBBF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748419918433255, "dur": 353, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419918433252, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748419918433612, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918433681, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419918433679, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_8CDD503792CD44AF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748419918433776, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918433882, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_8CDD503792CD44AF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748419918433939, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1748419918434334, "dur": 530, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1748419918435234, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918435427, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918435751, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918435816, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918436719, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mapbox\\Unity\\MeshGeneration\\LayerVisualizers\\LocationPrefabsLayerVisualizer.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748419918436719, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918437793, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918438437, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918439109, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918439572, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918440132, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918440701, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918441260, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918441884, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918442908, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918443446, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918444027, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918444561, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918445108, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918445637, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918446159, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918446708, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918447240, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918447783, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918448321, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918448884, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918449475, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918450283, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918450862, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918451445, "dur": 1963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918453410, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748419918453889, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419918454267, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419918453633, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748419918454419, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918454672, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918454738, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748419918454959, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918455017, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748419918455559, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918455703, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748419918456483, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Microsoft.CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419918455948, "dur": 807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748419918456756, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918456887, "dur": 1473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918458360, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918459393, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918460049, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918460772, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918461592, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918462279, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918462476, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918463315, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918463487, "dur": 1027, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918464514, "dur": 2244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918466759, "dur": 2911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918469670, "dur": 49509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918519181, "dur": 2009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748419918521191, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918521327, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419918521288, "dur": 1942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748419918523231, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918523323, "dur": 1835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748419918525159, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918525262, "dur": 2016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748419918527279, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918527407, "dur": 2037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Simulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748419918529445, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918529727, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918530195, "dur": 187, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AdaptivePerformance.Profiler.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419918530557, "dur": 64635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419918595193, "dur": 3002199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918409137, "dur": 22558, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918431762, "dur": 654, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1748419918431700, "dur": 718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_91C7B02DD7E937E1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748419918432568, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419918432566, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_C6EAA360B1853425.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748419918432774, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_187779C7F7B3B4CE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748419918432832, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419918432830, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_7FEBE6A2654794CE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748419918432990, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918433122, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419918433120, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_5DDE8E3DC54C0DB1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748419918433589, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1748419918434012, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918434174, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918434248, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918434332, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918434837, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918435821, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918436715, "dur": 586, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mapbox\\Unity\\MeshGeneration\\Modifiers\\MergedModifierStack.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748419918436715, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918437864, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918438918, "dur": 3789, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Cors.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419918438811, "dur": 4331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918443142, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918443769, "dur": 2730, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\Decal\\Entities\\DecalSkipCulledSystem.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748419918443740, "dur": 3246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918446987, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918447534, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918448100, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918448641, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918449197, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918449871, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918450423, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918451016, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918451850, "dur": 1639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918453640, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1748419918453491, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748419918453766, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918453884, "dur": 450, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Mapbox\\Core\\Plugins\\ThirdParty\\Mapbox.Json\\Net35\\Mapbox.Json.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419918455530, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419918455854, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419918456339, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\bool2x4.gen.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748419918456455, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\bool3x2.gen.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748419918456550, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\bool3x4.gen.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748419918456683, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\bool4x2.gen.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748419918456809, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\bool4x3.gen.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748419918457399, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\float2.gen.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748419918457682, "dur": 236, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\float4x2.gen.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748419918457919, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\float4x3.gen.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748419918458263, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\Il2CppEagerStaticClassConstructionAttribute.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748419918458642, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\int4x2.gen.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748419918458730, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\int4x3.gen.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748419918458796, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\int4x4.gen.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748419918458872, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\math.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748419918458964, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\matrix.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748419918459392, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\Noise\\noise3D.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748419918453871, "dur": 6198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748419918460071, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918460267, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918460388, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748419918460599, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748419918461159, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918461311, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918462081, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918462992, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918463475, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748419918463666, "dur": 506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748419918464173, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918464310, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918464518, "dur": 2233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918466751, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918467243, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748419918467426, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748419918467972, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918468104, "dur": 1574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918469679, "dur": 49492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918520142, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.UserSecrets.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419918519173, "dur": 2435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748419918521609, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918521690, "dur": 1834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748419918523525, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918523833, "dur": 2010, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748419918525844, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918526507, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419918525945, "dur": 2136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748419918528082, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918528988, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.HttpSys.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419918529773, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419918530106, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419918530359, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419918528160, "dur": 2526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748419918530686, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419918530788, "dur": 3066585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918409190, "dur": 22566, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918431774, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748419918431848, "dur": 393, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1748419918431760, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_934EE5D4B70C0964.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748419918432285, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_A5CAFB6010E2CC4E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748419918432537, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748419918432535, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_DB3452A33F8E3985.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748419918432640, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918432792, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918432905, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748419918432903, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_86C139E5AAFE1760.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748419918432990, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918433211, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UWP.Extensions.dll_24FE18831D700F3B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748419918433913, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1748419918434181, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1748419918434757, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1748419918434998, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1748419918435368, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10540497235457876305.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1748419918435856, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918436631, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918437206, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918437784, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918438385, "dur": 669, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Globalization.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748419918439055, "dur": 2360, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748419918438329, "dur": 4406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918442736, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918443330, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918443909, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918444465, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918445014, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918445737, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918446262, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918446798, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918447333, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918447912, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918448444, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918449005, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918449596, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918450413, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918451137, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918452522, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748419918453469, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\CompilerServices\\Hint.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748419918454160, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\SimdDebugViews.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748419918454292, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\v128.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748419918454373, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\v256.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748419918454556, "dur": 233, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\x86\\Bmi2.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748419918455083, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\x86\\Sse3.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748419918452690, "dur": 2740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748419918455431, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918455589, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748419918455760, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918455854, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\Unity.CompilationPipeline.Common.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748419918455852, "dur": 706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748419918456559, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918456676, "dur": 655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 11, "ts": 1748419918457378, "dur": 397, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918460788, "dur": 54105, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 11, "ts": 1748419918519173, "dur": 2012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748419918521191, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918521281, "dur": 1857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748419918523139, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918523214, "dur": 1837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748419918525052, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918525129, "dur": 1801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Profiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748419918526931, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918527015, "dur": 1879, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748419918528895, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918528995, "dur": 1896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748419918530892, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419918531032, "dur": 3066335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918409231, "dur": 22543, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918431787, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419918431841, "dur": 349, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1748419918431775, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C5AD924FEB2B8122.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419918432360, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918432613, "dur": 196, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_17FCD0F4DE70BA41.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419918432969, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.GradleProject.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419918432967, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_10AF8199D68E983D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419918433100, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918433244, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1748419918433401, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1748419918433536, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1748419918433839, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1748419918434144, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1748419918434328, "dur": 287, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1748419918435004, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1748419918435509, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13279423640760612673.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1748419918435796, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918436544, "dur": 1632, "ph": "X", "name": "File", "args": {"detail": "Assets\\Script\\SimpleWindController.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748419918435872, "dur": 2644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918438668, "dur": 657, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.SignalR.Core.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419918438516, "dur": 1330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918439847, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918440430, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918440997, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918441576, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918442233, "dur": 986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918443219, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918443752, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918444335, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918444900, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918445437, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918445957, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918446539, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918447072, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918447652, "dur": 580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918448233, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918448784, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918449347, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918449886, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918450516, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918451092, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918452729, "dur": 691, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.profiling.core@1.0.2\\Runtime\\ProfilerMarkerExtensions.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748419918453420, "dur": 549, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.profiling.core@1.0.2\\Runtime\\ProfilerMarker3Params.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748419918451662, "dur": 2408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918454072, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419918454343, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.UI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748419918454866, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918454999, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748419918455596, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918455856, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419918456324, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419918455711, "dur": 899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748419918456610, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918458038, "dur": 1725, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\ReflectionFieldAccessor.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748419918456757, "dur": 3335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918460654, "dur": 672, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Collections\\INotifiedCollectionItem.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748419918460092, "dur": 2033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918462125, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918462961, "dur": 70, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918463031, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918463478, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419918463653, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748419918464161, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918464257, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918464508, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419918464683, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748419918465180, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918465309, "dur": 1449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918466758, "dur": 2909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918469669, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419918469883, "dur": 49302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918519190, "dur": 1993, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748419918521183, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918521263, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748419918521332, "dur": 1805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748419918523138, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918523206, "dur": 1779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748419918524985, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918525957, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.RazorPages.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419918525133, "dur": 2033, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748419918527167, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918527466, "dur": 1894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748419918529361, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918529868, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918529921, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1748419918530058, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918530530, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419918530797, "dur": 3066590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419921604894, "dur": 2963, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 14060, "tid": 435848, "ts": 1748419921617575, "dur": 35, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 14060, "tid": 435848, "ts": 1748419921617686, "dur": 8355, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 14060, "tid": 435848, "ts": 1748419921608594, "dur": 17505, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}