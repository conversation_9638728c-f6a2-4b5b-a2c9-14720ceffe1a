using UnityEngine;
using Mapbox.Utils;
using Mapbox.Unity.Map;

/// <summary>
/// Gestionnaire de placement manuel des bouées dans Unity
/// Permet de placer les bouées B1/B2 manuellement via l'interface Unity
/// S'intègre avec le système automatique Meshtastic
/// </summary>
public class ManualBuoyPlacer : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private bool enableManualPlacement = true;
    [SerializeField] private bool showManualControls = true;
    [SerializeField] private bool overrideAutomaticPlacement = false; // Si true, le manuel prime sur l'automatique

    [Header("Bouées")]
    [SerializeField] private GameObject buoyPrefab;
    [SerializeField] private Transform startBuoyTransform; // Référence à la bouée B1 existante
    [SerializeField] private Transform finishBuoyTransform; // Référence à la bouée B2 existante

    [Header("Placement Manuel")]
    [SerializeField] private Vector2d manualStartPosition = new Vector2d(43.123456, 5.654321);
    [SerializeField] private Vector2d manualFinishPosition = new Vector2d(43.124456, 5.655321);
    [SerializeField] private float buoyHeight = 0f; // Hauteur au-dessus de l'eau

    [Header("Interface")]
    [SerializeField] private KeyCode placeStartBuoyKey = KeyCode.F1;
    [SerializeField] private KeyCode placeFinishBuoyKey = KeyCode.F2;
    [SerializeField] private KeyCode toggleModeKey = KeyCode.F3;

    [Header("Debug")]
    [SerializeField] private bool showDebugLogs = true;
    [SerializeField] private bool showGizmos = true;

    // Références système
    private AbstractMap map;
    private MeshtasticGPSTracker gpsTracker;
    private RaceLineManager raceLineManager;

    // État
    private GameObject manualStartBuoy;
    private GameObject manualFinishBuoy;
    private bool isInitialized = false;

    private void Start()
    {
        InitializeSystem();
    }

    private void Update()
    {
        if (!enableManualPlacement) return;

        HandleKeyboardInput();
    }

    /// <summary>
    /// Initialise le système de placement manuel
    /// </summary>
    private void InitializeSystem()
    {
        // Trouver les composants nécessaires
        map = FindObjectOfType<AbstractMap>();
        gpsTracker = FindObjectOfType<MeshtasticGPSTracker>();
        raceLineManager = FindObjectOfType<RaceLineManager>();

        if (map == null)
        {
            LogMessage("❌ Carte Mapbox non trouvée - nécessaire pour la conversion GPS");
            return;
        }

        // Trouver le préfab de bouée si non assigné
        if (buoyPrefab == null)
        {
            // Essayer de récupérer le préfab depuis le MeshtasticGPSTracker
            if (gpsTracker != null)
            {
                var buoyPrefabField = typeof(MeshtasticGPSTracker).GetField("buoyPrefab",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (buoyPrefabField != null)
                {
                    buoyPrefab = buoyPrefabField.GetValue(gpsTracker) as GameObject;
                    LogMessage("✅ Préfab de bouée récupéré depuis MeshtasticGPSTracker");
                }
            }
        }

        if (buoyPrefab == null)
        {
            LogMessage("⚠️ Aucun préfab de bouée assigné - utilisation de primitives Unity");
        }

        isInitialized = true;
        LogMessage("✅ Système de placement manuel initialisé");
    }

    /// <summary>
    /// Gère les entrées clavier pour le placement manuel
    /// </summary>
    private void HandleKeyboardInput()
    {
        if (Input.GetKeyDown(placeStartBuoyKey))
        {
            PlaceStartBuoyManually();
        }

        if (Input.GetKeyDown(placeFinishBuoyKey))
        {
            PlaceFinishBuoyManually();
        }

        if (Input.GetKeyDown(toggleModeKey))
        {
            ToggleManualMode();
        }
    }

    /// <summary>
    /// Place manuellement la bouée de départ (B1)
    /// </summary>
    [ContextMenu("Place Start Buoy Manually")]
    public void PlaceStartBuoyManually()
    {
        if (!isInitialized)
        {
            LogMessage("❌ Système non initialisé");
            return;
        }

        LogMessage($"🎯 Placement manuel de la bouée de départ à {manualStartPosition}");

        // Créer ou mettre à jour la bouée
        manualStartBuoy = CreateOrUpdateBuoy(manualStartPosition, "StartBuoy_Manual", manualStartBuoy, Color.green);

        if (manualStartBuoy != null)
        {
            startBuoyTransform = manualStartBuoy.transform;

            // Notifier le système si on override l'automatique ou s'il n'y a pas de système automatique
            if (overrideAutomaticPlacement || gpsTracker == null)
            {
                NotifyBuoyPlacement();
            }

            LogMessage("✅ Bouée de départ placée manuellement");
        }
    }

    /// <summary>
    /// Place manuellement la bouée d'arrivée (B2)
    /// </summary>
    [ContextMenu("Place Finish Buoy Manually")]
    public void PlaceFinishBuoyManually()
    {
        if (!isInitialized)
        {
            LogMessage("❌ Système non initialisé");
            return;
        }

        LogMessage($"🎯 Placement manuel de la bouée d'arrivée à {manualFinishPosition}");

        // Créer ou mettre à jour la bouée
        manualFinishBuoy = CreateOrUpdateBuoy(manualFinishPosition, "EndBuoy_Manual", manualFinishBuoy, Color.red);

        if (manualFinishBuoy != null)
        {
            finishBuoyTransform = manualFinishBuoy.transform;

            // Notifier le système si on override l'automatique ou s'il n'y a pas de système automatique
            if (overrideAutomaticPlacement || gpsTracker == null)
            {
                NotifyBuoyPlacement();
            }

            LogMessage("✅ Bouée d'arrivée placée manuellement");
        }
    }

    /// <summary>
    /// Crée ou met à jour une bouée à la position spécifiée
    /// </summary>
    private GameObject CreateOrUpdateBuoy(Vector2d gpsPosition, string buoyName, GameObject existingBuoy, Color color)
    {
        // Convertir GPS vers position Unity
        Vector3 worldPosition = map.GeoToWorldPosition(gpsPosition);
        worldPosition.y += buoyHeight;

        GameObject buoyObject;

        if (existingBuoy != null)
        {
            // Mettre à jour la position de la bouée existante
            buoyObject = existingBuoy;
            buoyObject.transform.position = worldPosition;
            LogMessage($"🔄 Bouée '{buoyName}' mise à jour à {worldPosition}");
        }
        else
        {
            // Créer une nouvelle bouée
            if (buoyPrefab != null)
            {
                buoyObject = Instantiate(buoyPrefab, worldPosition, Quaternion.identity);
            }
            else
            {
                // Utiliser une primitive Unity si pas de préfab
                buoyObject = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                buoyObject.transform.localScale = Vector3.one * 2f; // Taille visible
                buoyObject.GetComponent<Renderer>().material.color = color;
            }

            buoyObject.name = buoyName;
            buoyObject.transform.SetParent(transform); // Organiser dans la hiérarchie
            LogMessage($"🆕 Nouvelle bouée '{buoyName}' créée à {worldPosition}");
        }

        return buoyObject;
    }

    /// <summary>
    /// Notifie les systèmes du placement des bouées
    /// </summary>
    private void NotifyBuoyPlacement()
    {
        if (manualStartBuoy != null && manualFinishBuoy != null)
        {
            LogMessage("📢 Notification du placement des bouées manuelles");

            // Notifier le RaceLineManager directement avec la méthode pour bouées manuelles
            if (raceLineManager != null)
            {
                raceLineManager.OnManualBuoysPlaced(manualStartBuoy, manualFinishBuoy);
            }

            // Notifier via l'événement du MeshtasticGPSTracker si disponible
            if (gpsTracker != null && overrideAutomaticPlacement)
            {
                var eventInfo = typeof(MeshtasticGPSTracker).GetEvent("OnStartEndBuoysPlaced");
                if (eventInfo != null)
                {
                    // Déclencher l'événement manuellement
                    var eventField = typeof(MeshtasticGPSTracker).GetField("OnStartEndBuoysPlaced",
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    if (eventField != null)
                    {
                        var eventDelegate = eventField.GetValue(gpsTracker) as System.Delegate;
                        if (eventDelegate != null)
                        {
                            eventDelegate.DynamicInvoke(manualStartBuoy, manualFinishBuoy);
                        }
                    }
                }
            }
        }
    }

    /// <summary>
    /// Place les deux bouées avec les positions actuelles
    /// </summary>
    [ContextMenu("Place Both Buoys")]
    public void PlaceBothBuoys()
    {
        PlaceStartBuoyManually();
        PlaceFinishBuoyManually();
    }

    /// <summary>
    /// Définit la position de départ manuellement via coordonnées GPS
    /// </summary>
    public void SetManualStartPosition(double latitude, double longitude)
    {
        manualStartPosition = new Vector2d(latitude, longitude);
        LogMessage($"📍 Position de départ définie: {latitude}, {longitude}");
    }

    /// <summary>
    /// Définit la position d'arrivée manuellement via coordonnées GPS
    /// </summary>
    public void SetManualFinishPosition(double latitude, double longitude)
    {
        manualFinishPosition = new Vector2d(latitude, longitude);
        LogMessage($"📍 Position d'arrivée définie: {latitude}, {longitude}");
    }

    /// <summary>
    /// Active/désactive le mode manuel
    /// </summary>
    [ContextMenu("Toggle Manual Mode")]
    public void ToggleManualMode()
    {
        enableManualPlacement = !enableManualPlacement;
        LogMessage($"🔄 Mode manuel: {(enableManualPlacement ? "Activé" : "Désactivé")}");
    }

    /// <summary>
    /// Active/désactive l'override du système automatique
    /// </summary>
    [ContextMenu("Toggle Override Automatic")]
    public void ToggleOverrideAutomatic()
    {
        overrideAutomaticPlacement = !overrideAutomaticPlacement;
        LogMessage($"🔄 Override automatique: {(overrideAutomaticPlacement ? "Activé" : "Désactivé")}");
    }

    /// <summary>
    /// Supprime les bouées manuelles
    /// </summary>
    [ContextMenu("Clear Manual Buoys")]
    public void ClearManualBuoys()
    {
        if (manualStartBuoy != null)
        {
            DestroyImmediate(manualStartBuoy);
            manualStartBuoy = null;
            startBuoyTransform = null;
        }

        if (manualFinishBuoy != null)
        {
            DestroyImmediate(manualFinishBuoy);
            manualFinishBuoy = null;
            finishBuoyTransform = null;
        }

        LogMessage("🗑️ Bouées manuelles supprimées");
    }

    /// <summary>
    /// Affiche les informations de debug
    /// </summary>
    [ContextMenu("Show Debug Info")]
    public void ShowDebugInfo()
    {
        LogMessage("📊 === INFORMATIONS DE DEBUG ===");
        LogMessage($"Mode manuel: {(enableManualPlacement ? "Activé" : "Désactivé")}");
        LogMessage($"Override automatique: {(overrideAutomaticPlacement ? "Activé" : "Désactivé")}");
        LogMessage($"Position départ: {manualStartPosition}");
        LogMessage($"Position arrivée: {manualFinishPosition}");
        LogMessage($"Bouée départ: {(manualStartBuoy != null ? manualStartBuoy.name : "Non placée")}");
        LogMessage($"Bouée arrivée: {(manualFinishBuoy != null ? manualFinishBuoy.name : "Non placée")}");
        LogMessage($"Carte Mapbox: {(map != null ? "Disponible" : "Non trouvée")}");
        LogMessage($"RaceLineManager: {(raceLineManager != null ? "Disponible" : "Non trouvé")}");
        LogMessage("=== FIN DEBUG ===");
    }

    /// <summary>
    /// Affiche un message de log si les logs de debug sont activés
    /// </summary>
    private void LogMessage(string message)
    {
        if (showDebugLogs)
        {
            Debug.Log($"[ManualBuoyPlacer] {message}");
        }
    }

    private void OnDrawGizmosSelected()
    {
        if (!showGizmos || !isInitialized || map == null) return;

        // Visualiser les positions GPS manuelles
        Gizmos.color = Color.green;
        Vector3 startPos = map.GeoToWorldPosition(manualStartPosition);
        Gizmos.DrawWireSphere(startPos, 5f);

        Gizmos.color = Color.red;
        Vector3 finishPos = map.GeoToWorldPosition(manualFinishPosition);
        Gizmos.DrawWireSphere(finishPos, 5f);

        // Ligne entre les positions
        Gizmos.color = Color.yellow;
        Gizmos.DrawLine(startPos, finishPos);
    }
}
