using UnityEngine;

/// <summary>
/// Classe responsable des effets visuels marins (tangage, roulis, etc.)
/// </summary>
public class MarineEffectsController
{
    // Paramètres des effets
    private float bobHeight = 0.02f;
    private float bobSpeed = 1f;
    private float rollAmount = 5f;
    private float heightOffset = 1f;

    // Variables internes
    private float currentAngularVelocity = 0f;

    public MarineEffectsController(float bobHeight = 0.02f, float bobSpeed = 1f, float rollAmount = 5f, float heightOffset = 1f)
    {
        this.bobHeight = bobHeight;
        this.bobSpeed = bobSpeed;
        this.rollAmount = rollAmount;
        this.heightOffset = heightOffset;
    }

    /// <summary>
    /// Applique les effets marins (tangage, roulis) au bateau
    /// </summary>
    public void ApplyEffects(Transform transform, float speed, float maxSpeed)
    {
        // Calculer les facteurs d'effet
        float speedFactor = speed / maxSpeed;
        float turnFactor = Mathf.Abs(currentAngularVelocity) / 30f; // 30 est une valeur arbitraire pour normaliser

        // Calculer le roulis basé sur la vitesse et la rotation
        float roll = turnFactor * rollAmount * Mathf.Sin(Time.time * bobSpeed);

        // Calculer le tangage (bob) basé sur la vitesse
        float bob = bobHeight * (1 + speedFactor) * Mathf.Sin(Time.time * bobSpeed * 2);

        // Appliquer les effets
        Vector3 currentRotation = transform.rotation.eulerAngles;
        transform.rotation = Quaternion.Euler(0, currentRotation.y, roll);

        // NE PLUS ÉCRASER LA POSITION Y - elle est gérée par UnifiedBoatGPS ou BoatWaypointMover
        // Seulement appliquer le bob comme offset relatif
        Vector3 currentPos = transform.position;
        currentPos.y += bob * 0.1f; // Bob très léger pour l'effet marin
        transform.position = currentPos;
    }

    /// <summary>
    /// Met à jour la vitesse angulaire pour les calculs d'effet
    /// </summary>
    public void UpdateAngularVelocity(float newAngularVelocity)
    {
        currentAngularVelocity = newAngularVelocity;
    }

    // Propriétés
    public float HeightOffset
    {
        get => heightOffset;
        set => heightOffset = value;
    }

    public float BobHeight
    {
        get => bobHeight;
        set => bobHeight = value;
    }

    public float BobSpeed
    {
        get => bobSpeed;
        set => bobSpeed = value;
    }

    public float RollAmount
    {
        get => rollAmount;
        set => rollAmount = value;
    }
}
