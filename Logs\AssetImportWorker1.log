Using pre-set license
Built from '2022.3/staging' branch; Version is '2022.3.47f1 (88c277b85d21) revision 8962679'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'fr' Physical Memory: 13234 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2022.3.47f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
C:/PL/Projets Unity/RegattaVisionV5
-logFile
Logs/AssetImportWorker1.log
-srvPort
60480
Successfully changed project path to: C:/PL/Projets Unity/RegattaVisionV5
C:/PL/Projets Unity/RegattaVisionV5
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [20076]  Target information:

Player connection [20076]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 4105988165 [EditorId] 4105988165 [Version] 1048832 [Id] WindowsEditor(7,MiniPC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [20076]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 4105988165 [EditorId] 4105988165 [Version] 1048832 [Id] WindowsEditor(7,MiniPC) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [20076] Host joined multi-casting on [***********:54997]...
Player connection [20076] Host joined alternative multi-casting on [***********:34997]...
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
Refreshing native plugins compatible for Editor in 879.91 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.47f1 (88c277b85d21)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/PL/Projets Unity/RegattaVisionV5/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: AMD Radeon(TM) Graphics (ID=0x1638)
    Vendor:   ATI
    VRAM:     6617 MB
    Driver:   31.0.21921.1000
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56428
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.47f1/Editor/Data/PlaybackEngines/MetroSupport/UnityEditor.UWP.Extensions.dll
Registered in 0.027607 seconds.
- Loaded All Assemblies, in  2.289 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 338 ms
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.762 seconds
Domain Reload Profiling: 3047ms
	BeginReloadAssembly (233ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (542ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (112ms)
	LoadAllAssembliesAndSetupDomain (1383ms)
		LoadAssemblies (235ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1378ms)
			TypeCache.Refresh (1376ms)
				TypeCache.ScanAssembly (1058ms)
			ScanForSourceGeneratedMonoScriptInfo (1ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (763ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (710ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (478ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (3ms)
			ProcessInitializeOnLoadAttributes (162ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.895 seconds
Native extension for UWP target not found
Native extension for WindowsStandalone target not found
Native extension for Android target not found
Mono: successfully reloaded assembly
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
- Finished resetting the current domain, in  1.607 seconds
Domain Reload Profiling: 6498ms
	BeginReloadAssembly (275ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (4513ms)
		LoadAssemblies (3665ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1022ms)
			TypeCache.Refresh (956ms)
				TypeCache.ScanAssembly (873ms)
			ScanForSourceGeneratedMonoScriptInfo (43ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1607ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1255ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (30ms)
			SetLoadedEditorAssemblies (8ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (224ms)
			ProcessInitializeOnLoadAttributes (878ms)
			ProcessInitializeOnLoadMethodAttributes (98ms)
			AfterProcessingInitializeOnLoad (17ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.09 seconds
Refreshing native plugins compatible for Editor in 80.37 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6836 Unused Serialized files (Serialized files now loaded: 0)
Unloading 47 unused Assets / (2.8 MB). Loaded Objects now: 7310.
Memory consumption went from 289.4 MB to 286.6 MB.
Total: 8.200400 ms (FindLiveObjects: 0.837800 ms CreateObjectMapping: 0.585300 ms MarkObjects: 5.638000 ms  DeleteObjects: 1.137700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 3ead1caaecf1c488f20bcfeb224b6af5 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
========================================================================
Received Import Request.
  Time since last request: 105112.032524 seconds.
  path: Assets/Script/StartSequenceTimer.cs
  artifactKey: Guid(702e92ebb1343174aa468ea5d903bcbc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/StartSequenceTimer.cs using Guid(702e92ebb1343174aa468ea5d903bcbc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '2f58887b1845d71196e393dc72a74d13') in 0.006745 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Script/RegattaData.cs
  artifactKey: Guid(9b232974bccb9964f94bfd4cb34a2976) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/RegattaData.cs using Guid(9b232974bccb9964f94bfd4cb34a2976) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '6f41aab193ce3a19fcfb9a354634b619') in 0.000894 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Script/UIDisplayResults.cs
  artifactKey: Guid(b35201eb18995ea4c9e497d835b54c91) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/UIDisplayResults.cs using Guid(b35201eb18995ea4c9e497d835b54c91) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '15f194304bc06b588a47977b5c4567f0') in 0.001139 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Script/StartBuoyPosition.cs
  artifactKey: Guid(7bd9bf02dab96de4eaaf75b85f1dd19b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/StartBuoyPosition.cs using Guid(7bd9bf02dab96de4eaaf75b85f1dd19b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '69f82d2c254c35ce87f0d1645336d01d') in 0.000655 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Script/StartingLine.cs
  artifactKey: Guid(47a423fbd971c934785baba7d5b0e9ab) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/StartingLine.cs using Guid(47a423fbd971c934785baba7d5b0e9ab) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'b98c1869333cce3fdf0a1de525d8ef5d') in 0.000870 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/Script/UnifiedBoatGPS.cs
  artifactKey: Guid(9dac5bad4bffb9e42b1e34f66d9be4c3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/UnifiedBoatGPS.cs using Guid(9dac5bad4bffb9e42b1e34f66d9be4c3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'c072a4408a42927e6f18d5f2f0b83dea') in 0.000770 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000081 seconds.
  path: Assets/Script/RaceSession.cs
  artifactKey: Guid(077df316d5408fa4aa385f2ef628f527) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/RaceSession.cs using Guid(077df316d5408fa4aa385f2ef628f527) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '9f839adc7d48daad2033773c813ad211') in 0.000764 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Script/TeamInfo.cs
  artifactKey: Guid(f8169af93618a2140adbb6146a4a4fa9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/TeamInfo.cs using Guid(f8169af93618a2140adbb6146a4a4fa9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '255f80734af3f196f2820d504ac64eb6') in 0.000881 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Script/TideWalkerTracker.cs
  artifactKey: Guid(b81e4042860d38e4497d856f5c8d2d24) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/TideWalkerTracker.cs using Guid(b81e4042860d38e4497d856f5c8d2d24) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '0f90807ca0f8c0447c9d70d859832db6') in 0.001025 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Script/RegattaCameraController.cs
  artifactKey: Guid(29a8ba283e373bc40bfaf01fb7a25ad5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/RegattaCameraController.cs using Guid(29a8ba283e373bc40bfaf01fb7a25ad5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '1502d1d7b083cde8722ea8c3b17d2563') in 0.000716 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Script/UISessionResultsPanel.cs
  artifactKey: Guid(75faa66f3bf22454dbaa54bfcaae21d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/UISessionResultsPanel.cs using Guid(75faa66f3bf22454dbaa54bfcaae21d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '0b122cefa17e2cdf79d338a0ea8a525d') in 0.000830 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Script/UIDisplayGeneralRanking.cs
  artifactKey: Guid(d2b0e76d70df4eb42b41e09cdaac257f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/UIDisplayGeneralRanking.cs using Guid(d2b0e76d70df4eb42b41e09cdaac257f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'db5518cfcf74086eb038fcee7d53eaf3') in 0.000739 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Script/UIRanking.cs
  artifactKey: Guid(381a11591f0a93947ad0a2b428f7d894) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/UIRanking.cs using Guid(381a11591f0a93947ad0a2b428f7d894) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'cf276c30bd4ef9f476e7ac7b2c595cd5') in 0.001035 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
