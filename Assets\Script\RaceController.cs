using UnityEngine;
using System.Collections.Generic;
using System.Linq;

public class RaceController : MonoBehaviour
{
    [System.Serializable]
    public class RacingBoat
    {
        public GameObject boat;
        public FloatingBoatUI ui;
        public UnifiedBoatGPS unifiedGPS; // Remplacer BoatWaypointMover par UnifiedBoatGPS
        public int currentWaypointIndex;
        public int rank;
        public int lap;
        public Rigidbody rb; // Ajouter une référence au Rigidbody
    }

    [Header("Course")]
    [SerializeField] private List<Transform> waypoints;
    [SerializeField] private float waypointDetectionRadius = 10f;
    [SerializeField] private int totalLaps = 3;

    [Header("Sessions")]
    [SerializeField] private int currentSession = 1;
    // Suppression de la variable totalSessions, on n'utilise plus de limite prédéfinie
    private float sessionStartTime;
    [SerializeField] private RaceTimer raceTimerScript;
    [SerializeField] private UIRanking uiRankingScript;

    [Header("Flotte")]
    [SerializeField, Tooltip("Liste des bateaux participants à la course")]
    private List<GameObject> _runtimeBoats = new List<GameObject>();
    public List<GameObject> Boats => _runtimeBoats;
    public event System.Action BoatsChanged;
    public event System.Action ActiveBoatsChanged; // Nouvel événement pour les caméras

    [Header("Ligne de départ")]
    [SerializeField] private Transform officialBoat;
    [SerializeField] private bool useStartingLine = true;
    private StartingLine startingLine;
    private Dictionary<GameObject, Vector3> previousBoatPositions = new Dictionary<GameObject, Vector3>();
    private MeshtasticGPSTracker meshtasticGPSTracker; // Référence au MeshtasticGPSTracker

    [SerializeField] private GameObject floatingUIPrefab;

    private List<RacingBoat> racers = new List<RacingBoat>();
    private bool isRaceStarted;
    private bool isSessionManuallyStopped = false; // Nouvelle variable
    private List<float> _waypointDistances;
    private float _totalTrackLength;

    // Variables pour optimiser les mises à jour
    [Header("Optimization")]
    [SerializeField] private float updateInterval = 0.1f; // Intervalle en secondes pour les mises à jour coûteuses
    private float timeSinceLastUpdate = 0f;

    // Méthodes d'accès publiques (modifiées)
    public List<GameObject> GetBoats() => _runtimeBoats;
    public Transform GetLeadBoat() => racers.OrderBy(r => r.rank).FirstOrDefault()?.boat.transform;
    public List<Transform> GetWaypoints() => waypoints;
    public List<RacingBoat> GetRacers() => racers;
    public int GetTotalLaps() => totalLaps;
    public bool IsRaceStarted() => isRaceStarted;
    public int GetCurrentSession() => currentSession;

    // Suppression de la méthode GetTotalSessions() qui n'est plus nécessaire
    // car nous n'avons plus de limite de sessions prédéfinie

    /// <summary>
    /// Méthode publique pour diagnostiquer et nettoyer la liste des bateaux
    /// </summary>
    [ContextMenu("Diagnostiquer et Nettoyer Liste Bateaux")]
    public void DiagnoseAndCleanBoatList()
    {
        Debug.Log("[RaceController] === DIAGNOSTIC DE LA LISTE DES BATEAUX ===");
        Debug.Log($"[RaceController] Nombre de bateaux avant nettoyage: {_runtimeBoats.Count}");

        ValidateAndCleanBoatList();
        LogBoatList();

        // Vérifier aussi le MeshtasticGPSTracker
        var meshtasticTracker = FindObjectOfType<MeshtasticGPSTracker>();
        if (meshtasticTracker != null)
        {
            Debug.Log("[RaceController] === DIAGNOSTIC MESHTASTIC GPS TRACKER ===");
            LogMeshtasticBoats(meshtasticTracker);
        }
    }

    // Méthodes pour gérer la liste des bateaux (modifiées)
    public void AddBoatsToRace(List<GameObject> boats)
    {
        Debug.Log($"[RaceController] Tentative d'ajout de {boats.Count} bateaux à la course.");

        foreach (var boat in boats)
        {
            AddBoat(boat);
        }

        // Validation finale et nettoyage
        ValidateAndCleanBoatList();

        Debug.Log($"[RaceController] Nombre final de bateaux dans la course: {_runtimeBoats.Count}");
        LogBoatList();
    }

    /// <summary>
    /// Valide et nettoie la liste des bateaux pour s'assurer qu'aucun bateau officiel n'est inclus
    /// </summary>
    private void ValidateAndCleanBoatList()
    {
        var boatsToRemove = new List<GameObject>();

        foreach (var boat in _runtimeBoats)
        {
            if (boat == null)
            {
                boatsToRemove.Add(boat);
                continue;
            }

            if (IsOfficialBoat(boat))
            {
                Debug.LogWarning($"[RaceController] Bateau officiel '{boat.name}' détecté dans la liste de course. Suppression automatique.");
                boatsToRemove.Add(boat);
            }
        }

        foreach (var boat in boatsToRemove)
        {
            _runtimeBoats.Remove(boat);
        }

        if (boatsToRemove.Count > 0)
        {
            BoatsChanged?.Invoke();
        }
    }

    /// <summary>
    /// Affiche la liste des bateaux dans les logs pour le débogage
    /// </summary>
    private void LogBoatList()
    {
        Debug.Log("[RaceController] === LISTE DES BATEAUX DE COURSE ===");
        for (int i = 0; i < _runtimeBoats.Count; i++)
        {
            var boat = _runtimeBoats[i];
            if (boat != null)
            {
                var teamInfo = boat.GetComponent<TeamInfo>();
                string teamName = teamInfo?.GetTeamName() ?? "Nom non défini";
                Debug.Log($"[RaceController] Bateau {i + 1}: '{boat.name}' - Équipe: '{teamName}'");
            }
            else
            {
                Debug.LogError($"[RaceController] Bateau {i + 1}: NULL");
            }
        }
        Debug.Log("[RaceController] =====================================");
    }

    /// <summary>
    /// Affiche la liste des bateaux du MeshtasticGPSTracker pour le débogage
    /// </summary>
    private void LogMeshtasticBoats(MeshtasticGPSTracker tracker)
    {
        if (tracker == null) return;

        // Utiliser la réflexion pour accéder aux champs privés
        var boatsField = typeof(AbstractGPSTracker).GetField("boats",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var boatDictionaryField = typeof(AbstractGPSTracker).GetField("boatDictionary",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var officialBoatNodeIdField = typeof(MeshtasticGPSTracker).GetField("officialBoatNodeId",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

        if (boatsField != null)
        {
            var boats = boatsField.GetValue(tracker) as System.Collections.Generic.List<RegattaBoatData>;
            if (boats != null)
            {
                Debug.Log($"[RaceController] MeshtasticGPSTracker - Nombre de bateaux dans la liste: {boats.Count}");
                for (int i = 0; i < boats.Count; i++)
                {
                    var boat = boats[i];
                    if (boat != null)
                    {
                        Debug.Log($"[RaceController] MeshtasticGPSTracker Bateau {i + 1}: '{boat.boatId}' - MAC: '{boat.associatedMacAddress}' - GameObject: {(boat.boatObject != null ? boat.boatObject.name : "NULL")}");
                    }
                    else
                    {
                        Debug.LogError($"[RaceController] MeshtasticGPSTracker Bateau {i + 1}: NULL");
                    }
                }
            }
        }

        if (boatDictionaryField != null)
        {
            var boatDictionary = boatDictionaryField.GetValue(tracker) as System.Collections.Generic.Dictionary<string, RegattaBoatData>;
            if (boatDictionary != null)
            {
                Debug.Log($"[RaceController] MeshtasticGPSTracker - Nombre de bateaux dans le dictionnaire: {boatDictionary.Count}");
                foreach (var kvp in boatDictionary)
                {
                    Debug.Log($"[RaceController] MeshtasticGPSTracker Dictionnaire - MAC: '{kvp.Key}' -> Bateau: '{kvp.Value?.boatId}' - GameObject: {(kvp.Value?.boatObject != null ? kvp.Value.boatObject.name : "NULL")}");
                }
            }
        }

        if (officialBoatNodeIdField != null)
        {
            var officialBoatNodeId = officialBoatNodeIdField.GetValue(tracker) as string;
            Debug.Log($"[RaceController] MeshtasticGPSTracker - ID du bateau officiel: '{officialBoatNodeId}'");
        }
    }

    public void AddBoat(GameObject boat)
    {
        if (!_runtimeBoats.Contains(boat))
        {
            // NOUVEAU: Vérifier si c'est le bateau officiel et l'exclure
            if (IsOfficialBoat(boat))
            {
                Debug.LogWarning($"[RaceController] Le bateau officiel '{boat.name}' ne peut pas être ajouté à la course. Il est exclu automatiquement.");
                return;
            }

            _runtimeBoats.Add(boat);
            Debug.Log($"[RaceController] Bateau '{boat.name}' ajouté à la course. Total: {_runtimeBoats.Count}");
            BoatsChanged?.Invoke();
        }
    }

    /// <summary>
    /// Vérifie si un bateau est le bateau officiel
    /// </summary>
    private bool IsOfficialBoat(GameObject boat)
    {
        // Vérifier par Transform
        if (officialBoat != null && boat.transform == officialBoat)
        {
            return true;
        }

        // Vérifier par nom (si le bateau officiel a un nom spécifique)
        if (boat.name.ToLower().Contains("official") || boat.name.ToLower().Contains("officiel"))
        {
            return true;
        }

        // Vérifier via MeshtasticGPSTracker si disponible
        if (meshtasticGPSTracker != null)
        {
            // Accéder à l'ID du bateau officiel depuis MeshtasticGPSTracker
            var officialBoatNodeId = GetOfficialBoatNodeId();
            if (!string.IsNullOrEmpty(officialBoatNodeId))
            {
                // Vérifier si ce bateau a le même ID Meshtastic que le bateau officiel
                var teamInfo = boat.GetComponent<TeamInfo>();
                if (teamInfo != null && teamInfo.GetTeamName() == officialBoatNodeId)
                {
                    return true;
                }
            }
        }

        return false;
    }

    /// <summary>
    /// Récupère l'ID du bateau officiel depuis MeshtasticGPSTracker
    /// </summary>
    private string GetOfficialBoatNodeId()
    {
        if (meshtasticGPSTracker != null)
        {
            // Utiliser la réflexion pour accéder au champ privé officialBoatNodeId
            var field = typeof(MeshtasticGPSTracker).GetField("officialBoatNodeId",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (field != null)
            {
                return field.GetValue(meshtasticGPSTracker) as string;
            }
        }
        return null;
    }

    public void RemoveBoat(GameObject boat)
    {
        if (_runtimeBoats.Remove(boat))
        {
            BoatsChanged?.Invoke();
            if (isRaceStarted)
            {
                var racer = racers.FirstOrDefault(r => r.boat == boat);
                if (racer != null)
                {
                    Destroy(racer.ui.gameObject);
                    racers.Remove(racer);
                }
            }
        }
    }

    public void UpdateTeamName(GameObject boat, string newName)
    {
        var racer = racers.FirstOrDefault(r => r.boat == boat);
        if (racer != null && racer.ui != null)
        {
            racer.ui.SetTeamName(newName);
        }
    }

    public RacingBoat GetBoatInfo(GameObject boat)
    {
        return racers.FirstOrDefault(r => r.boat == boat);
    }

    // Méthodes de calcul de progression (avec vérifications de sécurité)
    public float GetDistanceToNextWaypoint(RacingBoat racer)
    {
        // Vérifications de sécurité
        if (racer == null || waypoints == null || waypoints.Count == 0)
            return float.MaxValue; // Distance très grande pour le classement

        if (racer.currentWaypointIndex >= waypoints.Count)
            return float.MaxValue;

        if (waypoints[racer.currentWaypointIndex] == null)
            return float.MaxValue;

        return Vector3.Distance(
            racer.boat.transform.position,
            waypoints[racer.currentWaypointIndex].position
        );
    }

    public string GetProgressInfo(RacingBoat racer)
    {
        if (racer == null) return "";
        return $"Tour {racer.lap + 1}/{totalLaps}";
    }

    // Gestion des sessions modifiée
    public void StartNewSession()
    {
        Debug.Log("[RaceController] StartNewSession() appelée.");
        if (waypoints.Count == 0)
        {
            Debug.LogError("Aucun waypoint défini pour la course");
            return;
        }

        if (_runtimeBoats.Count == 0)
        {
            Debug.LogError("Aucun bateau sélectionné dans _runtimeBoats pour la course");
            return;
        }

        // Nettoyer les interfaces précédentes si nécessaire
        CleanupRacerInterfaces();

        InitializeRacers();
        // La ligne de départ sera initialisée via l'événement des bouées

        isRaceStarted = true;
        // Time.timeScale = 1f; // Supprimé pour éviter les changements abrupts au début de la course

        // Notifier les caméras que les bateaux actifs ont changé
        ActiveBoatsChanged?.Invoke();

        RaceEvents.TriggerOnSessionStarted(currentSession);
        sessionStartTime = Time.time;

        if (raceTimerScript != null)
        {
            raceTimerScript.ResetRaceTime();
        }
        else
        {
            Debug.LogError("RaceTimerScript non assigné dans RaceController!");
        }
        ResetSessionManuallyStopped();
    }

    // Méthodes de gestion de session modifiées
    public void StopCurrentSession()
    {
        if (!isRaceStarted) return;
        StopSession();
    }

    public void StopSession()
    {
        if (!isRaceStarted) return;

        isRaceStarted = false;
        isSessionManuallyStopped = true; // Définir l'indicateur d'arrêt manuel
        Debug.Log($"StopRace() called for Session {currentSession}");

        // NOUVEAU: Ne plus modifier les Rigidbodies à l'arrêt de la course
        // Les bateaux GPS doivent rester en mode kinématique pour continuer à fonctionner
        // après la course (pour les spectateurs qui veulent voir le mouvement GPS)
        foreach (var racer in racers)
        {
            if (racer.rb != null)
            {
                // Garder isKinematic = true pour que le GPS continue de fonctionner
                Debug.Log($"Rigidbody maintenu en mode GPS (kinématique) pour le bateau {racer.boat.name}");
            }
        }

        // Arrêter le chronomètre
        if (raceTimerScript != null)
        {
            raceTimerScript.StopRace();
            raceTimerScript.ResetRaceTime();
        }

        // Sauvegarder les résultats de la session AVANT de nettoyer les interfaces
        RaceResultsSaver resultsSaver = FindObjectOfType<RaceResultsSaver>();
        if (resultsSaver != null)
        {
            resultsSaver.SaveRaceResults(currentSession);
        }
        else
        {
            Debug.LogError("RaceResultsSaver est manquant !");
        }

        // Nettoyer les interfaces des bateaux APRÈS avoir sauvegardé les résultats
        CleanupRacerInterfaces();

        // Déclencher l'événement de fin de session
        RaceEvents.TriggerOnSessionEnded(currentSession);
    }

    public void PrepareNextSession()
    {
        // Incrémentation du numéro de session sans vérifier de limite
        currentSession++;
        CleanupRacerInterfaces();
        raceTimerScript.ResetRaceTime();
        ResetSessionManuallyStopped();
        Debug.Log($"Préparation de la session {currentSession}");
    }

    public void ManualStartNextSession()
    {
        // Incrémentation du numéro de session sans vérifier de limite
        currentSession++;
        StartNewSession();
        ResetSessionManuallyStopped();
    }

    // Méthodes de nettoyage et de réinitialisation
    private void CleanupRacerInterfaces()
    {
        foreach (var racer in racers)
        {
            if (racer.ui != null)
            {
                Destroy(racer.ui.gameObject);
            }
        }
        racers.Clear();
    }

    private void ResetRaceData()
    {
        currentSession = 1;
        _runtimeBoats.Clear();
        previousBoatPositions.Clear();

        isRaceStarted = false;
        sessionStartTime = 0f;
    }

    // Méthodes existantes restent inchangées
    private void InitializeRacers()
    {
        racers.Clear();
        foreach (var boat in _runtimeBoats)
        {
            if (boat == null) continue;

            var unifiedGPS = boat.GetComponent<UnifiedBoatGPS>();
            if (unifiedGPS == null)
            {
                Debug.LogError($"Bateau {boat.name} n'a pas de composant UnifiedBoatGPS!");
                continue;
            }

            // Désactiver le BoatWaypointMover si présent pour éviter les conflits
            var waypointMover = boat.GetComponent<BoatWaypointMover>();
            if (waypointMover != null)
            {
                waypointMover.enabled = false;
            }

            GameObject uiObject = Instantiate(floatingUIPrefab);
            FloatingBoatUI ui = uiObject.GetComponent<FloatingBoatUI>();
            ui.SetTarget(boat.transform);

            var teamInfo = boat.GetComponent<TeamInfo>();
            if (teamInfo != null)
                RaceEvents.TriggerOnTeamNameUpdated(boat, teamInfo.GetTeamName());
            else
                ui.SetTeamName(boat.name);

            ui.SetTotalLaps(totalLaps);
            ui.UpdateLap(0);

            racers.Add(new RacingBoat
            {
                boat = boat,
                ui = ui,
                unifiedGPS = unifiedGPS,
                currentWaypointIndex = 0,
                rank = 1,
                lap = 0,
                rb = boat.GetComponent<Rigidbody>() // Obtenir la référence au Rigidbody
            });

            // NOUVEAU: Les bateaux GPS doivent toujours avoir isKinematic = true
            // Ne plus modifier les Rigidbodies ici car cela interfère avec le mouvement GPS
            if (racers[^1].rb != null)
            {
                // S'assurer que le Rigidbody est configuré pour le GPS (toujours kinématique)
                racers[^1].rb.isKinematic = true;
                racers[^1].rb.detectCollisions = false;
                Debug.Log($"Rigidbody configuré pour GPS (kinématique) pour le bateau {boat.name}");
            }
        }
    }

    protected void Awake()
    {
        meshtasticGPSTracker = FindObjectOfType<MeshtasticGPSTracker>();
        if (meshtasticGPSTracker != null)
        {
            meshtasticGPSTracker.OnStartEndBuoysPlaced += HandleStartEndBuoysPlaced;
        }
        else
        {
            Debug.LogWarning("[RaceController] MeshtasticGPSTracker non trouvé dans la scène. La liaison dynamique des bouées ne fonctionnera pas.");
        }
    }

    protected void OnDestroy()
    {
        if (meshtasticGPSTracker != null)
        {
            meshtasticGPSTracker.OnStartEndBuoysPlaced -= HandleStartEndBuoysPlaced;
        }
    }

    /// <summary>
    /// Gère l'événement de placement des bouées de départ et d'arrivée par le MeshtasticGPSTracker.
    /// </summary>
    /// <param name="startBuoy">La bouée de départ.</param>
    /// <param name="endBuoy">La bouée d'arrivée.</param>
    private void HandleStartEndBuoysPlaced(GameObject startBuoy, GameObject endBuoy)
    {
        Debug.Log("[RaceController] Événement OnStartEndBuoysPlaced reçu. Configuration des lignes de course.");

        // Utiliser le nouveau RaceLineManager au lieu de l'ancien StartingLine
        var raceLineManager = GetComponent<RaceLineManager>();
        if (raceLineManager == null)
        {
            raceLineManager = gameObject.AddComponent<RaceLineManager>();
            Debug.Log("[RaceController] RaceLineManager ajouté automatiquement");
        }

        // Le RaceLineManager s'abonne automatiquement aux événements et gère les lignes
        // Nous pouvons aussi appeler directement la méthode si nécessaire
        if (startBuoy != null && endBuoy != null)
        {
            raceLineManager.OnBuoysPlaced(startBuoy, endBuoy);
            Debug.Log("[RaceController] Lignes de course configurées avec le RaceLineManager.");
        }
        else
        {
            Debug.LogWarning("[RaceController] Une ou les deux bouées sont nulles lors de la réception de l'événement.");
        }
    }

    private void Update()
    {
        if (!isRaceStarted) return;

        if (CheckSessionFinished())
        {
            Debug.Log("Course terminée!");
            StopSession();
            return;
        }

        timeSinceLastUpdate += Time.deltaTime;

        if (timeSinceLastUpdate >= updateInterval)
        {
            CheckWaypointsAndUpdateLaps();
            UpdateRankings();

            if (uiRankingScript != null)
            {
                uiRankingScript.UpdateRankingDisplay();
            }

            CheckStartingLineCrossings();

            timeSinceLastUpdate = 0f; // Réinitialiser le compteur
        }
    }

    private void CheckStartingLineCrossings()
    {
        if (!useStartingLine || startingLine == null) return;

        foreach (var racer in racers)
        {
            GameObject boat = racer.boat;
            if (!previousBoatPositions.ContainsKey(boat))
            {
                previousBoatPositions[boat] = boat.transform.position;
                continue;
            }

            // Vérifier si le bateau a traversé la ligne
            if (startingLine.HasBoatCrossedLine(boat.transform, previousBoatPositions[boat]))
            {
                Debug.Log($"Bateau {boat.name} a traversé la ligne de départ!");

                // Vous pouvez déclencher un événement ici ou mettre à jour le statut de la course
                // Exemple : RaceEvents.TriggerBoatCrossedStartingLine(boat);
            }

            // Mettre à jour la position précédente
            previousBoatPositions[boat] = boat.transform.position;
        }
    }

    private bool CheckSessionFinished()
    {
        // Vérifier si tous les bateaux ont terminé leurs tours OU si la session a été arrêtée manuellement
        bool allBoatsFinished = racers.All(boat => boat.lap >= totalLaps);

        if (isSessionManuallyStopped || allBoatsFinished)
        {
            // Marquer les bateaux qui n'ont pas terminé comme "hors temps"
            foreach (var boat in racers)
            {
                if (boat.lap < totalLaps)
                {
                    // Accéder au script RaceResultsSaver et marquer le bateau comme "hors temps"
                    RaceResultsSaver resultsSaver = FindObjectOfType<RaceResultsSaver>();
                    if (resultsSaver != null)
                    {
                        resultsSaver.SetBoatStatus(currentSession, boat.boat.name, "Hors temps");
                    }
                    else
                    {
                        Debug.LogError("RaceResultsSaver est manquant !");
                    }
                }
            }
            isSessionManuallyStopped = false; // Reset the flag before returning
            return true; // La session est terminée
        }

        return false; // La session n'est pas encore terminée
    }

    public void ResetSessionManuallyStopped()
    {
        isSessionManuallyStopped = false;
    }

    private void CheckWaypointsAndUpdateLaps()
    {
        // Vérifier que les waypoints sont disponibles
        if (waypoints == null || waypoints.Count == 0)
        {
            return; // Pas de waypoints, pas de vérification possible
        }

        foreach (var racer in racers)
        {
            if (racer.lap >= totalLaps) continue;

            // Vérifier que l'index du waypoint est valide
            if (racer.currentWaypointIndex >= waypoints.Count || waypoints[racer.currentWaypointIndex] == null)
            {
                continue; // Waypoint invalide, passer au bateau suivant
            }

            Vector3 waypointPosition = waypoints[racer.currentWaypointIndex].position;
            // Le mouvement est géré par UnifiedBoatGPS, pas besoin d'appeler SetNextWaypoint sur BoatWaypointMover
            // racer.mover.SetNextWaypoint(waypoints[racer.currentWaypointIndex]);

            // Utiliser la position actuelle du bateau pour vérifier la distance au waypoint
            float distance = Vector3.Distance(racer.boat.transform.position, waypointPosition);

            if (distance < waypointDetectionRadius)
            {
                UpdateRacerProgress(racer);
            }
        }
    }

    private void UpdateRacerProgress(RacingBoat racer)
    {
        // Vérifier que les waypoints sont disponibles
        if (waypoints == null || waypoints.Count == 0) return;

        racer.currentWaypointIndex++;
        if (racer.currentWaypointIndex >= waypoints.Count)
        {
            racer.currentWaypointIndex = 0;
            racer.lap++;
            racer.ui.UpdateLap(racer.lap);
            Debug.Log($"Bateau {racer.boat.name} - Tour {racer.lap}/{totalLaps}");
        }
    }

    private void UpdateRankings()
    {
        var sortedBoats = racers
            .OrderByDescending(b => b.lap)
            .ThenByDescending(b => b.currentWaypointIndex)
            .ThenBy(b => GetDistanceToNextWaypoint(b))
            .ToList();

        var leader = sortedBoats.FirstOrDefault();

        for (int i = 0; i < sortedBoats.Count; i++)
        {
            var racer = sortedBoats[i];
            racer.rank = i + 1;

            float gapToLeader = (i == 0) ? 0 : CalculateGapToLeader(racer, leader);
            racer.ui.UpdateRank(racer.rank, gapToLeader);
        }
    }

    private float CalculateGapToLeader(RacingBoat follower, RacingBoat leader)
    {
        // Si pas de waypoints, utiliser une distance simple
        if (waypoints == null || waypoints.Count == 0)
        {
            return Vector3.Distance(leader.boat.transform.position, follower.boat.transform.position);
        }

        float gap = 0f;

        if (_waypointDistances == null || _waypointDistances.Count != waypoints.Count)
            CacheWaypointDistances();

        int lapDifference = leader.lap - follower.lap;
        gap += lapDifference * _totalTrackLength;

        float leaderProgress = GetBoatProgress(leader);
        float followerProgress = GetBoatProgress(follower);

        gap += leaderProgress - followerProgress;

        return Mathf.Max(0, gap);
    }

    private float GetBoatProgress(RacingBoat boat)
    {
        // Si pas de waypoints, retourner 0
        if (waypoints == null || waypoints.Count == 0 || _waypointDistances == null)
        {
            return 0f;
        }

        float progress = boat.lap * _totalTrackLength;

        // Ajouter la distance de tous les waypoints précédents
        for (int i = 0; i < boat.currentWaypointIndex && i < _waypointDistances.Count; i++)
        {
            progress += _waypointDistances[i];
        }

        // Calculer la distance depuis le dernier waypoint atteint
        if (boat.currentWaypointIndex > 0 && boat.currentWaypointIndex <= waypoints.Count)
        {
            if (waypoints[boat.currentWaypointIndex - 1] != null)
            {
                Vector3 lastWaypoint = waypoints[boat.currentWaypointIndex - 1].position;
                progress += Vector3.Distance(lastWaypoint, boat.boat.transform.position);
            }
        }
        else if (boat.lap > 0 && waypoints.Count > 0 && waypoints[^1] != null)
        {
            // Si le bateau a commencé un nouveau tour, le dernier waypoint est le dernier de la liste
            Vector3 lastWaypoint = waypoints[^1].position;
            progress += Vector3.Distance(lastWaypoint, boat.boat.transform.position);
        }

        return progress;
    }

    private void CacheWaypointDistances()
    {
        _waypointDistances = new List<float>();
        _totalTrackLength = 0f;

        // Vérifier que les waypoints sont disponibles
        if (waypoints == null || waypoints.Count == 0)
        {
            return;
        }

        for (int i = 0; i < waypoints.Count; i++)
        {
            // Vérifier que les waypoints actuels et suivants ne sont pas null
            if (waypoints[i] == null || waypoints[(i + 1) % waypoints.Count] == null)
            {
                _waypointDistances.Add(0f); // Distance 0 pour les waypoints invalides
                continue;
            }

            Vector3 current = waypoints[i].position;
            Vector3 next = waypoints[(i + 1) % waypoints.Count].position;
            float distance = Vector3.Distance(current, next);
            _waypointDistances.Add(distance);
            _totalTrackLength += distance;
        }
    }

    private void OnDrawGizmos()
    {
        if (waypoints == null) return;

        foreach (var wp in waypoints.Where(w => w != null))
        {
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(wp.position, waypointDetectionRadius);
        }

        if (waypoints.Count > 1)
        {
            Gizmos.color = Color.cyan;
            for (int i = 0; i < waypoints.Count; i++)
            {
                if (waypoints[i] != null)
                {
                    int nextIndex = (i + 1) % waypoints.Count;
                    if (waypoints[nextIndex] != null)
                    {
                        Gizmos.DrawLine(waypoints[i].position, waypoints[nextIndex].position);
                    }
                }
            }
        }
    }
}