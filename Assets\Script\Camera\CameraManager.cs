using UnityEngine;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Gestionnaire centralisé des caméras pour s'assurer qu'elles suivent seulement les bateaux actifs
/// </summary>
public class CameraManager : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private bool autoUpdateCameras = true;
    [SerializeField] private float updateInterval = 1f;
    [SerializeField] private bool showDebugLogs = true;

    [Header("Références")]
    [SerializeField] private RaceController raceController;
    [SerializeField] private RegattaTacticalCamera tacticalCamera;
    [SerializeField] private SpectatorCameraController spectatorCamera;
    [SerializeField] private RegattaCameraSwitcher cameraSwitcher;

    // État
    private float lastUpdateTime;
    private List<GameObject> lastActiveBoats = new List<GameObject>();

    private void Start()
    {
        InitializeCameraManager();
    }

    private void Update()
    {
        if (autoUpdateCameras && Time.time - lastUpdateTime > updateInterval)
        {
            UpdateCameraTargets();
            lastUpdateTime = Time.time;
        }
    }

    /// <summary>
    /// Initialise le gestionnaire de caméras
    /// </summary>
    private void InitializeCameraManager()
    {
        // Trouver les références automatiquement si non assignées
        if (raceController == null)
            raceController = FindObjectOfType<RaceController>();

        if (tacticalCamera == null)
            tacticalCamera = FindObjectOfType<RegattaTacticalCamera>();

        if (spectatorCamera == null)
            spectatorCamera = FindObjectOfType<SpectatorCameraController>();

        if (cameraSwitcher == null)
            cameraSwitcher = FindObjectOfType<RegattaCameraSwitcher>();

        // S'abonner aux événements du RaceController
        if (raceController != null)
        {
            raceController.ActiveBoatsChanged += OnActiveBoatsChanged;
            LogMessage("✅ Abonné aux événements de changement de bateaux actifs");
        }

        LogMessage("🎥 CameraManager initialisé");
        ShowCameraStatus();
    }

    private void OnDestroy()
    {
        // Se désabonner des événements
        if (raceController != null)
        {
            raceController.ActiveBoatsChanged -= OnActiveBoatsChanged;
        }
    }

    /// <summary>
    /// Appelé quand les bateaux actifs changent
    /// </summary>
    private void OnActiveBoatsChanged()
    {
        LogMessage("📢 Événement ActiveBoatsChanged reçu");
        ForceUpdateAllCameras();
    }

    /// <summary>
    /// Met à jour les cibles des caméras selon les bateaux actifs
    /// </summary>
    [ContextMenu("Update Camera Targets")]
    public void UpdateCameraTargets()
    {
        if (raceController == null) return;

        // Obtenir les bateaux actifs dans la course
        var activeBoats = GetActiveBoats();

        // Vérifier si la liste des bateaux actifs a changé
        if (HasActiveBoatsChanged(activeBoats))
        {
            LogMessage($"🔄 Mise à jour des caméras - {activeBoats.Count} bateaux actifs");

            // Mettre à jour les caméras
            UpdateSpectatorCamera(activeBoats);
            UpdateTacticalCamera(activeBoats);

            // Sauvegarder la nouvelle liste
            lastActiveBoats = activeBoats.ToList();
        }
    }

    /// <summary>
    /// Obtient la liste des bateaux actifs dans la course
    /// </summary>
    private List<GameObject> GetActiveBoats()
    {
        if (raceController == null) return new List<GameObject>();

        var racers = raceController.GetRacers();
        if (racers == null || racers.Count == 0) return new List<GameObject>();

        return racers.Select(r => r.boat).Where(b => b != null).ToList();
    }

    /// <summary>
    /// Vérifie si la liste des bateaux actifs a changé
    /// </summary>
    private bool HasActiveBoatsChanged(List<GameObject> currentActiveBoats)
    {
        if (lastActiveBoats.Count != currentActiveBoats.Count) return true;

        for (int i = 0; i < currentActiveBoats.Count; i++)
        {
            if (!lastActiveBoats.Contains(currentActiveBoats[i])) return true;
        }

        return false;
    }

    /// <summary>
    /// Met à jour la caméra spectateur
    /// </summary>
    private void UpdateSpectatorCamera(List<GameObject> activeBoats)
    {
        if (spectatorCamera == null || activeBoats.Count == 0) return;

        // Si la cible actuelle n'est pas dans les bateaux actifs, changer de cible
        var currentTarget = GetSpectatorCameraTarget();
        if (currentTarget == null || !activeBoats.Contains(currentTarget.gameObject))
        {
            spectatorCamera.SetTarget(activeBoats[0].transform);
            LogMessage($"📹 SpectatorCamera: nouvelle cible -> {activeBoats[0].name}");
        }
    }

    /// <summary>
    /// Met à jour la caméra tactique (elle se met à jour automatiquement via GetRacers())
    /// </summary>
    private void UpdateTacticalCamera(List<GameObject> activeBoats)
    {
        // La RegattaTacticalCamera utilise maintenant GetRacers() donc elle se met à jour automatiquement
        if (tacticalCamera != null)
        {
            LogMessage($"🎯 TacticalCamera: suit {activeBoats.Count} bateaux actifs");
        }
    }

    /// <summary>
    /// Obtient la cible actuelle de la caméra spectateur
    /// </summary>
    private Transform GetSpectatorCameraTarget()
    {
        if (spectatorCamera == null) return null;

        // Utiliser la réflexion pour accéder au champ privé targetBoat
        var targetField = typeof(SpectatorCameraController).GetField("targetBoat",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

        if (targetField != null)
        {
            return targetField.GetValue(spectatorCamera) as Transform;
        }

        return null;
    }

    /// <summary>
    /// Force la mise à jour de toutes les caméras
    /// </summary>
    [ContextMenu("Force Update All Cameras")]
    public void ForceUpdateAllCameras()
    {
        lastActiveBoats.Clear(); // Forcer la détection de changement
        UpdateCameraTargets();
        LogMessage("🔄 Mise à jour forcée de toutes les caméras");
    }

    /// <summary>
    /// Affiche le statut des caméras
    /// </summary>
    [ContextMenu("Show Camera Status")]
    public void ShowCameraStatus()
    {
        LogMessage("📊 === STATUT DES CAMÉRAS ===");

        LogMessage($"RaceController: {(raceController != null ? "✅" : "❌")}");
        LogMessage($"TacticalCamera: {(tacticalCamera != null ? "✅" : "❌")}");
        LogMessage($"SpectatorCamera: {(spectatorCamera != null ? "✅" : "❌")}");
        LogMessage($"CameraSwitcher: {(cameraSwitcher != null ? "✅" : "❌")}");

        if (raceController != null)
        {
            var activeBoats = GetActiveBoats();
            LogMessage($"Bateaux actifs: {activeBoats.Count}");

            foreach (var boat in activeBoats)
            {
                LogMessage($"  - {boat.name}");
            }

            var allBoats = raceController.GetBoats();
            LogMessage($"Bateaux totaux dans la scène: {allBoats.Count}");
        }

        var currentTarget = GetSpectatorCameraTarget();
        LogMessage($"Cible SpectatorCamera: {(currentTarget != null ? currentTarget.name : "Aucune")}");

        LogMessage("=== FIN DU STATUT ===");
    }

    /// <summary>
    /// Bascule vers le bateau suivant pour la caméra spectateur
    /// </summary>
    [ContextMenu("Switch to Next Boat")]
    public void SwitchToNextBoat()
    {
        if (spectatorCamera == null) return;

        var activeBoats = GetActiveBoats();
        if (activeBoats.Count == 0) return;

        var currentTarget = GetSpectatorCameraTarget();
        int currentIndex = -1;

        if (currentTarget != null)
        {
            currentIndex = activeBoats.FindIndex(b => b.transform == currentTarget);
        }

        int nextIndex = (currentIndex + 1) % activeBoats.Count;
        spectatorCamera.SetTarget(activeBoats[nextIndex].transform);

        LogMessage($"📹 Basculement vers: {activeBoats[nextIndex].name}");
    }

    /// <summary>
    /// Bascule vers le bateau leader
    /// </summary>
    [ContextMenu("Switch to Leader")]
    public void SwitchToLeader()
    {
        if (spectatorCamera == null || raceController == null) return;

        var racers = raceController.GetRacers();
        if (racers == null || racers.Count == 0) return;

        var leader = racers.OrderBy(r => r.rank).FirstOrDefault();
        if (leader != null && leader.boat != null)
        {
            spectatorCamera.SetTarget(leader.boat.transform);
            LogMessage($"👑 Basculement vers le leader: {leader.boat.name}");
        }
    }

    /// <summary>
    /// Configure les caméras pour ignorer les bateaux inactifs
    /// </summary>
    [ContextMenu("Configure Cameras for Active Boats Only")]
    public void ConfigureCamerasForActiveBoatsOnly()
    {
        LogMessage("⚙️ Configuration des caméras pour bateaux actifs uniquement...");

        // Forcer la mise à jour
        ForceUpdateAllCameras();

        // Vérifier que les caméras utilisent les bonnes méthodes
        if (tacticalCamera != null)
        {
            LogMessage("✅ TacticalCamera configurée pour utiliser GetRacers()");
        }

        if (spectatorCamera != null)
        {
            LogMessage("✅ SpectatorCamera configurée pour utiliser GetRacers()");
        }

        LogMessage("✅ Configuration terminée");
    }

    /// <summary>
    /// Test du système de caméras
    /// </summary>
    [ContextMenu("Test Camera System")]
    public void TestCameraSystem()
    {
        LogMessage("🧪 === TEST DU SYSTÈME DE CAMÉRAS ===");

        if (raceController == null)
        {
            LogMessage("❌ RaceController non trouvé");
            return;
        }

        var allBoats = raceController.GetBoats();
        var activeBoats = GetActiveBoats();

        LogMessage($"Bateaux totaux: {allBoats.Count}");
        LogMessage($"Bateaux actifs: {activeBoats.Count}");

        if (activeBoats.Count < allBoats.Count)
        {
            LogMessage("✅ Les caméras suivent seulement les bateaux actifs");
        }
        else if (activeBoats.Count == allBoats.Count)
        {
            LogMessage("⚠️ Tous les bateaux sont actifs - normal si tous sont sélectionnés");
        }
        else
        {
            LogMessage("❌ Problème: plus de bateaux actifs que de bateaux totaux");
        }

        // Tester le changement de cible
        SwitchToNextBoat();

        LogMessage("=== TEST TERMINÉ ===");
    }

    /// <summary>
    /// Affiche un message de log si les logs de debug sont activés
    /// </summary>
    private void LogMessage(string message)
    {
        if (showDebugLogs)
        {
            Debug.Log($"[CameraManager] {message}");
        }
    }

    /// <summary>
    /// Appelé quand la course démarre
    /// </summary>
    public void OnRaceStart()
    {
        LogMessage("🏁 Course démarrée - mise à jour des caméras");
        ForceUpdateAllCameras();
    }

    /// <summary>
    /// Appelé quand la course s'arrête
    /// </summary>
    public void OnRaceStop()
    {
        LogMessage("🏁 Course arrêtée");
        // Les caméras peuvent continuer à suivre les derniers bateaux actifs
    }
}
