using UnityEngine;

public class RegattaTVCamera : MonoBehaviour
{
    [Header("Positions")]
    [SerializeField] private float baseHeight = 5f;
    [SerializeField] private float minDistance = 15f;
    [SerializeField] private float maxDistance = 30f;
    [SerializeField] private float damping = 0.5f;

    [Header("Angles dynamiques")]
    [SerializeField] private float angleChangeSpeed = 0.2f;
    [SerializeField] private float maxLateralAngle = 45f;
    [SerializeField] private float heightVariationRange = 3f;

    private Transform target;
    private Vector3 velocity;
    private float currentAngle;
    private float currentDistance;
    private float angleVelocity;
    private float heightOffset;

    public void SetTarget(Transform newTarget)
    {
        target = newTarget;
    }

    private void Start()
    {
        currentDistance = minDistance;
        heightOffset = baseHeight;
    }

    private void LateUpdate()
    {
        if (target == null) return;

        // Variation dynamique de l'angle et de la distance
        currentAngle = Mathf.SmoothDamp(currentAngle, Mathf.Sin(Time.time * angleChangeSpeed) * maxLateralAngle,
                                      ref angleVelocity, 2f);

        currentDistance = Mathf.Lerp(minDistance, maxDistance,
                                   (Mathf.Sin(Time.time * 0.1f) + 1f) * 0.5f);

        heightOffset = baseHeight + Mathf.Sin(Time.time * 0.3f) * heightVariationRange;

        // Calcul de la position de la cam�ra
        Quaternion rotation = Quaternion.Euler(0, currentAngle, 0);
        Vector3 offset = rotation * (-Vector3.forward * currentDistance) +
                        (Vector3.up * heightOffset);

        Vector3 targetPosition = target.position + offset;

        // Mouvement fluide
        transform.position = Vector3.SmoothDamp(transform.position, targetPosition, ref velocity, damping);

        // La cam�ra regarde toujours devant le bateau
        Vector3 lookAtPoint = target.position + (target.forward * 10f);
        transform.LookAt(lookAtPoint);
    }
}