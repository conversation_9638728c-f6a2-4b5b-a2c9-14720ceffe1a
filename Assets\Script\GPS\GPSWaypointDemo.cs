using UnityEngine;
using Mapbox.Utils;

/// <summary>
/// Script de démonstration pour tester le système d'intégration GPS-Waypoint
/// Peut être utilisé pour simuler des données GPS et tester le mouvement des bateaux
/// </summary>
public class GPSWaypointDemo : MonoBehaviour
{
    [Header("Demo Configuration")]
    [SerializeField] private bool enableDemo = false;
    [SerializeField] private bool autoStartDemo = false;
    [SerializeField] private float demoUpdateInterval = 1f;
    [SerializeField] private int maxDemoPoints = 10;
    
    [Header("GPS Simulation")]
    [SerializeField] private Vector2d startGPSPosition = new Vector2d(43.123456, 5.654321);
    [SerializeField] private float gpsMovementSpeed = 0.0001f; // Degrés par seconde
    [SerializeField] private float gpsNoiseLevel = 0.00001f;
    
    [Header("Target Boats")]
    [SerializeField] private GameObject[] targetBoats;
    [SerializeField] private bool autoDetectBoats = true;
    
    private GPSWaypointIntegrator integrator;
    private float lastDemoUpdate = 0f;
    private int demoPointCount = 0;
    private Vector2d currentSimulatedGPS;
    private bool isDemoRunning = false;

    private void Start()
    {
        // Trouver l'intégrateur
        integrator = FindObjectOfType<GPSWaypointIntegrator>();
        if (integrator == null)
        {
            Debug.LogWarning("[GPSWaypointDemo] Aucun GPSWaypointIntegrator trouvé dans la scène");
        }

        // Auto-détecter les bateaux si nécessaire
        if (autoDetectBoats)
        {
            DetectBoats();
        }

        // Initialiser la position GPS simulée
        currentSimulatedGPS = startGPSPosition;

        if (autoStartDemo && enableDemo)
        {
            StartDemo();
        }
    }

    private void Update()
    {
        if (!enableDemo || !isDemoRunning) return;

        // Mettre à jour la démo à intervalles réguliers
        if (Time.time - lastDemoUpdate >= demoUpdateInterval)
        {
            UpdateDemo();
            lastDemoUpdate = Time.time;
        }
    }

    /// <summary>
    /// Détecte automatiquement les bateaux dans la scène
    /// </summary>
    private void DetectBoats()
    {
        var boats = FindObjectsOfType<BoatWaypointMover>();
        targetBoats = new GameObject[boats.Length];
        
        for (int i = 0; i < boats.Length; i++)
        {
            targetBoats[i] = boats[i].gameObject;
        }
        
        Debug.Log($"[GPSWaypointDemo] {targetBoats.Length} bateaux détectés automatiquement");
    }

    /// <summary>
    /// Démarre la démonstration
    /// </summary>
    [ContextMenu("Start Demo")]
    public void StartDemo()
    {
        if (!enableDemo)
        {
            Debug.LogWarning("[GPSWaypointDemo] Démo désactivée");
            return;
        }

        if (integrator == null)
        {
            Debug.LogError("[GPSWaypointDemo] Impossible de démarrer la démo : aucun intégrateur trouvé");
            return;
        }

        // S'assurer que l'intégration est initialisée
        integrator.InitializeIntegration();

        isDemoRunning = true;
        demoPointCount = 0;
        currentSimulatedGPS = startGPSPosition;
        lastDemoUpdate = Time.time;

        Debug.Log("[GPSWaypointDemo] === DÉMONSTRATION DÉMARRÉE ===");
        Debug.Log($"Position GPS de départ: {startGPSPosition}");
        Debug.Log($"Bateaux cibles: {targetBoats.Length}");
    }

    /// <summary>
    /// Arrête la démonstration
    /// </summary>
    [ContextMenu("Stop Demo")]
    public void StopDemo()
    {
        isDemoRunning = false;
        Debug.Log("[GPSWaypointDemo] === DÉMONSTRATION ARRÊTÉE ===");
        
        if (integrator != null)
        {
            integrator.LogIntegrationStats();
        }
    }

    /// <summary>
    /// Met à jour la démonstration
    /// </summary>
    private void UpdateDemo()
    {
        if (demoPointCount >= maxDemoPoints)
        {
            Debug.Log("[GPSWaypointDemo] Nombre maximum de points atteint, arrêt de la démo");
            StopDemo();
            return;
        }

        // Simuler le mouvement GPS
        SimulateGPSMovement();

        // Envoyer les données GPS simulées aux bateaux
        SendGPSDataToBoats();

        demoPointCount++;
        
        Debug.Log($"[GPSWaypointDemo] Point GPS #{demoPointCount} envoyé: {currentSimulatedGPS}");
    }

    /// <summary>
    /// Simule le mouvement GPS
    /// </summary>
    private void SimulateGPSMovement()
    {
        // Mouvement en spirale avec du bruit
        float time = Time.time * 0.1f;
        float spiralRadius = time * gpsMovementSpeed;
        
        float deltaLat = Mathf.Sin(time) * spiralRadius;
        float deltaLon = Mathf.Cos(time) * spiralRadius;
        
        // Ajouter du bruit GPS
        deltaLat += Random.Range(-gpsNoiseLevel, gpsNoiseLevel);
        deltaLon += Random.Range(-gpsNoiseLevel, gpsNoiseLevel);
        
        currentSimulatedGPS = new Vector2d(
            startGPSPosition.x + deltaLat,
            startGPSPosition.y + deltaLon
        );
    }

    /// <summary>
    /// Envoie les données GPS simulées aux bateaux
    /// </summary>
    private void SendGPSDataToBoats()
    {
        if (targetBoats == null || targetBoats.Length == 0) return;

        foreach (var boat in targetBoats)
        {
            if (boat == null) continue;

            // Méthode 1: Via l'adaptateur direct
            var adapter = boat.GetComponent<GPSToWaypointAdapter>();
            if (adapter != null)
            {
                adapter.ForceGPSUpdate(currentSimulatedGPS);
            }
            else
            {
                // Méthode 2: Via le BoatWaypointMover direct
                var waypointMover = boat.GetComponent<BoatWaypointMover>();
                if (waypointMover != null)
                {
                    waypointMover.AddGPSPosition(currentSimulatedGPS, Time.time);
                }
            }
        }
    }

    /// <summary>
    /// Teste l'envoi d'un point GPS unique
    /// </summary>
    [ContextMenu("Send Single GPS Point")]
    public void SendSingleGPSPoint()
    {
        if (!enableDemo) return;

        Vector2d testPosition = new Vector2d(
            startGPSPosition.x + Random.Range(-0.001f, 0.001f),
            startGPSPosition.y + Random.Range(-0.001f, 0.001f)
        );

        SendGPSDataToBoats();
        Debug.Log($"[GPSWaypointDemo] Point GPS unique envoyé: {testPosition}");
    }

    /// <summary>
    /// Active/désactive le mouvement par waypoints
    /// </summary>
    [ContextMenu("Toggle Waypoint Movement")]
    public void ToggleWaypointMovement()
    {
        if (integrator != null)
        {
            integrator.ToggleWaypointMovement();
        }
    }

    /// <summary>
    /// Affiche les statistiques d'intégration
    /// </summary>
    [ContextMenu("Log Integration Stats")]
    public void LogIntegrationStats()
    {
        if (integrator != null)
        {
            integrator.LogIntegrationStats();
        }
        
        // Afficher aussi les stats des adaptateurs
        foreach (var boat in targetBoats)
        {
            if (boat == null) continue;
            
            var adapter = boat.GetComponent<GPSToWaypointAdapter>();
            if (adapter != null)
            {
                adapter.LogAdapterStats();
            }
        }
    }

    /// <summary>
    /// Réinitialise la démonstration
    /// </summary>
    [ContextMenu("Reset Demo")]
    public void ResetDemo()
    {
        StopDemo();
        demoPointCount = 0;
        currentSimulatedGPS = startGPSPosition;
        
        // Réinitialiser l'intégration
        if (integrator != null)
        {
            integrator.ManualInitializeIntegration();
        }
        
        Debug.Log("[GPSWaypointDemo] Démonstration réinitialisée");
    }

    private void OnDrawGizmosSelected()
    {
        if (!enableDemo) return;

        // Visualiser la position GPS de départ
        Gizmos.color = Color.green;
        Gizmos.DrawWireSphere(transform.position, 5f);
        
        // Visualiser la position GPS actuelle si la démo est en cours
        if (isDemoRunning)
        {
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position + Vector3.up * 10f, 3f);
        }
    }
}
