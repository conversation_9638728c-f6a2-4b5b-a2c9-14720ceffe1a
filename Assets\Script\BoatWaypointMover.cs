using UnityEngine;

[RequireComponent(typeof(Rigidbody))]
[RequireComponent(typeof(BoxCollider))]
public class BoatWaypointMover : MonoBehaviour
{
    [Header("Movement")]
    [SerializeField] private float baseSpeed = 10f;
    [SerializeField] private float speedVariation = 2f;    // Variation normale de vitesse
    [SerializeField] private float boostMultiplier = 1.5f; // Pour les acc�l�rations temporaires
    [SerializeField] private float boostDuration = 3f;     // Dur�e des acc�l�rations
    [SerializeField] private float boostChance = 0.02f;    // Chance d'acc�l�ration par seconde
    [SerializeField] private float rotationSpeed = 2f;
    [SerializeField] private float lateralMovement = 3f;
    [SerializeField] private float smoothTime = 0.3f;

    [Header("Turning")]
    [SerializeField] private float turnAnticipationDistance = 30f;  // Distance � laquelle commencer � anticiper le virage
    [SerializeField] private float maxTurnSpeed = 30f;             // Vitesse de rotation maximum
    [SerializeField] private float turnSpeedMultiplier = 0.5f;     // R�duction de vitesse dans les virages
    [SerializeField] private float minTurnRadius = 15f;            // Rayon minimum de virage

    [Header("Collision")]
    [SerializeField] private Vector3 colliderSize = new Vector3(5f, 2f, 10f);
    [SerializeField] private float pushForce = 5000f;
    [SerializeField] private float emergencyBrakeDistance = 3f;

    private Rigidbody rb;
    private BoxCollider boxCollider;
    private Transform currentWaypoint;
    private Vector3 currentVelocity;
    private float currentSpeed;
    private float targetSpeed;
    private Vector3 velocityRef;
    private float lateralOffset;
    private float noiseOffset;
    private float normalSpeed;
    private Vector3 targetPoint;
    private float boostTimer;
    private bool isBoosting;

    private void Awake()
    {
        // Configuration du Collider
        boxCollider = GetComponent<BoxCollider>();
        boxCollider.size = colliderSize;
        boxCollider.center = new Vector3(0, colliderSize.y / 2, 0);
        boxCollider.isTrigger = false;

        // Configuration du Rigidbody
        rb = GetComponent<Rigidbody>();
        rb.useGravity = false;
        rb.constraints = RigidbodyConstraints.FreezePositionY |
                        RigidbodyConstraints.FreezeRotationX |
                        RigidbodyConstraints.FreezeRotationZ;
        rb.mass = 2000f;
        rb.drag = 2f;
        rb.angularDrag = 5f;
        rb.interpolation = RigidbodyInterpolation.Interpolate;
        rb.collisionDetectionMode = CollisionDetectionMode.ContinuousDynamic;

        // Initialisation des variables de mouvement
        currentSpeed = baseSpeed;
        targetSpeed = baseSpeed;
        normalSpeed = baseSpeed;
        noiseOffset = Random.Range(0f, 1000f);
    }

    private void FixedUpdate()
    {
        if (currentWaypoint == null) return;

        CheckForCollisions();
        UpdateTargetPoint();
        HandleTurning();
        ApplyMovement();
    }

    private void UpdateTargetPoint()
    {
        // Calcul du point cible avec offset lat�ral
        lateralOffset = Mathf.PerlinNoise(Time.time * 0.5f + noiseOffset, 0) * lateralMovement;

        // Calcul d'un point d'anticipation pour le virage
        float distanceToWaypoint = Vector3.Distance(transform.position, currentWaypoint.position);
        if (distanceToWaypoint < turnAnticipationDistance)
        {
            // Cr�er un point de passage plus large autour de la bou�e
            Vector3 directionToWaypoint = (currentWaypoint.position - transform.position).normalized;
            Vector3 perpendicular = Vector3.Cross(directionToWaypoint, Vector3.up);
            targetPoint = currentWaypoint.position + (perpendicular * minTurnRadius);
        }
        else
        {
            targetPoint = currentWaypoint.position + currentWaypoint.right * lateralOffset;
        }
    }

    private void HandleTurning()
    {
        Vector3 directionToTarget = (targetPoint - transform.position).normalized;
        directionToTarget.y = 0;

        // Calcul de l'angle � tourner
        float angleToTarget = Vector3.SignedAngle(transform.forward, directionToTarget, Vector3.up);

        // Ajustement de la vitesse en fonction de l'angle
        float turnFactor = Mathf.Abs(angleToTarget) / 180f;
        float speedMultiplier = Mathf.Lerp(1f, turnSpeedMultiplier, turnFactor);

        if (!isBoosting)
        {
            targetSpeed = normalSpeed * speedMultiplier;
        }

        // Limitation de la vitesse de rotation en fonction de la vitesse
        float currentTurnSpeed = Mathf.Lerp(maxTurnSpeed, maxTurnSpeed * 0.5f, currentSpeed / baseSpeed);
        float rotationAmount = Mathf.Clamp(angleToTarget, -currentTurnSpeed, currentTurnSpeed) * rotationSpeed * Time.fixedDeltaTime;

        // Application de la rotation
        Quaternion targetRotation = rb.rotation * Quaternion.Euler(0, rotationAmount, 0);
        rb.MoveRotation(targetRotation);
    }

    private void ApplyMovement()
    {
        // Gestion du boost
        if (isBoosting)
        {
            boostTimer -= Time.fixedDeltaTime;
            if (boostTimer <= 0)
            {
                isBoosting = false;
                targetSpeed = normalSpeed;
            }
        }
        else if (Random.value < boostChance * Time.fixedDeltaTime)
        {
            // D�marrer un boost
            isBoosting = true;
            boostTimer = boostDuration;
            targetSpeed = normalSpeed * boostMultiplier;
        }
        else if (Random.value < 0.02f) // Variation normale de vitesse
        {
            normalSpeed = baseSpeed + Random.Range(-speedVariation, speedVariation);
            if (!isBoosting)
                targetSpeed = normalSpeed;
        }

        // Lissage de la vitesse
        currentSpeed = Mathf.Lerp(currentSpeed, targetSpeed, Time.fixedDeltaTime * 2f);

        // Application du mouvement
        Vector3 targetVelocity = transform.forward * currentSpeed;
        Vector3 smoothVelocity = Vector3.SmoothDamp(rb.velocity, targetVelocity, ref velocityRef, smoothTime);
        rb.velocity = smoothVelocity;
    }

    private void CheckForCollisions()
    {
        Ray frontRay = new Ray(transform.position, transform.forward);
        Ray leftRay = new Ray(transform.position, -transform.right);
        Ray rightRay = new Ray(transform.position, transform.right);

        RaycastHit hit;
        float rayDistance = colliderSize.z + emergencyBrakeDistance;

        if (Physics.Raycast(frontRay, out hit, rayDistance) ||
            Physics.Raycast(leftRay, out hit, colliderSize.x) ||
            Physics.Raycast(rightRay, out hit, colliderSize.x))
        {
            if (hit.collider.GetComponent<BoatWaypointMover>() != null)
            {
                targetSpeed = currentSpeed * 0.5f;
                Vector3 avoidanceDirection = transform.position - hit.point;
                avoidanceDirection.y = 0;
                rb.AddForce(avoidanceDirection.normalized * pushForce * Time.fixedDeltaTime, ForceMode.Impulse);
            }
        }
    }

    private void OnCollisionEnter(Collision collision)
    {
        if (collision.gameObject.GetComponent<BoatWaypointMover>() != null)
        {
            Vector3 pushDirection = transform.position - collision.transform.position;
            pushDirection.y = 0;
            rb.AddForce(pushDirection.normalized * pushForce, ForceMode.Impulse);

            currentSpeed *= 0.5f;
            targetSpeed = normalSpeed * 0.5f;
        }
    }

    public float GetCurrentSpeed()
    {
        return currentSpeed;
    }

    public void SetNextWaypoint(Transform waypoint)
    {
        currentWaypoint = waypoint;
    }

    private void OnDrawGizmosSelected()
    {
        if (currentWaypoint != null)
        {
            // Visualisation du point cible et du rayon de virage
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(targetPoint, 1f);
            Gizmos.DrawWireSphere(currentWaypoint.position, minTurnRadius);

            // Visualisation de la distance d'anticipation
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(currentWaypoint.position, turnAnticipationDistance);
        }
    }
}