using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using Mapbox.Utils;
using Mapbox.Unity.Map;

[RequireComponent(typeof(Rigidbody))]
[RequireComponent(typeof(BoxCollider))]
public class BoatWaypointMover : MonoBehaviour
{
    [Header("GPS Movement Settings")]
    [SerializeField] private float maxSpeed = 15f;                    // Vitesse maximum en m/s
    [SerializeField] private float rotationSpeed = 2f;               // Vitesse de rotation
    [SerializeField] private float pathFollowDelay = 2f;             // Délai de suivi du chemin GPS
    [SerializeField] private float inertiaFactor = 3f;               // Facteur d'inertie pour le mouvement fluide
    [SerializeField] private int minGPSPoints = 3;                   // Nombre minimum de points GPS pour commencer le mouvement
    [SerializeField] private float gpsAccuracy = 2f;                 // Précision GPS en mètres
    [SerializeField] private float lateralOffset = 2f;               // Décalage latéral pour éviter la superposition

    [Header("Turning")]
    [SerializeField] private float turnAnticipationDistance = 30f;   // Distance d'anticipation des virages
    [SerializeField] private float maxTurnSpeed = 30f;              // Vitesse de rotation maximum
    [SerializeField] private float turnSpeedMultiplier = 0.5f;      // Réduction de vitesse dans les virages
    [SerializeField] private float minTurnRadius = 15f;             // Rayon minimum de virage

    [Header("Collision")]
    [SerializeField] private Vector3 colliderSize = new Vector3(5f, 2f, 10f);
    [SerializeField] private float pushForce = 5000f;
    [SerializeField] private float emergencyBrakeDistance = 3f;

    [Header("Height Control (Y Position)")]
    [SerializeField] private float waterLevel = 0f;             // Niveau de l'eau (Y de référence)
    [SerializeField] private float boatHeightOffset = 0.5f;     // Hauteur du bateau au-dessus de l'eau
    [SerializeField] private bool enableRuntimeHeightControl = true; // Permettre l'ajustement en temps réel
    [SerializeField] private KeyCode increaseHeightKey = KeyCode.PageUp;   // Touche pour monter
    [SerializeField] private KeyCode decreaseHeightKey = KeyCode.PageDown; // Touche pour descendre
    [SerializeField] private float heightAdjustmentStep = 0.1f;  // Pas d'ajustement par pression de touche

    [Header("Debug")]
    [SerializeField] private bool showDebugInfo = false;
    [SerializeField] private bool showGPSPath = false;

    // Composants
    private Rigidbody rb;
    private BoxCollider boxCollider;
    private AbstractMap map;

    // GPS et pathfinding
    private readonly Queue<GPSPositionData> gpsBuffer = new(5);
    private readonly List<WaypointData> waypoints = new();
    private int currentWaypointIndex = 0;
    private bool hasValidPath = false;
    private float lastGPSUpdateTime = 0f;
    private float lastCalculationTime = 0f;
    private Vector3 targetPosition;

    // Mouvement
    private float currentSpeed = 0f;
    private Vector3 currentVelocity;
    private Vector3 velocityRef;
    private float noiseOffset;

    // Structures de données pour GPS et pathfinding
    /// <summary>
    /// Structure pour stocker les données de position GPS
    /// </summary>
    private struct GPSPositionData
    {
        public Vector2d gpsCoordinates;
        public Vector3 unityPosition;
        public float speed;
        public float timestamp;

        public GPSPositionData(Vector2d gps, Vector3 unity, float spd, float time)
        {
            gpsCoordinates = gps;
            unityPosition = unity;
            speed = spd;
            timestamp = time;
        }
    }

    /// <summary>
    /// Structure pour stocker les données de waypoint
    /// </summary>
    private struct WaypointData
    {
        public Vector3 position;
        public float speed;
        public float arrivalTime;

        public WaypointData(Vector3 pos, float spd, float time)
        {
            position = pos;
            speed = spd;
            arrivalTime = time;
        }
    }

    private void Awake()
    {
        // Configuration du Collider
        boxCollider = GetComponent<BoxCollider>();
        boxCollider.size = colliderSize;
        boxCollider.center = new Vector3(0, colliderSize.y / 2, 0);
        boxCollider.isTrigger = false;

        // Configuration du Rigidbody
        rb = GetComponent<Rigidbody>();
        rb.useGravity = false;
        // SUPPRIMÉ FreezePositionY pour permettre le contrôle de hauteur
        rb.constraints = RigidbodyConstraints.FreezeRotationX |
                        RigidbodyConstraints.FreezeRotationZ;
        rb.mass = 2000f;
        rb.drag = 2f;
        rb.angularDrag = 5f;
        rb.interpolation = RigidbodyInterpolation.Interpolate;
        rb.collisionDetectionMode = CollisionDetectionMode.ContinuousDynamic;

        // Initialisation des variables de mouvement
        currentSpeed = 0f;
        noiseOffset = Random.Range(0f, 1000f);

        // Recherche de la carte Mapbox
        map = FindObjectOfType<AbstractMap>();
        if (map == null)
        {
            Debug.LogError($"[BoatWaypointMover] {gameObject.name}: Aucune carte Mapbox trouvée dans la scène!");
        }
    }

    private void Update()
    {
        // Contrôle de hauteur en temps réel
        if (enableRuntimeHeightControl && Application.isPlaying)
        {
            HandleHeightControl();
        }

        // Appliquer le contrôle de hauteur Y
        ApplyHeightControl();
    }

    private void FixedUpdate()
    {
        // Vérifier si nous avons assez de points GPS pour commencer le mouvement
        if (gpsBuffer.Count < minGPSPoints)
        {
            if (showDebugInfo)
                Debug.Log($"[BoatWaypointMover] {gameObject.name}: En attente de {minGPSPoints} points GPS (actuellement: {gpsBuffer.Count})");
            return;
        }

        CheckForCollisions();
        UpdateGPSMovement();
    }

    /// <summary>
    /// Ajoute une nouvelle position GPS au buffer et recalcule le chemin
    /// </summary>
    public void AddGPSPosition(Vector2d gpsCoordinates, float timestamp)
    {
        if (map == null)
        {
            Debug.LogWarning($"[BoatWaypointMover] {gameObject.name}: Carte non initialisée, impossible d'ajouter la position GPS");
            return;
        }

        // Convertir GPS vers position Unity - utiliser notre système de hauteur
        Vector3 unityPosition = map.GeoToWorldPosition(gpsCoordinates);
        unityPosition.y = waterLevel + boatHeightOffset; // Utiliser notre système de hauteur

        // Calculer la vitesse à partir des positions précédentes
        float speed = CalculateSpeedFromGPS(gpsCoordinates, timestamp);

        // Vérifier si la position est significativement différente de la dernière
        bool isSignificantMovement = IsSignificantMovement(unityPosition);

        // Ajouter la nouvelle position au buffer
        if (gpsBuffer.Count >= 5) // Limite du buffer
        {
            gpsBuffer.Dequeue();
        }

        gpsBuffer.Enqueue(new GPSPositionData(gpsCoordinates, unityPosition, speed, timestamp));

        // Recalculer le path si nécessaire
        bool shouldRecalculate = (gpsBuffer.Count >= minGPSPoints && isSignificantMovement) ||
                                (Time.time - lastCalculationTime > 5.0f);

        if (shouldRecalculate)
        {
            RecalculatePath();
            lastCalculationTime = Time.time;
        }

        lastGPSUpdateTime = timestamp;

        if (showDebugInfo)
            Debug.Log($"[BoatWaypointMover] {gameObject.name}: Position GPS ajoutée - Vitesse: {speed:F1} m/s");
    }

    /// <summary>
    /// Calcule la vitesse à partir des coordonnées GPS et du timestamp
    /// </summary>
    private float CalculateSpeedFromGPS(Vector2d newGPSPosition, float timestamp)
    {
        if (gpsBuffer.Count == 0) return 0f;

        var lastGPSData = gpsBuffer.Last();

        // Calculer la distance en mètres entre les deux positions GPS
        float distance = CalculateGPSDistance(lastGPSData.gpsCoordinates, newGPSPosition);

        // Si la distance est inférieure à la précision GPS, considérer que le bateau est immobile
        if (distance < gpsAccuracy) return 0f;

        // Calculer le temps écoulé depuis la dernière mise à jour (en secondes)
        float timeElapsed = timestamp - lastGPSData.timestamp;
        if (timeElapsed <= 0) return lastGPSData.speed; // Éviter la division par zéro

        // Calculer la vitesse en m/s
        float speedMS = distance / timeElapsed;

        // Limiter la vitesse maximale
        return Mathf.Min(speedMS, maxSpeed);
    }

    /// <summary>
    /// Calcule la distance en mètres entre deux coordonnées GPS
    /// </summary>
    private float CalculateGPSDistance(Vector2d pos1, Vector2d pos2)
    {
        const float earthRadius = 6371000f; // Rayon de la Terre en mètres

        float lat1Rad = (float)pos1.x * Mathf.Deg2Rad;
        float lat2Rad = (float)pos2.x * Mathf.Deg2Rad;
        float deltaLatRad = (float)(pos2.x - pos1.x) * Mathf.Deg2Rad;
        float deltaLonRad = (float)(pos2.y - pos1.y) * Mathf.Deg2Rad;

        float a = Mathf.Sin(deltaLatRad / 2) * Mathf.Sin(deltaLatRad / 2) +
                  Mathf.Cos(lat1Rad) * Mathf.Cos(lat2Rad) *
                  Mathf.Sin(deltaLonRad / 2) * Mathf.Sin(deltaLonRad / 2);

        float c = 2 * Mathf.Atan2(Mathf.Sqrt(a), Mathf.Sqrt(1 - a));

        return earthRadius * c;
    }

    /// <summary>
    /// Vérifie si le mouvement est significatif par rapport à la dernière position
    /// </summary>
    private bool IsSignificantMovement(Vector3 newPosition)
    {
        if (gpsBuffer.Count == 0) return true;

        var lastPosition = gpsBuffer.Last().unityPosition;
        float distance = Vector3.Distance(lastPosition, newPosition);

        // Considérer comme significatif si la distance est supérieure à la précision GPS
        return distance > gpsAccuracy;
    }

    /// <summary>
    /// Met à jour le mouvement basé sur les données GPS
    /// </summary>
    private void UpdateGPSMovement()
    {
        if (!hasValidPath || waypoints.Count == 0) return;

        // Mettre à jour la cible actuelle
        UpdateTarget();

        // Calculer la direction vers la cible
        Vector3 directionToTarget = (targetPosition - transform.position).normalized;
        directionToTarget.y = 0;

        if (directionToTarget != Vector3.zero)
        {
            // Calculer la rotation avec lissage
            Quaternion targetRotation = Quaternion.LookRotation(directionToTarget);
            float speedRatio = Mathf.Clamp01(currentSpeed / maxSpeed);
            float currentTurnSpeed = Mathf.Lerp(rotationSpeed * 0.5f, rotationSpeed, speedRatio);

            transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, currentTurnSpeed * Time.fixedDeltaTime);
        }

        // Calculer la vitesse cible basée sur les waypoints
        float targetSpeed = CalculateTargetSpeed();

        // Appliquer l'inertie pour un mouvement fluide
        currentSpeed = Mathf.Lerp(currentSpeed, targetSpeed, inertiaFactor * Time.fixedDeltaTime);

        // Appliquer le mouvement avec décalage latéral
        ApplyGPSMovement();
    }

    /// <summary>
    /// Recalcule le chemin à partir des points GPS stockés
    /// </summary>
    private void RecalculatePath()
    {
        var points = gpsBuffer.ToArray();
        waypoints.Clear();

        if (points.Length < minGPSPoints)
        {
            hasValidPath = false;
            return;
        }

        // Créer des waypoints interpolés pour un mouvement fluide
        for (int i = 0; i < points.Length; i++)
        {
            // Ajouter un décalage latéral pour éviter la superposition
            Vector3 lateralOffsetVector = CalculateLateralOffset(points[i].unityPosition);
            Vector3 adjustedPosition = points[i].unityPosition + lateralOffsetVector;

            waypoints.Add(new WaypointData(
                adjustedPosition,
                points[i].speed,
                points[i].timestamp + pathFollowDelay
            ));
        }

        // Ajouter des points interpolés entre les waypoints pour un mouvement plus fluide
        if (waypoints.Count >= 2)
        {
            var originalWaypoints = waypoints.ToList();
            waypoints.Clear();

            for (int i = 0; i < originalWaypoints.Count - 1; i++)
            {
                waypoints.Add(originalWaypoints[i]);

                // Ajouter un point interpolé entre chaque paire de waypoints
                Vector3 midPoint = Vector3.Lerp(originalWaypoints[i].position, originalWaypoints[i + 1].position, 0.5f);
                float midSpeed = (originalWaypoints[i].speed + originalWaypoints[i + 1].speed) * 0.5f;
                float midTime = (originalWaypoints[i].arrivalTime + originalWaypoints[i + 1].arrivalTime) * 0.5f;

                waypoints.Add(new WaypointData(midPoint, midSpeed, midTime));
            }

            // Ajouter le dernier waypoint
            waypoints.Add(originalWaypoints[originalWaypoints.Count - 1]);
        }

        currentWaypointIndex = 0;
        hasValidPath = waypoints.Count > 0;

        if (hasValidPath)
        {
            targetPosition = waypoints[0].position;
            if (showDebugInfo)
                Debug.Log($"[BoatWaypointMover] {gameObject.name}: Chemin recalculé avec {waypoints.Count} waypoints");
        }
    }

    /// <summary>
    /// Calcule le décalage latéral pour éviter la superposition des bateaux
    /// </summary>
    private Vector3 CalculateLateralOffset(Vector3 position)
    {
        // Utiliser le bruit de Perlin basé sur la position pour créer un décalage latéral naturel
        float offsetX = Mathf.PerlinNoise(position.x * 0.01f + noiseOffset, Time.time * 0.1f) * lateralOffset;
        float offsetZ = Mathf.PerlinNoise(position.z * 0.01f + noiseOffset, Time.time * 0.1f) * lateralOffset;

        return new Vector3(offsetX, 0, offsetZ);
    }

    /// <summary>
    /// Met à jour la cible actuelle en fonction du temps écoulé
    /// </summary>
    private void UpdateTarget()
    {
        if (!hasValidPath || waypoints.Count == 0) return;

        // Calculer le temps écoulé depuis la dernière mise à jour GPS
        float timeSinceLastUpdate = Time.time - lastGPSUpdateTime;

        // Si nous avons dépassé le délai de suivi, rester sur le dernier point
        if (timeSinceLastUpdate > pathFollowDelay * 2)
        {
            if (waypoints.Count > 0)
            {
                targetPosition = waypoints[^1].position;
                currentWaypointIndex = waypoints.Count - 1;
            }
            return;
        }

        // Vérifier si nous avons atteint le waypoint actuel
        if (currentWaypointIndex < waypoints.Count)
        {
            float distance = Vector3.Distance(transform.position, waypoints[currentWaypointIndex].position);

            if (distance < 2f && currentWaypointIndex < waypoints.Count - 1) // Waypoint atteint
            {
                currentWaypointIndex++;
            }

            targetPosition = waypoints[currentWaypointIndex].position;
        }
    }

    /// <summary>
    /// Calcule la vitesse cible basée sur les waypoints
    /// </summary>
    private float CalculateTargetSpeed()
    {
        if (!hasValidPath || waypoints.Count == 0) return 0f;

        if (currentWaypointIndex < waypoints.Count)
        {
            float waypointSpeed = waypoints[currentWaypointIndex].speed;

            // Si nous sommes proches du dernier waypoint, ralentir progressivement
            if (currentWaypointIndex == waypoints.Count - 1)
            {
                float distance = Vector3.Distance(transform.position, targetPosition);
                return Mathf.Min(distance / pathFollowDelay, waypointSpeed) * 0.8f;
            }

            return waypointSpeed;
        }

        return 0f;
    }

    /// <summary>
    /// Applique le mouvement GPS avec le Rigidbody
    /// </summary>
    private void ApplyGPSMovement()
    {
        if (currentSpeed <= 0.1f) return;

        // Déplacer vers le waypoint
        Vector3 targetVelocity = transform.forward * currentSpeed;
        Vector3 smoothVelocity = Vector3.SmoothDamp(rb.velocity, targetVelocity, ref velocityRef, 0.3f);
        rb.velocity = smoothVelocity;
    }

    private void CheckForCollisions()
    {
        Ray frontRay = new Ray(transform.position, transform.forward);
        Ray leftRay = new Ray(transform.position, -transform.right);
        Ray rightRay = new Ray(transform.position, transform.right);

        RaycastHit hit;
        float rayDistance = colliderSize.z + emergencyBrakeDistance;

        if (Physics.Raycast(frontRay, out hit, rayDistance) ||
            Physics.Raycast(leftRay, out hit, colliderSize.x) ||
            Physics.Raycast(rightRay, out hit, colliderSize.x))
        {
            if (hit.collider.GetComponent<BoatWaypointMover>() != null)
            {
                // Réduire la vitesse en cas d'obstacle
                currentSpeed *= 0.5f;
                Vector3 avoidanceDirection = transform.position - hit.point;
                avoidanceDirection.y = 0;
                rb.AddForce(avoidanceDirection.normalized * pushForce * Time.fixedDeltaTime, ForceMode.Impulse);
            }
        }
    }

    private void OnCollisionEnter(Collision collision)
    {
        if (collision.gameObject.GetComponent<BoatWaypointMover>() != null)
        {
            Vector3 pushDirection = transform.position - collision.transform.position;
            pushDirection.y = 0;
            rb.AddForce(pushDirection.normalized * pushForce, ForceMode.Impulse);

            // Réduire la vitesse en cas de collision
            currentSpeed *= 0.5f;
        }
    }

    public float GetCurrentSpeed()
    {
        return currentSpeed;
    }

    /// <summary>
    /// Méthode publique pour obtenir la vitesse actuelle en nœuds
    /// </summary>
    public float GetCurrentSpeedInKnots()
    {
        return currentSpeed * 1.944f; // Conversion m/s vers nœuds
    }

    /// <summary>
    /// Méthode publique pour vérifier si le bateau a un chemin valide
    /// </summary>
    public bool HasValidPath()
    {
        return hasValidPath && waypoints.Count > 0;
    }

    /// <summary>
    /// Méthode publique pour obtenir le nombre de points GPS dans le buffer
    /// </summary>
    public int GetGPSBufferCount()
    {
        return gpsBuffer.Count;
    }

    private void OnDrawGizmosSelected()
    {
        if (!showGPSPath) return;

        // Visualisation du chemin GPS
        if (hasValidPath && waypoints.Count > 0)
        {
            Gizmos.color = Color.green;
            for (int i = 0; i < waypoints.Count - 1; i++)
            {
                Gizmos.DrawLine(waypoints[i].position, waypoints[i + 1].position);
            }

            // Visualisation de la cible actuelle
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(targetPosition, 2f);

            // Visualisation des waypoints
            Gizmos.color = Color.blue;
            foreach (var waypoint in waypoints)
            {
                Gizmos.DrawWireSphere(waypoint.position, 1f);
            }
        }

        // Visualisation du buffer GPS
        if (gpsBuffer.Count > 0)
        {
            Gizmos.color = Color.red;
            var gpsPoints = gpsBuffer.ToArray();
            for (int i = 0; i < gpsPoints.Length - 1; i++)
            {
                Gizmos.DrawLine(gpsPoints[i].unityPosition, gpsPoints[i + 1].unityPosition);
            }
        }
    }

    /// <summary>
    /// Gère le contrôle de hauteur en temps réel
    /// </summary>
    private void HandleHeightControl()
    {
        bool heightChanged = false;

        if (Input.GetKeyDown(increaseHeightKey))
        {
            boatHeightOffset += heightAdjustmentStep;
            heightChanged = true;
            Debug.Log($"[BoatWaypointMover] {gameObject.name}: Hauteur augmentée à {boatHeightOffset:F2}m (Y = {waterLevel + boatHeightOffset:F2})");
        }
        else if (Input.GetKeyDown(decreaseHeightKey))
        {
            boatHeightOffset -= heightAdjustmentStep;
            heightChanged = true;
            Debug.Log($"[BoatWaypointMover] {gameObject.name}: Hauteur diminuée à {boatHeightOffset:F2}m (Y = {waterLevel + boatHeightOffset:F2})");
        }

        // Afficher les instructions la première fois
        if (heightChanged && Time.time < 10f)
        {
            Debug.Log($"[BoatWaypointMover] Contrôles de hauteur: {increaseHeightKey} = Monter, {decreaseHeightKey} = Descendre");
        }
    }

    /// <summary>
    /// Applique le contrôle de hauteur Y
    /// </summary>
    private void ApplyHeightControl()
    {
        // Forcer la position Y du Rigidbody
        Vector3 currentPos = rb.position;
        float targetY = waterLevel + boatHeightOffset;

        if (Mathf.Abs(currentPos.y - targetY) > 0.01f)
        {
            currentPos.y = targetY;
            rb.MovePosition(currentPos);
        }
    }

    /// <summary>
    /// Augmente la hauteur du bateau
    /// </summary>
    [ContextMenu("Increase Height")]
    public void IncreaseHeight()
    {
        boatHeightOffset += heightAdjustmentStep;
        Debug.Log($"[BoatWaypointMover] {gameObject.name}: Hauteur augmentée à {boatHeightOffset:F2}m");
    }

    /// <summary>
    /// Diminue la hauteur du bateau
    /// </summary>
    [ContextMenu("Decrease Height")]
    public void DecreaseHeight()
    {
        boatHeightOffset -= heightAdjustmentStep;
        Debug.Log($"[BoatWaypointMover] {gameObject.name}: Hauteur diminuée à {boatHeightOffset:F2}m");
    }

    /// <summary>
    /// Remet la hauteur à zéro (niveau de l'eau)
    /// </summary>
    [ContextMenu("Reset Height to Water Level")]
    public void ResetHeightToWaterLevel()
    {
        boatHeightOffset = 0f;
        Debug.Log($"[BoatWaypointMover] {gameObject.name}: Hauteur remise au niveau de l'eau");
    }

    /// <summary>
    /// Définit une hauteur personnalisée
    /// </summary>
    public void SetCustomHeight(float height)
    {
        boatHeightOffset = height;
        Debug.Log($"[BoatWaypointMover] {gameObject.name}: Hauteur définie à {boatHeightOffset:F2}m");
    }

    /// <summary>
    /// Définit le niveau de l'eau
    /// </summary>
    public void SetWaterLevel(float level)
    {
        waterLevel = level;
        Debug.Log($"[BoatWaypointMover] {gameObject.name}: Niveau de l'eau défini à {waterLevel:F2}m");
    }

    /// <summary>
    /// Obtient la hauteur Y actuelle du bateau
    /// </summary>
    public float GetCurrentYPosition()
    {
        return waterLevel + boatHeightOffset;
    }

    /// <summary>
    /// Debug spécifique pour les problèmes de hauteur Y
    /// </summary>
    [ContextMenu("Debug Height Issues")]
    public void DebugHeightIssues()
    {
        Debug.Log($"🔍 === DEBUG HAUTEUR POUR {gameObject.name} ===");
        Debug.Log($"💧 Niveau de l'eau (waterLevel): {waterLevel:F2}m");
        Debug.Log($"⚓ Offset bateau (boatHeightOffset): {boatHeightOffset:F2}m");
        Debug.Log($"📐 Hauteur Y calculée: {waterLevel + boatHeightOffset:F2}m");
        Debug.Log($"📍 Position Y réelle du transform: {transform.position.y:F2}m");
        Debug.Log($"📍 Position Y réelle du Rigidbody: {rb.position.y:F2}m");
        Debug.Log($"🎮 Contrôle runtime activé: {enableRuntimeHeightControl}");
        Debug.Log($"⌨️ Touches de contrôle: {increaseHeightKey} (monter) / {decreaseHeightKey} (descendre)");
        Debug.Log($"📏 Pas d'ajustement: {heightAdjustmentStep:F2}m");
        Debug.Log($"🔒 Contraintes Rigidbody: {rb.constraints}");
        Debug.Log("=== FIN DEBUG HAUTEUR ===");
    }
}