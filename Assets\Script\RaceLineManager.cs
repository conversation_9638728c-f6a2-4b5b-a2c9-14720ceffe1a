using UnityEngine;

/// <summary>
/// Gestionnaire des lignes de départ et d'arrivée entre le bateau sécurité et les bouées B1/B2
/// </summary>
public class RaceLineManager : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private bool autoDetectSecurityBoat = true;
    [SerializeField] private string securityBoatNodeId = "!da5394d0"; // NodeId du bateau sécurité
    [SerializeField] private bool showStartingLine = true;
    [SerializeField] private bool showFinishLine = true;
    [SerializeField] private bool acceptManualBuoys = true; // Accepter les bouées placées manuellement
    [SerializeField] private bool prioritizeManualBuoys = false; // Donner priorité aux bouées manuelles

    [Header("Ligne de Départ")]
    [SerializeField] private Color startLineColor = Color.green;
    [SerializeField] private float startLineWidth = 0.8f;
    [SerializeField] private float lineHeight = 2f;

    [Header("Ligne d'Arrivée")]
    [SerializeField] private Color finishLineColor = Color.red;
    [SerializeField] private float finishLineWidth = 0.8f;

    [Header("Debug")]
    [SerializeField] private bool showDebugLogs = true;

    // Références aux objets
    private Transform securityBoat;
    private Transform startBuoy; // B1 (automatique ou manuelle)
    private Transform finishBuoy; // B2 (automatique ou manuelle)
    private Transform automaticStartBuoy; // B1 automatique (Meshtastic)
    private Transform automaticFinishBuoy; // B2 automatique (Meshtastic)
    private Transform manualStartBuoy; // B1 manuelle
    private Transform manualFinishBuoy; // B2 manuelle

    // Composants de ligne
    private LineRenderer startLineRenderer;
    private LineRenderer finishLineRenderer;
    private GameObject startLineObject;
    private GameObject finishLineObject;

    // État
    private bool isInitialized = false;

    private void Start()
    {
        // S'abonner aux événements du MeshtasticGPSTracker
        var gpsTracker = FindObjectOfType<MeshtasticGPSTracker>();
        if (gpsTracker != null)
        {
            // Utiliser la réflexion pour s'abonner à l'événement OnStartEndBuoysPlaced
            var eventInfo = typeof(MeshtasticGPSTracker).GetEvent("OnStartEndBuoysPlaced");
            if (eventInfo != null)
            {
                var handler = System.Delegate.CreateDelegate(eventInfo.EventHandlerType, this, "OnBuoysPlaced");
                eventInfo.AddEventHandler(gpsTracker, handler);
                LogMessage("✅ Abonné aux événements de placement des bouées");
            }
        }

        if (autoDetectSecurityBoat)
        {
            DetectSecurityBoat();
        }
    }

    /// <summary>
    /// Détecte automatiquement le bateau sécurité
    /// </summary>
    private void DetectSecurityBoat()
    {
        var gpsTracker = FindObjectOfType<MeshtasticGPSTracker>();
        if (gpsTracker == null)
        {
            LogMessage("❌ MeshtasticGPSTracker non trouvé");
            return;
        }

        // Utiliser la réflexion pour accéder à la liste des bateaux
        var boatsField = typeof(MeshtasticGPSTracker).GetField("boats",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

        if (boatsField != null)
        {
            var boats = boatsField.GetValue(gpsTracker) as System.Collections.IList;
            if (boats != null)
            {
                foreach (var boat in boats)
                {
                    // Obtenir le nodeId du bateau
                    var nodeIdField = boat.GetType().GetField("associatedMacAddress");
                    if (nodeIdField != null)
                    {
                        var nodeId = nodeIdField.GetValue(boat) as string;
                        if (nodeId == securityBoatNodeId)
                        {
                            // Obtenir le GameObject du bateau
                            var boatObjectField = boat.GetType().GetField("boatObject");
                            if (boatObjectField != null)
                            {
                                var boatObject = boatObjectField.GetValue(boat) as GameObject;
                                if (boatObject != null)
                                {
                                    securityBoat = boatObject.transform;
                                    LogMessage($"✅ Bateau sécurité détecté: {boatObject.name}");
                                    CheckInitialization();
                                    return;
                                }
                            }
                        }
                    }
                }
            }
        }

        LogMessage($"⚠️ Bateau sécurité avec NodeId {securityBoatNodeId} non trouvé");
    }

    /// <summary>
    /// Appelé quand les bouées sont placées par le MeshtasticGPSTracker (automatique)
    /// </summary>
    public void OnBuoysPlaced(GameObject startBuoyObj, GameObject finishBuoyObj)
    {
        LogMessage("🎯 Événement de placement des bouées automatiques reçu");

        if (startBuoyObj != null)
        {
            automaticStartBuoy = startBuoyObj.transform;
            LogMessage($"✅ Bouée de départ automatique (B1) détectée: {startBuoyObj.name}");
        }

        if (finishBuoyObj != null)
        {
            automaticFinishBuoy = finishBuoyObj.transform;
            LogMessage($"✅ Bouée d'arrivée automatique (B2) détectée: {finishBuoyObj.name}");
        }

        UpdateActiveBuoys();
        CheckInitialization();
    }

    /// <summary>
    /// Appelé quand les bouées sont placées manuellement
    /// </summary>
    public void OnManualBuoysPlaced(GameObject startBuoyObj, GameObject finishBuoyObj)
    {
        if (!acceptManualBuoys)
        {
            LogMessage("⚠️ Bouées manuelles ignorées (acceptManualBuoys = false)");
            return;
        }

        LogMessage("🎯 Événement de placement des bouées manuelles reçu");

        if (startBuoyObj != null)
        {
            manualStartBuoy = startBuoyObj.transform;
            LogMessage($"✅ Bouée de départ manuelle (B1) détectée: {startBuoyObj.name}");
        }

        if (finishBuoyObj != null)
        {
            manualFinishBuoy = finishBuoyObj.transform;
            LogMessage($"✅ Bouée d'arrivée manuelle (B2) détectée: {finishBuoyObj.name}");
        }

        UpdateActiveBuoys();
        CheckInitialization();
    }

    /// <summary>
    /// Met à jour les bouées actives selon la priorité
    /// </summary>
    private void UpdateActiveBuoys()
    {
        // Déterminer quelles bouées utiliser selon la priorité
        if (prioritizeManualBuoys)
        {
            // Priorité aux bouées manuelles
            startBuoy = manualStartBuoy != null ? manualStartBuoy : automaticStartBuoy;
            finishBuoy = manualFinishBuoy != null ? manualFinishBuoy : automaticFinishBuoy;
            LogMessage("🔄 Priorité donnée aux bouées manuelles");
        }
        else
        {
            // Priorité aux bouées automatiques
            startBuoy = automaticStartBuoy != null ? automaticStartBuoy : manualStartBuoy;
            finishBuoy = automaticFinishBuoy != null ? automaticFinishBuoy : manualFinishBuoy;
            LogMessage("🔄 Priorité donnée aux bouées automatiques");
        }

        LogMessage($"Bouées actives - Départ: {(startBuoy != null ? startBuoy.name : "Aucune")}, " +
                  $"Arrivée: {(finishBuoy != null ? finishBuoy.name : "Aucune")}");
    }

    /// <summary>
    /// Vérifie si tous les éléments sont disponibles pour initialiser les lignes
    /// </summary>
    private void CheckInitialization()
    {
        if (securityBoat != null && startBuoy != null && finishBuoy != null && !isInitialized)
        {
            InitializeRaceLines();
        }
    }

    /// <summary>
    /// Initialise les lignes de départ et d'arrivée
    /// </summary>
    private void InitializeRaceLines()
    {
        LogMessage("🏁 === INITIALISATION DES LIGNES DE COURSE ===");

        // Créer la ligne de départ (bateau sécurité <-> bouée B1)
        if (showStartingLine)
        {
            CreateStartLine();
        }

        // Créer la ligne d'arrivée (bateau sécurité <-> bouée B2)
        if (showFinishLine)
        {
            CreateFinishLine();
        }

        isInitialized = true;
        LogMessage("✅ Lignes de course initialisées avec succès");
    }

    /// <summary>
    /// Crée la ligne de départ
    /// </summary>
    private void CreateStartLine()
    {
        startLineObject = new GameObject("StartLine");
        startLineObject.transform.SetParent(transform);

        startLineRenderer = startLineObject.AddComponent<LineRenderer>();
        ConfigureLineRenderer(startLineRenderer, startLineColor, startLineWidth);

        LogMessage("✅ Ligne de départ créée");
    }

    /// <summary>
    /// Crée la ligne d'arrivée
    /// </summary>
    private void CreateFinishLine()
    {
        finishLineObject = new GameObject("FinishLine");
        finishLineObject.transform.SetParent(transform);

        finishLineRenderer = finishLineObject.AddComponent<LineRenderer>();
        ConfigureLineRenderer(finishLineRenderer, finishLineColor, finishLineWidth);

        LogMessage("✅ Ligne d'arrivée créée");
    }

    /// <summary>
    /// Configure un LineRenderer
    /// </summary>
    private void ConfigureLineRenderer(LineRenderer lineRenderer, Color color, float width)
    {
        lineRenderer.startWidth = width;
        lineRenderer.endWidth = width;
        lineRenderer.material = new Material(Shader.Find("Sprites/Default"));
        lineRenderer.startColor = color;
        lineRenderer.endColor = color;
        lineRenderer.positionCount = 2;
        lineRenderer.useWorldSpace = true;
        lineRenderer.sortingOrder = 10; // Au-dessus des autres éléments
    }

    private void Update()
    {
        if (!isInitialized) return;

        // Mettre à jour les lignes en temps réel
        UpdateLines();
    }

    /// <summary>
    /// Met à jour les positions des lignes
    /// </summary>
    private void UpdateLines()
    {
        if (securityBoat == null) return;

        Vector3 securityPosition = securityBoat.position;

        // Mettre à jour la ligne de départ
        if (startLineRenderer != null && startBuoy != null && showStartingLine)
        {
            Vector3 securityLinePos = new Vector3(securityPosition.x, securityPosition.y + lineHeight, securityPosition.z);
            Vector3 startBuoyPos = new Vector3(startBuoy.position.x, startBuoy.position.y + lineHeight, startBuoy.position.z);

            startLineRenderer.SetPosition(0, securityLinePos);
            startLineRenderer.SetPosition(1, startBuoyPos);
        }

        // Mettre à jour la ligne d'arrivée
        if (finishLineRenderer != null && finishBuoy != null && showFinishLine)
        {
            Vector3 securityLinePos = new Vector3(securityPosition.x, securityPosition.y + lineHeight, securityPosition.z);
            Vector3 finishBuoyPos = new Vector3(finishBuoy.position.x, finishBuoy.position.y + lineHeight, finishBuoy.position.z);

            finishLineRenderer.SetPosition(0, securityLinePos);
            finishLineRenderer.SetPosition(1, finishBuoyPos);
        }
    }

    /// <summary>
    /// Active/désactive la ligne de départ
    /// </summary>
    [ContextMenu("Toggle Start Line")]
    public void ToggleStartLine()
    {
        showStartingLine = !showStartingLine;
        if (startLineRenderer != null)
        {
            startLineRenderer.enabled = showStartingLine;
        }
        LogMessage($"Ligne de départ: {(showStartingLine ? "Activée" : "Désactivée")}");
    }

    /// <summary>
    /// Active/désactive la ligne d'arrivée
    /// </summary>
    [ContextMenu("Toggle Finish Line")]
    public void ToggleFinishLine()
    {
        showFinishLine = !showFinishLine;
        if (finishLineRenderer != null)
        {
            finishLineRenderer.enabled = showFinishLine;
        }
        LogMessage($"Ligne d'arrivée: {(showFinishLine ? "Activée" : "Désactivée")}");
    }

    /// <summary>
    /// Réinitialise le système
    /// </summary>
    [ContextMenu("Reset Race Lines")]
    public void ResetRaceLines()
    {
        if (startLineObject != null) DestroyImmediate(startLineObject);
        if (finishLineObject != null) DestroyImmediate(finishLineObject);

        startLineRenderer = null;
        finishLineRenderer = null;
        isInitialized = false;

        LogMessage("🔄 Lignes de course réinitialisées");

        // Redétecter les éléments
        DetectSecurityBoat();
        CheckInitialization();
    }

    /// <summary>
    /// Affiche les informations de debug
    /// </summary>
    [ContextMenu("Show Debug Info")]
    public void ShowDebugInfo()
    {
        LogMessage("📊 === INFORMATIONS DE DEBUG ===");
        LogMessage($"Bateau sécurité: {(securityBoat != null ? securityBoat.name : "Non trouvé")}");
        LogMessage($"Bouée départ (B1): {(startBuoy != null ? startBuoy.name : "Non trouvée")}");
        LogMessage($"Bouée arrivée (B2): {(finishBuoy != null ? finishBuoy.name : "Non trouvée")}");
        LogMessage($"Ligne de départ: {(startLineRenderer != null && showStartingLine ? "Active" : "Inactive")}");
        LogMessage($"Ligne d'arrivée: {(finishLineRenderer != null && showFinishLine ? "Active" : "Inactive")}");
        LogMessage($"Système initialisé: {isInitialized}");
    }

    /// <summary>
    /// Affiche un message de log si les logs de debug sont activés
    /// </summary>
    private void LogMessage(string message)
    {
        if (showDebugLogs)
        {
            Debug.Log($"[RaceLineManager] {message}");
        }
    }
}
