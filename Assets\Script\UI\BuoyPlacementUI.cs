using UnityEngine;
using UnityEngine.UI;
using TMPro;
using Mapbox.Utils;

/// <summary>
/// Interface utilisateur pour le placement manuel des bouées
/// Permet de saisir les coordonnées GPS et de placer les bouées facilement
/// </summary>
public class BuoyPlacementUI : MonoBehaviour
{
    [Header("UI References")]
    [SerializeField] private GameObject uiPanel;
    [SerializeField] private TMP_InputField startLatitudeInput;
    [SerializeField] private TMP_InputField startLongitudeInput;
    [SerializeField] private TMP_InputField finishLatitudeInput;
    [SerializeField] private TMP_InputField finishLongitudeInput;
    [SerializeField] private Button placeStartButton;
    [SerializeField] private Button placeFinishButton;
    [SerializeField] private Button placeBothButton;
    [SerializeField] private Button clearButton;
    [SerializeField] private Toggle manualModeToggle;
    [SerializeField] private Toggle overrideToggle;
    [SerializeField] private TextMeshProUGUI statusText;
    
    [Header("Configuration")]
    [SerializeField] private KeyCode toggleUIKey = KeyCode.B;
    [SerializeField] private bool showUIOnStart = false;
    [SerializeField] private bool autoCreateUI = true;
    
    [Header("Default Positions")]
    [SerializeField] private Vector2d defaultStartPosition = new Vector2d(43.123456, 5.654321);
    [SerializeField] private Vector2d defaultFinishPosition = new Vector2d(43.124456, 5.655321);

    // Références
    private ManualBuoyPlacer manualPlacer;
    private Canvas canvas;

    private void Start()
    {
        InitializeUI();
        
        if (showUIOnStart)
        {
            ShowUI();
        }
        else
        {
            HideUI();
        }
    }

    private void Update()
    {
        if (Input.GetKeyDown(toggleUIKey))
        {
            ToggleUI();
        }
    }

    /// <summary>
    /// Initialise l'interface utilisateur
    /// </summary>
    private void InitializeUI()
    {
        // Trouver ou créer le ManualBuoyPlacer
        manualPlacer = FindObjectOfType<ManualBuoyPlacer>();
        if (manualPlacer == null)
        {
            GameObject placerObject = new GameObject("Manual Buoy Placer");
            manualPlacer = placerObject.AddComponent<ManualBuoyPlacer>();
            Debug.Log("[BuoyPlacementUI] ManualBuoyPlacer créé automatiquement");
        }

        // Créer l'UI automatiquement si nécessaire
        if (autoCreateUI && uiPanel == null)
        {
            CreateUI();
        }

        // Configurer les événements des boutons
        SetupButtonEvents();
        
        // Initialiser les valeurs par défaut
        SetDefaultValues();
        
        Debug.Log("[BuoyPlacementUI] Interface utilisateur initialisée");
    }

    /// <summary>
    /// Crée automatiquement l'interface utilisateur
    /// </summary>
    private void CreateUI()
    {
        // Créer le Canvas principal
        GameObject canvasObject = new GameObject("Buoy Placement Canvas");
        canvas = canvasObject.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvas.sortingOrder = 100;
        
        canvasObject.AddComponent<CanvasScaler>();
        canvasObject.AddComponent<GraphicRaycaster>();

        // Créer le panel principal
        GameObject panelObject = new GameObject("Buoy Panel");
        panelObject.transform.SetParent(canvasObject.transform, false);
        
        Image panelImage = panelObject.AddComponent<Image>();
        panelImage.color = new Color(0, 0, 0, 0.8f);
        
        RectTransform panelRect = panelObject.GetComponent<RectTransform>();
        panelRect.anchorMin = new Vector2(0.02f, 0.7f);
        panelRect.anchorMax = new Vector2(0.35f, 0.98f);
        panelRect.offsetMin = Vector2.zero;
        panelRect.offsetMax = Vector2.zero;
        
        uiPanel = panelObject;

        // Créer le contenu de l'UI
        CreateUIContent(panelObject);
        
        Debug.Log("[BuoyPlacementUI] Interface utilisateur créée automatiquement");
    }

    /// <summary>
    /// Crée le contenu de l'interface utilisateur
    /// </summary>
    private void CreateUIContent(GameObject parent)
    {
        // Layout vertical
        VerticalLayoutGroup layout = parent.AddComponent<VerticalLayoutGroup>();
        layout.padding = new RectOffset(10, 10, 10, 10);
        layout.spacing = 5;
        layout.childControlHeight = false;
        layout.childControlWidth = true;
        layout.childForceExpandHeight = false;
        layout.childForceExpandWidth = true;

        // Titre
        CreateLabel(parent, "🎯 PLACEMENT MANUEL DES BOUÉES", 16, Color.white);
        
        // Section Bouée de Départ
        CreateLabel(parent, "📍 Bouée de Départ (B1)", 14, Color.green);
        startLatitudeInput = CreateInputField(parent, "Latitude", defaultStartPosition.x.ToString("F6"));
        startLongitudeInput = CreateInputField(parent, "Longitude", defaultStartPosition.y.ToString("F6"));
        placeStartButton = CreateButton(parent, "Placer B1", Color.green);
        
        CreateSeparator(parent);
        
        // Section Bouée d'Arrivée
        CreateLabel(parent, "🏁 Bouée d'Arrivée (B2)", 14, Color.red);
        finishLatitudeInput = CreateInputField(parent, "Latitude", defaultFinishPosition.x.ToString("F6"));
        finishLongitudeInput = CreateInputField(parent, "Longitude", defaultFinishPosition.y.ToString("F6"));
        placeFinishButton = CreateButton(parent, "Placer B2", Color.red);
        
        CreateSeparator(parent);
        
        // Boutons d'action
        placeBothButton = CreateButton(parent, "Placer les Deux", Color.yellow);
        clearButton = CreateButton(parent, "Effacer Tout", Color.gray);
        
        CreateSeparator(parent);
        
        // Options
        manualModeToggle = CreateToggle(parent, "Mode Manuel Actif");
        overrideToggle = CreateToggle(parent, "Override Automatique");
        
        // Status
        statusText = CreateLabel(parent, "Prêt", 12, Color.cyan);
    }

    /// <summary>
    /// Crée un label de texte
    /// </summary>
    private TextMeshProUGUI CreateLabel(GameObject parent, string text, int fontSize, Color color)
    {
        GameObject labelObject = new GameObject("Label");
        labelObject.transform.SetParent(parent.transform, false);
        
        TextMeshProUGUI label = labelObject.AddComponent<TextMeshProUGUI>();
        label.text = text;
        label.fontSize = fontSize;
        label.color = color;
        label.alignment = TextAlignmentOptions.Center;
        
        RectTransform rect = labelObject.GetComponent<RectTransform>();
        rect.sizeDelta = new Vector2(0, fontSize + 5);
        
        return label;
    }

    /// <summary>
    /// Crée un champ de saisie
    /// </summary>
    private TMP_InputField CreateInputField(GameObject parent, string placeholder, string defaultValue)
    {
        GameObject inputObject = new GameObject("InputField");
        inputObject.transform.SetParent(parent.transform, false);
        
        Image inputImage = inputObject.AddComponent<Image>();
        inputImage.color = new Color(1, 1, 1, 0.1f);
        
        TMP_InputField inputField = inputObject.AddComponent<TMP_InputField>();
        
        // Créer le texte placeholder
        GameObject placeholderObject = new GameObject("Placeholder");
        placeholderObject.transform.SetParent(inputObject.transform, false);
        TextMeshProUGUI placeholderText = placeholderObject.AddComponent<TextMeshProUGUI>();
        placeholderText.text = placeholder;
        placeholderText.fontSize = 12;
        placeholderText.color = new Color(1, 1, 1, 0.5f);
        
        // Créer le texte de saisie
        GameObject textObject = new GameObject("Text");
        textObject.transform.SetParent(inputObject.transform, false);
        TextMeshProUGUI inputText = textObject.AddComponent<TextMeshProUGUI>();
        inputText.fontSize = 12;
        inputText.color = Color.white;
        
        inputField.textComponent = inputText;
        inputField.placeholder = placeholderText;
        inputField.text = defaultValue;
        
        RectTransform rect = inputObject.GetComponent<RectTransform>();
        rect.sizeDelta = new Vector2(0, 25);
        
        return inputField;
    }

    /// <summary>
    /// Crée un bouton
    /// </summary>
    private Button CreateButton(GameObject parent, string text, Color color)
    {
        GameObject buttonObject = new GameObject("Button");
        buttonObject.transform.SetParent(parent.transform, false);
        
        Image buttonImage = buttonObject.AddComponent<Image>();
        buttonImage.color = color * 0.7f;
        
        Button button = buttonObject.AddComponent<Button>();
        
        GameObject textObject = new GameObject("Text");
        textObject.transform.SetParent(buttonObject.transform, false);
        TextMeshProUGUI buttonText = textObject.AddComponent<TextMeshProUGUI>();
        buttonText.text = text;
        buttonText.fontSize = 12;
        buttonText.color = Color.white;
        buttonText.alignment = TextAlignmentOptions.Center;
        
        RectTransform textRect = textObject.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
        
        RectTransform rect = buttonObject.GetComponent<RectTransform>();
        rect.sizeDelta = new Vector2(0, 30);
        
        return button;
    }

    /// <summary>
    /// Crée un toggle
    /// </summary>
    private Toggle CreateToggle(GameObject parent, string text)
    {
        GameObject toggleObject = new GameObject("Toggle");
        toggleObject.transform.SetParent(parent.transform, false);
        
        Toggle toggle = toggleObject.AddComponent<Toggle>();
        
        // Background
        GameObject backgroundObject = new GameObject("Background");
        backgroundObject.transform.SetParent(toggleObject.transform, false);
        Image backgroundImage = backgroundObject.AddComponent<Image>();
        backgroundImage.color = new Color(1, 1, 1, 0.1f);
        
        // Checkmark
        GameObject checkmarkObject = new GameObject("Checkmark");
        checkmarkObject.transform.SetParent(backgroundObject.transform, false);
        Image checkmarkImage = checkmarkObject.AddComponent<Image>();
        checkmarkImage.color = Color.green;
        
        // Label
        GameObject labelObject = new GameObject("Label");
        labelObject.transform.SetParent(toggleObject.transform, false);
        TextMeshProUGUI labelText = labelObject.AddComponent<TextMeshProUGUI>();
        labelText.text = text;
        labelText.fontSize = 12;
        labelText.color = Color.white;
        
        toggle.targetGraphic = backgroundImage;
        toggle.graphic = checkmarkImage;
        
        RectTransform rect = toggleObject.GetComponent<RectTransform>();
        rect.sizeDelta = new Vector2(0, 25);
        
        return toggle;
    }

    /// <summary>
    /// Crée un séparateur visuel
    /// </summary>
    private void CreateSeparator(GameObject parent)
    {
        GameObject separatorObject = new GameObject("Separator");
        separatorObject.transform.SetParent(parent.transform, false);
        
        Image separatorImage = separatorObject.AddComponent<Image>();
        separatorImage.color = new Color(1, 1, 1, 0.3f);
        
        RectTransform rect = separatorObject.GetComponent<RectTransform>();
        rect.sizeDelta = new Vector2(0, 2);
    }

    /// <summary>
    /// Configure les événements des boutons
    /// </summary>
    private void SetupButtonEvents()
    {
        if (placeStartButton != null)
            placeStartButton.onClick.AddListener(PlaceStartBuoy);
            
        if (placeFinishButton != null)
            placeFinishButton.onClick.AddListener(PlaceFinishBuoy);
            
        if (placeBothButton != null)
            placeBothButton.onClick.AddListener(PlaceBothBuoys);
            
        if (clearButton != null)
            clearButton.onClick.AddListener(ClearBuoys);
            
        if (manualModeToggle != null)
            manualModeToggle.onValueChanged.AddListener(OnManualModeToggle);
            
        if (overrideToggle != null)
            overrideToggle.onValueChanged.AddListener(OnOverrideToggle);
    }

    /// <summary>
    /// Définit les valeurs par défaut
    /// </summary>
    private void SetDefaultValues()
    {
        if (startLatitudeInput != null)
            startLatitudeInput.text = defaultStartPosition.x.ToString("F6");
            
        if (startLongitudeInput != null)
            startLongitudeInput.text = defaultStartPosition.y.ToString("F6");
            
        if (finishLatitudeInput != null)
            finishLatitudeInput.text = defaultFinishPosition.x.ToString("F6");
            
        if (finishLongitudeInput != null)
            finishLongitudeInput.text = defaultFinishPosition.y.ToString("F6");
    }

    /// <summary>
    /// Place la bouée de départ
    /// </summary>
    private void PlaceStartBuoy()
    {
        if (TryParseCoordinates(startLatitudeInput.text, startLongitudeInput.text, out Vector2d position))
        {
            manualPlacer.SetManualStartPosition(position.x, position.y);
            manualPlacer.PlaceStartBuoyManually();
            UpdateStatus("✅ Bouée de départ placée");
        }
        else
        {
            UpdateStatus("❌ Coordonnées de départ invalides");
        }
    }

    /// <summary>
    /// Place la bouée d'arrivée
    /// </summary>
    private void PlaceFinishBuoy()
    {
        if (TryParseCoordinates(finishLatitudeInput.text, finishLongitudeInput.text, out Vector2d position))
        {
            manualPlacer.SetManualFinishPosition(position.x, position.y);
            manualPlacer.PlaceFinishBuoyManually();
            UpdateStatus("✅ Bouée d'arrivée placée");
        }
        else
        {
            UpdateStatus("❌ Coordonnées d'arrivée invalides");
        }
    }

    /// <summary>
    /// Place les deux bouées
    /// </summary>
    private void PlaceBothBuoys()
    {
        PlaceStartBuoy();
        PlaceFinishBuoy();
        UpdateStatus("✅ Toutes les bouées placées");
    }

    /// <summary>
    /// Efface toutes les bouées
    /// </summary>
    private void ClearBuoys()
    {
        manualPlacer.ClearManualBuoys();
        UpdateStatus("🗑️ Bouées effacées");
    }

    /// <summary>
    /// Gère le toggle du mode manuel
    /// </summary>
    private void OnManualModeToggle(bool value)
    {
        // Utiliser la réflexion pour modifier le champ privé
        var field = typeof(ManualBuoyPlacer).GetField("enableManualPlacement", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (field != null)
        {
            field.SetValue(manualPlacer, value);
        }
        
        UpdateStatus($"Mode manuel: {(value ? "Activé" : "Désactivé")}");
    }

    /// <summary>
    /// Gère le toggle d'override
    /// </summary>
    private void OnOverrideToggle(bool value)
    {
        // Utiliser la réflexion pour modifier le champ privé
        var field = typeof(ManualBuoyPlacer).GetField("overrideAutomaticPlacement", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (field != null)
        {
            field.SetValue(manualPlacer, value);
        }
        
        UpdateStatus($"Override: {(value ? "Activé" : "Désactivé")}");
    }

    /// <summary>
    /// Essaie de parser les coordonnées GPS
    /// </summary>
    private bool TryParseCoordinates(string latText, string lonText, out Vector2d position)
    {
        position = Vector2d.zero;
        
        if (double.TryParse(latText, out double lat) && double.TryParse(lonText, out double lon))
        {
            // Vérifier que les coordonnées sont dans des plages valides
            if (lat >= -90 && lat <= 90 && lon >= -180 && lon <= 180)
            {
                position = new Vector2d(lat, lon);
                return true;
            }
        }
        
        return false;
    }

    /// <summary>
    /// Met à jour le texte de statut
    /// </summary>
    private void UpdateStatus(string message)
    {
        if (statusText != null)
        {
            statusText.text = message;
        }
        
        Debug.Log($"[BuoyPlacementUI] {message}");
    }

    /// <summary>
    /// Affiche l'interface utilisateur
    /// </summary>
    [ContextMenu("Show UI")]
    public void ShowUI()
    {
        if (uiPanel != null)
        {
            uiPanel.SetActive(true);
        }
    }

    /// <summary>
    /// Masque l'interface utilisateur
    /// </summary>
    [ContextMenu("Hide UI")]
    public void HideUI()
    {
        if (uiPanel != null)
        {
            uiPanel.SetActive(false);
        }
    }

    /// <summary>
    /// Bascule l'affichage de l'interface utilisateur
    /// </summary>
    [ContextMenu("Toggle UI")]
    public void ToggleUI()
    {
        if (uiPanel != null)
        {
            bool isActive = uiPanel.activeSelf;
            uiPanel.SetActive(!isActive);
            
            UpdateStatus(isActive ? "UI masquée" : "UI affichée");
        }
    }
}
