using UnityEngine;
using Mapbox.Utils;
using System.Reflection;

/// <summary>
/// Adaptateur qui fait le lien entre UnifiedBoatGPS et BoatWaypointMover
/// Transfère les données GPS reçues vers le système de waypoints
/// </summary>
public class GPSToWaypointAdapter : MonoBehaviour
{
    private UnifiedBoatGPS gpsComponent;
    private BoatWaypointMover waypointMover;
    private Vector2d lastGPSPosition;
    private float lastUpdateTime;
    private bool isInitialized = false;
    
    [Header("Debug")]
    [SerializeField] private bool showDebugLogs = false;

    /// <summary>
    /// Initialise l'adaptateur avec les composants GPS et waypoint
    /// </summary>
    public void Initialize(UnifiedBoatGPS gps, BoatWaypointMover waypoint)
    {
        gpsComponent = gps;
        waypointMover = waypoint;
        
        if (gpsComponent == null || waypointMover == null)
        {
            Debug.LogError($"[GPSToWaypointAdapter] {gameObject.name}: Composants manquants pour l'initialisation");
            return;
        }

        // S'abonner aux mises à jour GPS en interceptant les appels
        SetupGPSInterception();
        
        isInitialized = true;
        
        if (showDebugLogs)
            Debug.Log($"[GPSToWaypointAdapter] {gameObject.name}: Adaptateur initialisé");
    }

    /// <summary>
    /// Configure l'interception des données GPS
    /// </summary>
    private void SetupGPSInterception()
    {
        // Nous allons intercepter les appels à UpdateRealPosition
        // En surveillant les changements de position GPS
        InvokeRepeating(nameof(CheckForGPSUpdates), 0.1f, 0.1f);
    }

    /// <summary>
    /// Vérifie périodiquement les mises à jour GPS
    /// </summary>
    private void CheckForGPSUpdates()
    {
        if (!isInitialized || gpsComponent == null || waypointMover == null) return;

        // Obtenir la position GPS actuelle via réflexion
        Vector2d currentGPSPosition = GetCurrentGPSPosition();
        
        if (currentGPSPosition != default && currentGPSPosition != lastGPSPosition)
        {
            // Nouvelle position GPS détectée
            OnGPSPositionUpdated(currentGPSPosition);
            lastGPSPosition = currentGPSPosition;
            lastUpdateTime = Time.time;
        }
    }

    /// <summary>
    /// Obtient la position GPS actuelle du composant UnifiedBoatGPS
    /// </summary>
    private Vector2d GetCurrentGPSPosition()
    {
        if (gpsComponent == null) return default;

        try
        {
            // Utiliser la réflexion pour accéder au champ privé currentGPSPosition
            var field = typeof(UnifiedBoatGPS).GetField("currentGPSPosition", 
                BindingFlags.NonPublic | BindingFlags.Instance);
            
            if (field != null)
            {
                var value = field.GetValue(gpsComponent);
                if (value is Vector2d gpsPos)
                {
                    return gpsPos;
                }
            }
        }
        catch (System.Exception e)
        {
            if (showDebugLogs)
                Debug.LogWarning($"[GPSToWaypointAdapter] Erreur lors de l'accès à la position GPS: {e.Message}");
        }

        return default;
    }

    /// <summary>
    /// Appelé quand une nouvelle position GPS est détectée
    /// </summary>
    private void OnGPSPositionUpdated(Vector2d newGPSPosition)
    {
        if (waypointMover == null) return;

        // Transférer la position GPS vers le BoatWaypointMover
        waypointMover.AddGPSPosition(newGPSPosition, Time.time);
        
        if (showDebugLogs)
        {
            Debug.Log($"[GPSToWaypointAdapter] {gameObject.name}: Position GPS transférée - " +
                     $"Lat: {newGPSPosition.x:F6}, Lon: {newGPSPosition.y:F6}");
        }
    }

    /// <summary>
    /// Méthode publique pour forcer une mise à jour GPS
    /// Peut être appelée directement par le système GPS
    /// </summary>
    public void ForceGPSUpdate(Vector2d gpsPosition, float heading = 0f)
    {
        if (!isInitialized || waypointMover == null) return;

        waypointMover.AddGPSPosition(gpsPosition, Time.time);
        lastGPSPosition = gpsPosition;
        lastUpdateTime = Time.time;
        
        if (showDebugLogs)
        {
            Debug.Log($"[GPSToWaypointAdapter] {gameObject.name}: Mise à jour GPS forcée - " +
                     $"Lat: {gpsPosition.x:F6}, Lon: {gpsPosition.y:F6}");
        }
    }

    /// <summary>
    /// Obtient les statistiques de l'adaptateur
    /// </summary>
    public AdapterStats GetStats()
    {
        return new AdapterStats
        {
            isInitialized = this.isInitialized,
            lastGPSPosition = this.lastGPSPosition,
            lastUpdateTime = this.lastUpdateTime,
            timeSinceLastUpdate = Time.time - this.lastUpdateTime,
            gpsBufferCount = waypointMover?.GetGPSBufferCount() ?? 0,
            hasValidPath = waypointMover?.HasValidPath() ?? false,
            currentSpeed = waypointMover?.GetCurrentSpeedInKnots() ?? 0f
        };
    }

    /// <summary>
    /// Structure pour les statistiques de l'adaptateur
    /// </summary>
    public struct AdapterStats
    {
        public bool isInitialized;
        public Vector2d lastGPSPosition;
        public float lastUpdateTime;
        public float timeSinceLastUpdate;
        public int gpsBufferCount;
        public bool hasValidPath;
        public float currentSpeed;
    }

    private void OnDestroy()
    {
        // Nettoyer les références
        gpsComponent = null;
        waypointMover = null;
        isInitialized = false;
    }

    /// <summary>
    /// Affiche les informations de debug dans l'inspecteur
    /// </summary>
    private void OnDrawGizmosSelected()
    {
        if (!isInitialized || !showDebugLogs) return;

        // Afficher la dernière position GPS connue
        if (lastGPSPosition != default)
        {
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireSphere(transform.position + Vector3.up * 5f, 2f);
        }
    }

    [ContextMenu("Log Adapter Stats")]
    public void LogAdapterStats()
    {
        var stats = GetStats();
        Debug.Log($"[GPSToWaypointAdapter] {gameObject.name} - Statistiques:");
        Debug.Log($"- Initialisé: {stats.isInitialized}");
        Debug.Log($"- Dernière position GPS: {stats.lastGPSPosition}");
        Debug.Log($"- Temps depuis dernière MAJ: {stats.timeSinceLastUpdate:F1}s");
        Debug.Log($"- Points GPS en buffer: {stats.gpsBufferCount}");
        Debug.Log($"- Chemin valide: {stats.hasValidPath}");
        Debug.Log($"- Vitesse actuelle: {stats.currentSpeed:F1} nœuds");
    }

    [ContextMenu("Force GPS Update Test")]
    public void TestForceGPSUpdate()
    {
        // Test avec une position GPS fictive
        Vector2d testPosition = new Vector2d(43.123456, 5.654321);
        ForceGPSUpdate(testPosition);
        Debug.Log($"[GPSToWaypointAdapter] Test de mise à jour GPS forcée avec position: {testPosition}");
    }
}
