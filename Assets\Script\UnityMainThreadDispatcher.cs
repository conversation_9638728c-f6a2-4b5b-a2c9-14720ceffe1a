using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System;

/// <summary>
/// Permet d'exécuter du code sur le thread principal Unity depuis d'autres threads
/// </summary>
public class UnityMainThreadDispatcher : MonoBehaviour
{
    private static readonly Queue<Action> _executionQueue = new Queue<Action>();
    private static UnityMainThreadDispatcher _instance = null;

    public static UnityMainThreadDispatcher Instance()
    {
        if (_instance == null)
        {
            // Chercher une instance existante
            _instance = FindObjectOfType<UnityMainThreadDispatcher>();
            
            if (_instance == null)
            {
                // Créer une nouvelle instance
                GameObject go = new GameObject("UnityMainThreadDispatcher");
                _instance = go.AddComponent<UnityMainThreadDispatcher>();
                DontDestroyOnLoad(go);
            }
        }
        return _instance;
    }

    void Update()
    {
        lock (_executionQueue)
        {
            while (_executionQueue.Count > 0)
            {
                _executionQueue.Dequeue().Invoke();
            }
        }
    }

    /// <summary>
    /// Ajoute une action à exécuter sur le thread principal
    /// </summary>
    public void Enqueue(Action action)
    {
        lock (_executionQueue)
        {
            _executionQueue.Enqueue(action);
        }
    }
}
