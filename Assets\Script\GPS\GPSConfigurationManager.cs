using UnityEngine;

/// <summary>
/// Gestionnaire de configuration GPS pour adapter les paramètres selon la fréquence des données GPS
/// </summary>
public class GPSConfigurationManager : MonoBehaviour
{
    [Header("Configuration GPS")]
    [SerializeField] private GPSFrequency currentFrequency = GPSFrequency.Slow;
    [SerializeField] private bool autoConfigureOnStart = true;
    [SerializeField] private bool showDebugLogs = true;

    [Header("Paramètres GPS Lent (8-12s)")]
    [SerializeField] private float slowGPSDelay = 15f;
    [SerializeField] private float slowGPSInertia = 3f;
    [SerializeField] private float slowGPSAccuracy = 2f;

    [Header("Paramètres GPS Rapide (1-3s)")]
    [SerializeField] private float fastGPSDelay = 5f;
    [SerializeField] private float fastGPSInertia = 1f;
    [SerializeField] private float fastGPSAccuracy = 1.5f;

    public enum GPSFrequency
    {
        Fast,   // 1-3 secondes
        Slow    // 8-12 secondes
    }

    private void Start()
    {
        if (autoConfigureOnStart)
        {
            ConfigureAllBoats();
        }
    }

    /// <summary>
    /// Configure tous les bateaux selon la fréquence GPS actuelle
    /// </summary>
    [ContextMenu("Configure All Boats")]
    public void ConfigureAllBoats()
    {
        var allGPSBoats = FindObjectsOfType<UnifiedBoatGPS>();
        
        if (allGPSBoats.Length == 0)
        {
            LogMessage("❌ Aucun bateau avec UnifiedBoatGPS trouvé");
            return;
        }

        LogMessage($"🔧 === CONFIGURATION GPS {currentFrequency.ToString().ToUpper()} ===");
        LogMessage($"Configuration de {allGPSBoats.Length} bateaux...");

        int configuredCount = 0;

        foreach (var gpsBoat in allGPSBoats)
        {
            if (ConfigureBoat(gpsBoat))
            {
                configuredCount++;
            }
        }

        LogMessage($"✅ {configuredCount}/{allGPSBoats.Length} bateaux configurés avec succès");
        LogMessage("=== CONFIGURATION TERMINÉE ===");
    }

    /// <summary>
    /// Configure un bateau spécifique
    /// </summary>
    private bool ConfigureBoat(UnifiedBoatGPS gpsBoat)
    {
        try
        {
            switch (currentFrequency)
            {
                case GPSFrequency.Slow:
                    ConfigureForSlowGPS(gpsBoat);
                    break;
                case GPSFrequency.Fast:
                    ConfigureForFastGPS(gpsBoat);
                    break;
            }

            LogMessage($"✓ {gpsBoat.gameObject.name}: Configuré pour GPS {currentFrequency}");
            return true;
        }
        catch (System.Exception e)
        {
            LogMessage($"❌ {gpsBoat.gameObject.name}: Erreur de configuration - {e.Message}");
            return false;
        }
    }

    /// <summary>
    /// Configure un bateau pour des données GPS lentes
    /// </summary>
    private void ConfigureForSlowGPS(UnifiedBoatGPS gpsBoat)
    {
        // Utiliser la réflexion pour configurer les champs privés
        SetPrivateField(gpsBoat, "pathFollowDelay", slowGPSDelay);
        SetPrivateField(gpsBoat, "inertiaFactor", slowGPSInertia);
        SetPrivateField(gpsBoat, "gpsAccuracy", slowGPSAccuracy);

        // Appeler la méthode de configuration si elle existe
        var configMethod = typeof(UnifiedBoatGPS).GetMethod("ConfigureForSlowGPS");
        if (configMethod != null)
        {
            configMethod.Invoke(gpsBoat, null);
        }
    }

    /// <summary>
    /// Configure un bateau pour des données GPS rapides
    /// </summary>
    private void ConfigureForFastGPS(UnifiedBoatGPS gpsBoat)
    {
        // Utiliser la réflexion pour configurer les champs privés
        SetPrivateField(gpsBoat, "pathFollowDelay", fastGPSDelay);
        SetPrivateField(gpsBoat, "inertiaFactor", fastGPSInertia);
        SetPrivateField(gpsBoat, "gpsAccuracy", fastGPSAccuracy);

        // Appeler la méthode de configuration si elle existe
        var configMethod = typeof(UnifiedBoatGPS).GetMethod("ConfigureForFastGPS");
        if (configMethod != null)
        {
            configMethod.Invoke(gpsBoat, null);
        }
    }

    /// <summary>
    /// Utilise la réflexion pour définir un champ privé
    /// </summary>
    private void SetPrivateField(object obj, string fieldName, object value)
    {
        var field = obj.GetType().GetField(fieldName, 
            System.Reflection.BindingFlags.NonPublic | 
            System.Reflection.BindingFlags.Instance);
        
        if (field != null)
        {
            field.SetValue(obj, value);
        }
        else
        {
            LogMessage($"⚠️ Champ '{fieldName}' non trouvé dans {obj.GetType().Name}");
        }
    }

    /// <summary>
    /// Change la fréquence GPS et reconfigure tous les bateaux
    /// </summary>
    [ContextMenu("Switch to Slow GPS (8-12s)")]
    public void SwitchToSlowGPS()
    {
        currentFrequency = GPSFrequency.Slow;
        ConfigureAllBoats();
    }

    /// <summary>
    /// Change la fréquence GPS et reconfigure tous les bateaux
    /// </summary>
    [ContextMenu("Switch to Fast GPS (1-3s)")]
    public void SwitchToFastGPS()
    {
        currentFrequency = GPSFrequency.Fast;
        ConfigureAllBoats();
    }

    /// <summary>
    /// Affiche les paramètres actuels de tous les bateaux
    /// </summary>
    [ContextMenu("Show All Boats Configuration")]
    public void ShowAllBoatsConfiguration()
    {
        var allGPSBoats = FindObjectsOfType<UnifiedBoatGPS>();
        
        LogMessage("📊 === CONFIGURATION ACTUELLE DES BATEAUX ===");
        
        foreach (var gpsBoat in allGPSBoats)
        {
            gpsBoat.ShowCurrentSpeedInfo();
        }
        
        LogMessage("=== FIN DE LA CONFIGURATION ===");
    }

    /// <summary>
    /// Active les logs de vitesse pour tous les bateaux
    /// </summary>
    [ContextMenu("Enable Speed Logs for All Boats")]
    public void EnableSpeedLogsForAllBoats()
    {
        var allGPSBoats = FindObjectsOfType<UnifiedBoatGPS>();
        
        foreach (var gpsBoat in allGPSBoats)
        {
            SetPrivateField(gpsBoat, "showSpeedLogs", true);
        }
        
        LogMessage($"✅ Logs de vitesse activés pour {allGPSBoats.Length} bateaux");
    }

    /// <summary>
    /// Désactive les logs de vitesse pour tous les bateaux
    /// </summary>
    [ContextMenu("Disable Speed Logs for All Boats")]
    public void DisableSpeedLogsForAllBoats()
    {
        var allGPSBoats = FindObjectsOfType<UnifiedBoatGPS>();
        
        foreach (var gpsBoat in allGPSBoats)
        {
            SetPrivateField(gpsBoat, "showSpeedLogs", false);
        }
        
        LogMessage($"✅ Logs de vitesse désactivés pour {allGPSBoats.Length} bateaux");
    }

    /// <summary>
    /// Affiche un message de log si les logs de debug sont activés
    /// </summary>
    private void LogMessage(string message)
    {
        if (showDebugLogs)
        {
            Debug.Log($"[GPSConfigurationManager] {message}");
        }
    }

    /// <summary>
    /// Teste la configuration avec des paramètres personnalisés
    /// </summary>
    public void TestCustomConfiguration(float delay, float inertia, float accuracy)
    {
        var allGPSBoats = FindObjectsOfType<UnifiedBoatGPS>();
        
        LogMessage($"🧪 Test de configuration personnalisée: Délai={delay}s, Inertie={inertia}, Précision={accuracy}m");
        
        foreach (var gpsBoat in allGPSBoats)
        {
            SetPrivateField(gpsBoat, "pathFollowDelay", delay);
            SetPrivateField(gpsBoat, "inertiaFactor", inertia);
            SetPrivateField(gpsBoat, "gpsAccuracy", accuracy);
        }
        
        LogMessage($"✅ Configuration personnalisée appliquée à {allGPSBoats.Length} bateaux");
    }
}
