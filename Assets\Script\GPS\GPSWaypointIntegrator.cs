using UnityEngine;
using System.Collections.Generic;
using Mapbox.Utils;

/// <summary>
/// Intégrateur qui connecte automatiquement le BoatWaypointMover avec le système GPS existant
/// Gère la transition entre le mouvement GPS direct et le mouvement par waypoints
/// </summary>
public class GPSWaypointIntegrator : MonoBehaviour
{
    [Header("Configuration")]
    [SerializeField] private bool enableWaypointMovement = true;
    [SerializeField] private bool autoDetectBoats = true;
    [SerializeField] private Transform fleetContainer;
    
    [Header("Waypoint Movement Settings")]
    [SerializeField] private float maxSpeed = 15f;
    [SerializeField] private float rotationSpeed = 2f;
    [SerializeField] private float pathFollowDelay = 2f;
    [SerializeField] private float inertiaFactor = 3f;
    [SerializeField] private int minGPSPoints = 3;
    [SerializeField] private float gpsAccuracy = 2f;
    [SerializeField] private float lateralOffset = 2f;
    
    [Header("Debug")]
    [SerializeField] private bool showDebugInfo = false;
    [SerializeField] private bool showGPSPath = false;

    private Dictionary<GameObject, BoatWaypointMover> integratedBoats = new();
    private Dictionary<GameObject, UnifiedBoatGPS> gpsComponents = new();
    private bool isInitialized = false;

    private void Start()
    {
        if (autoDetectBoats)
        {
            InitializeIntegration();
        }
    }

    /// <summary>
    /// Initialise l'intégration pour tous les bateaux détectés
    /// </summary>
    public void InitializeIntegration()
    {
        if (isInitialized)
        {
            Debug.LogWarning("[GPSWaypointIntegrator] Intégration déjà initialisée");
            return;
        }

        Debug.Log("[GPSWaypointIntegrator] === INITIALISATION DE L'INTÉGRATION GPS-WAYPOINT ===");
        
        integratedBoats.Clear();
        gpsComponents.Clear();

        // Trouver tous les bateaux avec UnifiedBoatGPS
        UnifiedBoatGPS[] allGPSComponents = FindObjectsOfType<UnifiedBoatGPS>();
        
        foreach (var gpsComponent in allGPSComponents)
        {
            IntegrateBoat(gpsComponent.gameObject);
        }

        // Également dans le conteneur Fleet si défini
        if (fleetContainer != null)
        {
            foreach (Transform child in fleetContainer)
            {
                var gps = child.GetComponent<UnifiedBoatGPS>();
                if (gps != null && !integratedBoats.ContainsKey(child.gameObject))
                {
                    IntegrateBoat(child.gameObject);
                }
            }
        }

        isInitialized = true;
        Debug.Log($"[GPSWaypointIntegrator] Intégration terminée pour {integratedBoats.Count} bateaux");
    }

    /// <summary>
    /// Intègre un bateau spécifique avec le système de waypoints GPS
    /// </summary>
    private void IntegrateBoat(GameObject boat)
    {
        if (boat == null) return;

        var gpsComponent = boat.GetComponent<UnifiedBoatGPS>();
        if (gpsComponent == null)
        {
            Debug.LogWarning($"[GPSWaypointIntegrator] {boat.name} n'a pas de composant UnifiedBoatGPS");
            return;
        }

        // Vérifier si le bateau a déjà un BoatWaypointMover
        var waypointMover = boat.GetComponent<BoatWaypointMover>();
        if (waypointMover == null)
        {
            // Ajouter le composant BoatWaypointMover
            waypointMover = boat.AddComponent<BoatWaypointMover>();
            Debug.Log($"[GPSWaypointIntegrator] Composant BoatWaypointMover ajouté à {boat.name}");
        }

        // Configurer le BoatWaypointMover avec les paramètres
        ConfigureWaypointMover(waypointMover);

        // Configurer le Rigidbody pour le mouvement par waypoints
        ConfigureRigidbody(boat);

        // Enregistrer les composants
        integratedBoats[boat] = waypointMover;
        gpsComponents[boat] = gpsComponent;

        // Connecter les événements GPS
        ConnectGPSEvents(boat, gpsComponent, waypointMover);

        Debug.Log($"[GPSWaypointIntegrator] Bateau intégré: {boat.name}");
    }

    /// <summary>
    /// Configure les paramètres du BoatWaypointMover
    /// </summary>
    private void ConfigureWaypointMover(BoatWaypointMover waypointMover)
    {
        // Utiliser la réflexion pour configurer les champs privés
        var type = typeof(BoatWaypointMover);
        
        SetPrivateField(waypointMover, "maxSpeed", maxSpeed);
        SetPrivateField(waypointMover, "rotationSpeed", rotationSpeed);
        SetPrivateField(waypointMover, "pathFollowDelay", pathFollowDelay);
        SetPrivateField(waypointMover, "inertiaFactor", inertiaFactor);
        SetPrivateField(waypointMover, "minGPSPoints", minGPSPoints);
        SetPrivateField(waypointMover, "gpsAccuracy", gpsAccuracy);
        SetPrivateField(waypointMover, "lateralOffset", lateralOffset);
        SetPrivateField(waypointMover, "showDebugInfo", showDebugInfo);
        SetPrivateField(waypointMover, "showGPSPath", showGPSPath);
    }

    /// <summary>
    /// Configure le Rigidbody pour le mouvement par waypoints
    /// </summary>
    private void ConfigureRigidbody(GameObject boat)
    {
        var rigidbody = boat.GetComponent<Rigidbody>();
        if (rigidbody != null)
        {
            // Pour le mouvement par waypoints, on utilise la physique
            rigidbody.isKinematic = false;
            rigidbody.detectCollisions = true;
            rigidbody.useGravity = false;
            rigidbody.constraints = RigidbodyConstraints.FreezePositionY |
                                   RigidbodyConstraints.FreezeRotationX |
                                   RigidbodyConstraints.FreezeRotationZ;
            
            Debug.Log($"[GPSWaypointIntegrator] Rigidbody configuré pour mouvement waypoint: {boat.name}");
        }
    }

    /// <summary>
    /// Connecte les événements GPS avec le système de waypoints
    /// </summary>
    private void ConnectGPSEvents(GameObject boat, UnifiedBoatGPS gpsComponent, BoatWaypointMover waypointMover)
    {
        // Créer un adaptateur pour transférer les données GPS vers le waypoint mover
        var adapter = boat.GetComponent<GPSToWaypointAdapter>();
        if (adapter == null)
        {
            adapter = boat.AddComponent<GPSToWaypointAdapter>();
        }
        
        adapter.Initialize(gpsComponent, waypointMover);
    }

    /// <summary>
    /// Utilise la réflexion pour définir un champ privé
    /// </summary>
    private void SetPrivateField(object obj, string fieldName, object value)
    {
        var field = obj.GetType().GetField(fieldName, 
            System.Reflection.BindingFlags.NonPublic | 
            System.Reflection.BindingFlags.Instance);
        
        if (field != null)
        {
            field.SetValue(obj, value);
        }
        else
        {
            Debug.LogWarning($"[GPSWaypointIntegrator] Champ '{fieldName}' non trouvé dans {obj.GetType().Name}");
        }
    }

    /// <summary>
    /// Active ou désactive le mouvement par waypoints pour tous les bateaux
    /// </summary>
    public void SetWaypointMovementEnabled(bool enabled)
    {
        enableWaypointMovement = enabled;
        
        foreach (var kvp in integratedBoats)
        {
            var boat = kvp.Key;
            var waypointMover = kvp.Value;
            
            if (waypointMover != null)
            {
                waypointMover.enabled = enabled;
            }
            
            // Ajuster la configuration du Rigidbody selon le mode
            var rigidbody = boat.GetComponent<Rigidbody>();
            if (rigidbody != null)
            {
                if (enabled)
                {
                    // Mode waypoint : physique activée
                    rigidbody.isKinematic = false;
                    rigidbody.detectCollisions = true;
                }
                else
                {
                    // Mode GPS direct : kinématique
                    rigidbody.isKinematic = true;
                    rigidbody.detectCollisions = false;
                }
            }
        }
        
        Debug.Log($"[GPSWaypointIntegrator] Mouvement par waypoints {(enabled ? "activé" : "désactivé")} pour {integratedBoats.Count} bateaux");
    }

    /// <summary>
    /// Obtient les statistiques d'intégration
    /// </summary>
    public void LogIntegrationStats()
    {
        Debug.Log($"[GPSWaypointIntegrator] === STATISTIQUES D'INTÉGRATION ===");
        Debug.Log($"Bateaux intégrés: {integratedBoats.Count}");
        Debug.Log($"Mouvement par waypoints: {(enableWaypointMovement ? "Activé" : "Désactivé")}");
        
        foreach (var kvp in integratedBoats)
        {
            var boat = kvp.Key;
            var waypointMover = kvp.Value;
            
            if (waypointMover != null)
            {
                Debug.Log($"- {boat.name}: {waypointMover.GetGPSBufferCount()} points GPS, " +
                         $"Chemin valide: {waypointMover.HasValidPath()}, " +
                         $"Vitesse: {waypointMover.GetCurrentSpeedInKnots():F1} nœuds");
            }
        }
    }

    [ContextMenu("Initialize Integration")]
    public void ManualInitializeIntegration()
    {
        isInitialized = false;
        InitializeIntegration();
    }

    [ContextMenu("Toggle Waypoint Movement")]
    public void ToggleWaypointMovement()
    {
        SetWaypointMovementEnabled(!enableWaypointMovement);
    }

    [ContextMenu("Log Integration Stats")]
    public void ManualLogStats()
    {
        LogIntegrationStats();
    }
}
