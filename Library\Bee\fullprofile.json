{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 14060, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 14060, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 14060, "tid": 407257, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 14060, "tid": 407257, "ts": 1748419605154439, "dur": 473, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 14060, "tid": 407257, "ts": 1748419605157418, "dur": 1196, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 14060, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 14060, "tid": 1, "ts": 1748419585630731, "dur": 33925, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 14060, "tid": 1, "ts": 1748419585664663, "dur": 118596, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 14060, "tid": 1, "ts": 1748419585783281, "dur": 91416, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 14060, "tid": 407257, "ts": 1748419605158627, "dur": 36, "ph": "X", "name": "", "args": {}}, {"pid": 14060, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585628736, "dur": 26727, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585655467, "dur": 19490876, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585656232, "dur": 2279, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585658522, "dur": 723, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585659255, "dur": 6770, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585666037, "dur": 222, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585666266, "dur": 423, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585666698, "dur": 728, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585667432, "dur": 16618, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585684076, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585684087, "dur": 125, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585684220, "dur": 630, "ph": "X", "name": "ProcessMessages 2092", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585684857, "dur": 131, "ph": "X", "name": "ReadAsync 2092", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585684993, "dur": 10, "ph": "X", "name": "ProcessMessages 8206", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685005, "dur": 38, "ph": "X", "name": "ReadAsync 8206", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685046, "dur": 2, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685051, "dur": 47, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685101, "dur": 2, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685105, "dur": 49, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685157, "dur": 3, "ph": "X", "name": "ProcessMessages 1084", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685161, "dur": 37, "ph": "X", "name": "ReadAsync 1084", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685203, "dur": 2, "ph": "X", "name": "ProcessMessages 1018", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685207, "dur": 35, "ph": "X", "name": "ReadAsync 1018", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685245, "dur": 2, "ph": "X", "name": "ProcessMessages 840", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685249, "dur": 36, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685288, "dur": 2, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685293, "dur": 29, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685324, "dur": 2, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685328, "dur": 34, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685369, "dur": 3, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685374, "dur": 34, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685410, "dur": 2, "ph": "X", "name": "ProcessMessages 1152", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685414, "dur": 25, "ph": "X", "name": "ReadAsync 1152", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685442, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685445, "dur": 32, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685480, "dur": 2, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685484, "dur": 27, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685513, "dur": 2, "ph": "X", "name": "ProcessMessages 895", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685517, "dur": 34, "ph": "X", "name": "ReadAsync 895", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685556, "dur": 3, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685560, "dur": 29, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685592, "dur": 2, "ph": "X", "name": "ProcessMessages 1181", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685596, "dur": 30, "ph": "X", "name": "ReadAsync 1181", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685629, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685632, "dur": 28, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685663, "dur": 2, "ph": "X", "name": "ProcessMessages 103", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685667, "dur": 34, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685704, "dur": 2, "ph": "X", "name": "ProcessMessages 922", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685708, "dur": 35, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685746, "dur": 2, "ph": "X", "name": "ProcessMessages 812", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685750, "dur": 44, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685798, "dur": 2, "ph": "X", "name": "ProcessMessages 1080", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685802, "dur": 37, "ph": "X", "name": "ReadAsync 1080", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685842, "dur": 2, "ph": "X", "name": "ProcessMessages 1022", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685846, "dur": 36, "ph": "X", "name": "ReadAsync 1022", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685886, "dur": 2, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685890, "dur": 32, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685925, "dur": 2, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685929, "dur": 31, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685963, "dur": 2, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585685967, "dur": 34, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686004, "dur": 2, "ph": "X", "name": "ProcessMessages 878", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686008, "dur": 34, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686044, "dur": 2, "ph": "X", "name": "ProcessMessages 730", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686048, "dur": 42, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686093, "dur": 2, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686098, "dur": 39, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686140, "dur": 2, "ph": "X", "name": "ProcessMessages 970", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686145, "dur": 37, "ph": "X", "name": "ReadAsync 970", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686186, "dur": 2, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686190, "dur": 36, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686229, "dur": 2, "ph": "X", "name": "ProcessMessages 934", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686233, "dur": 29, "ph": "X", "name": "ReadAsync 934", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686265, "dur": 2, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686269, "dur": 36, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686308, "dur": 2, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686311, "dur": 32, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686347, "dur": 2, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686350, "dur": 27, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686381, "dur": 2, "ph": "X", "name": "ProcessMessages 168", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686384, "dur": 31, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686419, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686422, "dur": 32, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686457, "dur": 2, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686461, "dur": 33, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686497, "dur": 2, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686501, "dur": 32, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686536, "dur": 2, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686540, "dur": 28, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686571, "dur": 1, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686574, "dur": 29, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686606, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686609, "dur": 33, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686645, "dur": 2, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686649, "dur": 33, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686685, "dur": 2, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686689, "dur": 34, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686726, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686729, "dur": 39, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686772, "dur": 2, "ph": "X", "name": "ProcessMessages 884", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686775, "dur": 33, "ph": "X", "name": "ReadAsync 884", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686812, "dur": 2, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686816, "dur": 29, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686849, "dur": 2, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686852, "dur": 31, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686886, "dur": 2, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686890, "dur": 32, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686926, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686928, "dur": 29, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686961, "dur": 2, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686966, "dur": 30, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585686999, "dur": 2, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687003, "dur": 28, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687033, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687037, "dur": 47, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687087, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687090, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687118, "dur": 2, "ph": "X", "name": "ProcessMessages 779", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687121, "dur": 29, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687154, "dur": 2, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687157, "dur": 33, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687193, "dur": 2, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687198, "dur": 32, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687233, "dur": 2, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687237, "dur": 29, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687269, "dur": 2, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687273, "dur": 30, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687306, "dur": 2, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687310, "dur": 32, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687348, "dur": 3, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687353, "dur": 32, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687387, "dur": 2, "ph": "X", "name": "ProcessMessages 968", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687391, "dur": 34, "ph": "X", "name": "ReadAsync 968", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687430, "dur": 2, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687434, "dur": 23, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687459, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687462, "dur": 31, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687497, "dur": 2, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687502, "dur": 39, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687545, "dur": 2, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687549, "dur": 45, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687598, "dur": 2, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687602, "dur": 39, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687644, "dur": 2, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687648, "dur": 39, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687691, "dur": 3, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687695, "dur": 36, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687739, "dur": 3, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687745, "dur": 49, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687798, "dur": 2, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687803, "dur": 47, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687853, "dur": 3, "ph": "X", "name": "ProcessMessages 1182", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687859, "dur": 39, "ph": "X", "name": "ReadAsync 1182", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687902, "dur": 2, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687906, "dur": 33, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687942, "dur": 2, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687946, "dur": 41, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687992, "dur": 2, "ph": "X", "name": "ProcessMessages 905", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585687996, "dur": 39, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688039, "dur": 2, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688043, "dur": 35, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688082, "dur": 2, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688086, "dur": 38, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688128, "dur": 3, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688132, "dur": 40, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688176, "dur": 2, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688180, "dur": 38, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688222, "dur": 2, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688226, "dur": 40, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688270, "dur": 2, "ph": "X", "name": "ProcessMessages 937", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688274, "dur": 40, "ph": "X", "name": "ReadAsync 937", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688318, "dur": 3, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688322, "dur": 37, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688363, "dur": 2, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688367, "dur": 36, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688406, "dur": 2, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688410, "dur": 41, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688455, "dur": 2, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688461, "dur": 40, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688505, "dur": 2, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688509, "dur": 36, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688548, "dur": 2, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688552, "dur": 39, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688595, "dur": 2, "ph": "X", "name": "ProcessMessages 948", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688600, "dur": 42, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688645, "dur": 2, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688650, "dur": 31, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688684, "dur": 2, "ph": "X", "name": "ProcessMessages 73", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688688, "dur": 106, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688800, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688803, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688854, "dur": 3, "ph": "X", "name": "ProcessMessages 1083", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688859, "dur": 49, "ph": "X", "name": "ReadAsync 1083", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688912, "dur": 2, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688916, "dur": 43, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688962, "dur": 2, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585688967, "dur": 46, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689017, "dur": 3, "ph": "X", "name": "ProcessMessages 1087", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689022, "dur": 38, "ph": "X", "name": "ReadAsync 1087", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689064, "dur": 2, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689068, "dur": 34, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689106, "dur": 2, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689110, "dur": 33, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689147, "dur": 2, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689151, "dur": 37, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689192, "dur": 2, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689196, "dur": 38, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689238, "dur": 2, "ph": "X", "name": "ProcessMessages 830", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689242, "dur": 32, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689278, "dur": 2, "ph": "X", "name": "ProcessMessages 217", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689282, "dur": 41, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689326, "dur": 3, "ph": "X", "name": "ProcessMessages 958", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689331, "dur": 34, "ph": "X", "name": "ReadAsync 958", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689371, "dur": 3, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689377, "dur": 44, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689425, "dur": 3, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689430, "dur": 42, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689476, "dur": 2, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689481, "dur": 33, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689517, "dur": 2, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689521, "dur": 35, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689560, "dur": 2, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689564, "dur": 37, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689604, "dur": 2, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689609, "dur": 42, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689655, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689658, "dur": 43, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689704, "dur": 3, "ph": "X", "name": "ProcessMessages 1069", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689709, "dur": 42, "ph": "X", "name": "ReadAsync 1069", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689756, "dur": 2, "ph": "X", "name": "ProcessMessages 865", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689760, "dur": 32, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689796, "dur": 2, "ph": "X", "name": "ProcessMessages 190", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689800, "dur": 40, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689844, "dur": 2, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689848, "dur": 96, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689950, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585689954, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690013, "dur": 5, "ph": "X", "name": "ProcessMessages 1314", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690020, "dur": 34, "ph": "X", "name": "ReadAsync 1314", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690057, "dur": 2, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690061, "dur": 30, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690095, "dur": 3, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690099, "dur": 40, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690144, "dur": 2, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690148, "dur": 44, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690196, "dur": 3, "ph": "X", "name": "ProcessMessages 1152", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690201, "dur": 34, "ph": "X", "name": "ReadAsync 1152", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690239, "dur": 2, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690243, "dur": 41, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690287, "dur": 2, "ph": "X", "name": "ProcessMessages 928", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690292, "dur": 36, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690332, "dur": 2, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690336, "dur": 35, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690377, "dur": 3, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690382, "dur": 53, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690440, "dur": 3, "ph": "X", "name": "ProcessMessages 1083", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690444, "dur": 43, "ph": "X", "name": "ReadAsync 1083", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690492, "dur": 3, "ph": "X", "name": "ProcessMessages 986", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690497, "dur": 32, "ph": "X", "name": "ReadAsync 986", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690533, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690536, "dur": 33, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690573, "dur": 2, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690576, "dur": 42, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690623, "dur": 2, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690627, "dur": 39, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690672, "dur": 3, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690676, "dur": 32, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690713, "dur": 2, "ph": "X", "name": "ProcessMessages 182", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690716, "dur": 37, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690757, "dur": 2, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690761, "dur": 43, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690808, "dur": 2, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690812, "dur": 38, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690854, "dur": 2, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690858, "dur": 34, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690896, "dur": 2, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690900, "dur": 41, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690945, "dur": 2, "ph": "X", "name": "ProcessMessages 958", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690949, "dur": 41, "ph": "X", "name": "ReadAsync 958", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690994, "dur": 2, "ph": "X", "name": "ProcessMessages 910", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585690999, "dur": 37, "ph": "X", "name": "ReadAsync 910", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691039, "dur": 2, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691043, "dur": 38, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691086, "dur": 2, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691090, "dur": 36, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691130, "dur": 2, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691134, "dur": 34, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691172, "dur": 2, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691175, "dur": 35, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691214, "dur": 2, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691218, "dur": 40, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691262, "dur": 2, "ph": "X", "name": "ProcessMessages 813", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691266, "dur": 35, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691306, "dur": 2, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691309, "dur": 34, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691347, "dur": 2, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691351, "dur": 39, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691394, "dur": 2, "ph": "X", "name": "ProcessMessages 928", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691399, "dur": 36, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691439, "dur": 2, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691443, "dur": 32, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691479, "dur": 2, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691483, "dur": 39, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691526, "dur": 2, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691530, "dur": 37, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691571, "dur": 2, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691575, "dur": 36, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691615, "dur": 2, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691619, "dur": 34, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691657, "dur": 2, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691660, "dur": 38, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691703, "dur": 2, "ph": "X", "name": "ProcessMessages 962", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691707, "dur": 39, "ph": "X", "name": "ReadAsync 962", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691750, "dur": 2, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691754, "dur": 34, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691792, "dur": 2, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691795, "dur": 40, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691839, "dur": 2, "ph": "X", "name": "ProcessMessages 1069", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691844, "dur": 38, "ph": "X", "name": "ReadAsync 1069", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691886, "dur": 2, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691890, "dur": 34, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691929, "dur": 2, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691933, "dur": 38, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691975, "dur": 2, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585691979, "dur": 33, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692016, "dur": 2, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692020, "dur": 40, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692064, "dur": 2, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692068, "dur": 32, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692104, "dur": 2, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692108, "dur": 40, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692152, "dur": 2, "ph": "X", "name": "ProcessMessages 1124", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692157, "dur": 42, "ph": "X", "name": "ReadAsync 1124", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692203, "dur": 2, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692208, "dur": 43, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692256, "dur": 2, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692260, "dur": 34, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692298, "dur": 2, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692302, "dur": 38, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692343, "dur": 3, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692348, "dur": 34, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692385, "dur": 2, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692389, "dur": 37, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692429, "dur": 2, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692433, "dur": 36, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692473, "dur": 2, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692477, "dur": 39, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692520, "dur": 2, "ph": "X", "name": "ProcessMessages 932", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692524, "dur": 33, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692561, "dur": 2, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692564, "dur": 33, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692602, "dur": 2, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692605, "dur": 35, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692644, "dur": 2, "ph": "X", "name": "ProcessMessages 826", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692648, "dur": 42, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692697, "dur": 3, "ph": "X", "name": "ProcessMessages 1074", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692702, "dur": 43, "ph": "X", "name": "ReadAsync 1074", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692750, "dur": 2, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692754, "dur": 43, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692803, "dur": 3, "ph": "X", "name": "ProcessMessages 906", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692808, "dur": 43, "ph": "X", "name": "ReadAsync 906", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692854, "dur": 3, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692859, "dur": 39, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692902, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692906, "dur": 37, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692949, "dur": 3, "ph": "X", "name": "ProcessMessages 788", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585692954, "dur": 45, "ph": "X", "name": "ReadAsync 788", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693003, "dur": 2, "ph": "X", "name": "ProcessMessages 1007", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693008, "dur": 37, "ph": "X", "name": "ReadAsync 1007", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693048, "dur": 2, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693052, "dur": 34, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693091, "dur": 2, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693094, "dur": 38, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693137, "dur": 3, "ph": "X", "name": "ProcessMessages 1160", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693141, "dur": 41, "ph": "X", "name": "ReadAsync 1160", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693187, "dur": 3, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693192, "dur": 37, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693234, "dur": 2, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693239, "dur": 41, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693285, "dur": 161, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693466, "dur": 68, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693538, "dur": 7, "ph": "X", "name": "ProcessMessages 4146", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693547, "dur": 42, "ph": "X", "name": "ReadAsync 4146", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693593, "dur": 2, "ph": "X", "name": "ProcessMessages 1068", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693598, "dur": 40, "ph": "X", "name": "ReadAsync 1068", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693642, "dur": 3, "ph": "X", "name": "ProcessMessages 988", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693646, "dur": 35, "ph": "X", "name": "ReadAsync 988", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693685, "dur": 2, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693689, "dur": 38, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693731, "dur": 2, "ph": "X", "name": "ProcessMessages 1285", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693735, "dur": 40, "ph": "X", "name": "ReadAsync 1285", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693780, "dur": 2, "ph": "X", "name": "ProcessMessages 1105", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693785, "dur": 36, "ph": "X", "name": "ReadAsync 1105", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693825, "dur": 2, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693828, "dur": 40, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693873, "dur": 3, "ph": "X", "name": "ProcessMessages 919", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693878, "dur": 39, "ph": "X", "name": "ReadAsync 919", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693920, "dur": 2, "ph": "X", "name": "ProcessMessages 948", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693925, "dur": 36, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693965, "dur": 2, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585693968, "dur": 36, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694009, "dur": 2, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694012, "dur": 38, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694055, "dur": 2, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694059, "dur": 33, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694096, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694100, "dur": 41, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694147, "dur": 3, "ph": "X", "name": "ProcessMessages 1017", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694152, "dur": 134, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694291, "dur": 3, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694297, "dur": 41, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694340, "dur": 3, "ph": "X", "name": "ProcessMessages 2242", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694345, "dur": 31, "ph": "X", "name": "ReadAsync 2242", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694381, "dur": 2, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694385, "dur": 44, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694433, "dur": 3, "ph": "X", "name": "ProcessMessages 1351", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694438, "dur": 40, "ph": "X", "name": "ReadAsync 1351", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694483, "dur": 3, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694487, "dur": 36, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694528, "dur": 2, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694531, "dur": 36, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694572, "dur": 2, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694576, "dur": 38, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694618, "dur": 3, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694623, "dur": 24, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694649, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694652, "dur": 33, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694689, "dur": 2, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694693, "dur": 39, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694737, "dur": 3, "ph": "X", "name": "ProcessMessages 924", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694742, "dur": 37, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694783, "dur": 2, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694787, "dur": 39, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694830, "dur": 2, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694834, "dur": 40, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694878, "dur": 2, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694883, "dur": 39, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694925, "dur": 2, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694930, "dur": 40, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694974, "dur": 2, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585694978, "dur": 41, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695023, "dur": 2, "ph": "X", "name": "ProcessMessages 956", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695027, "dur": 34, "ph": "X", "name": "ReadAsync 956", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695065, "dur": 2, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695068, "dur": 37, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695109, "dur": 3, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695113, "dur": 34, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695152, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695155, "dur": 33, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695192, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695196, "dur": 33, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695232, "dur": 2, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695236, "dur": 40, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695282, "dur": 3, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695287, "dur": 25, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695314, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695317, "dur": 72, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695394, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695397, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695443, "dur": 3, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695449, "dur": 33, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695485, "dur": 2, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695490, "dur": 32, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695526, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695529, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695580, "dur": 3, "ph": "X", "name": "ProcessMessages 892", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695586, "dur": 42, "ph": "X", "name": "ReadAsync 892", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695632, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695636, "dur": 38, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695678, "dur": 2, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695682, "dur": 35, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695721, "dur": 2, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695725, "dur": 41, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695771, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695774, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695821, "dur": 3, "ph": "X", "name": "ProcessMessages 851", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695825, "dur": 35, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695865, "dur": 2, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695869, "dur": 39, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695912, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695915, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695960, "dur": 3, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585695964, "dur": 34, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696003, "dur": 2, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696007, "dur": 33, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696044, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696047, "dur": 42, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696097, "dur": 3, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696102, "dur": 36, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696142, "dur": 2, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696146, "dur": 34, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696184, "dur": 2, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696188, "dur": 40, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696232, "dur": 2, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696236, "dur": 30, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696272, "dur": 3, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696278, "dur": 50, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696333, "dur": 1, "ph": "X", "name": "ProcessMessages 102", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696336, "dur": 43, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696385, "dur": 3, "ph": "X", "name": "ProcessMessages 967", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696390, "dur": 31, "ph": "X", "name": "ReadAsync 967", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696424, "dur": 1, "ph": "X", "name": "ProcessMessages 98", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696427, "dur": 36, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696466, "dur": 2, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696470, "dur": 34, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696509, "dur": 2, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696512, "dur": 47, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696563, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696566, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696613, "dur": 2, "ph": "X", "name": "ProcessMessages 1048", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696617, "dur": 33, "ph": "X", "name": "ReadAsync 1048", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696654, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696658, "dur": 39, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696702, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696706, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696749, "dur": 2, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696754, "dur": 37, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696795, "dur": 2, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696799, "dur": 34, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696838, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696842, "dur": 130, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696978, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585696981, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697035, "dur": 3, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697040, "dur": 42, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697086, "dur": 2, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697090, "dur": 35, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697130, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697133, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697178, "dur": 3, "ph": "X", "name": "ProcessMessages 842", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697183, "dur": 32, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697219, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697223, "dur": 59, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697285, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697289, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697339, "dur": 2, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697343, "dur": 38, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697386, "dur": 2, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697390, "dur": 73, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697470, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697474, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697521, "dur": 3, "ph": "X", "name": "ProcessMessages 992", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697527, "dur": 40, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697571, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697575, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697623, "dur": 3, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697627, "dur": 40, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697671, "dur": 2, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697676, "dur": 31, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697711, "dur": 2, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697715, "dur": 52, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697772, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697775, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697822, "dur": 3, "ph": "X", "name": "ProcessMessages 930", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697828, "dur": 33, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697863, "dur": 2, "ph": "X", "name": "ProcessMessages 813", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697867, "dur": 23, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697894, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697898, "dur": 33, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697935, "dur": 2, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697939, "dur": 33, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697976, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585697980, "dur": 65, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698050, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698053, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698098, "dur": 3, "ph": "X", "name": "ProcessMessages 922", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698103, "dur": 41, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698149, "dur": 2, "ph": "X", "name": "ProcessMessages 137", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698152, "dur": 37, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698193, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698196, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698239, "dur": 2, "ph": "X", "name": "ProcessMessages 863", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698243, "dur": 35, "ph": "X", "name": "ReadAsync 863", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698283, "dur": 2, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698287, "dur": 41, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698334, "dur": 2, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698338, "dur": 34, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698376, "dur": 2, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698380, "dur": 34, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698418, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698422, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698473, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698476, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698521, "dur": 3, "ph": "X", "name": "ProcessMessages 893", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698526, "dur": 33, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698563, "dur": 2, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698567, "dur": 32, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698604, "dur": 1, "ph": "X", "name": "ProcessMessages 102", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698607, "dur": 37, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698649, "dur": 4, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698655, "dur": 34, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698694, "dur": 3, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698699, "dur": 41, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698745, "dur": 1, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698748, "dur": 41, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698793, "dur": 3, "ph": "X", "name": "ProcessMessages 948", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698798, "dur": 29, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698833, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698837, "dur": 48, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698891, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698894, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698937, "dur": 3, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698942, "dur": 31, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698976, "dur": 2, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585698980, "dur": 64, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699048, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699050, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699092, "dur": 4, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699098, "dur": 35, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699137, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699141, "dur": 44, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699189, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699192, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699235, "dur": 2, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699240, "dur": 34, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699277, "dur": 2, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699281, "dur": 31, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699317, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699320, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699369, "dur": 3, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699374, "dur": 28, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699405, "dur": 2, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699409, "dur": 34, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699447, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699451, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699489, "dur": 2, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699494, "dur": 38, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699535, "dur": 2, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699539, "dur": 30, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699573, "dur": 2, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699577, "dur": 31, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699612, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699615, "dur": 36, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699654, "dur": 2, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699659, "dur": 35, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699697, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699700, "dur": 48, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699754, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699757, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699793, "dur": 2, "ph": "X", "name": "ProcessMessages 1220", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699798, "dur": 27, "ph": "X", "name": "ReadAsync 1220", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699828, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699831, "dur": 31, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699866, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699868, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699909, "dur": 2, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699913, "dur": 38, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699954, "dur": 2, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699958, "dur": 28, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699988, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585699991, "dur": 42, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700038, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700042, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700085, "dur": 2, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700090, "dur": 32, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700125, "dur": 2, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700129, "dur": 39, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700172, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700175, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700217, "dur": 2, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700221, "dur": 35, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700259, "dur": 2, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700263, "dur": 27, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700293, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700297, "dur": 80, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700383, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700388, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700453, "dur": 5, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700461, "dur": 59, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700527, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700533, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700587, "dur": 3, "ph": "X", "name": "ProcessMessages 98", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700593, "dur": 60, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700660, "dur": 3, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700666, "dur": 28, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700697, "dur": 3, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700703, "dur": 23, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700730, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700734, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700788, "dur": 3, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700794, "dur": 39, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700837, "dur": 2, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700841, "dur": 53, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700899, "dur": 1, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700902, "dur": 51, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700961, "dur": 4, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585700967, "dur": 45, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701016, "dur": 2, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701021, "dur": 38, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701063, "dur": 2, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701067, "dur": 34, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701105, "dur": 1, "ph": "X", "name": "ProcessMessages 219", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701108, "dur": 42, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701154, "dur": 3, "ph": "X", "name": "ProcessMessages 940", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701159, "dur": 30, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701193, "dur": 2, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701196, "dur": 61, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701264, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701269, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701329, "dur": 4, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701335, "dur": 38, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701380, "dur": 3, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701385, "dur": 24, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701414, "dur": 36, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701453, "dur": 2, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701457, "dur": 34, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701494, "dur": 2, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701498, "dur": 39, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701540, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701543, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701584, "dur": 2, "ph": "X", "name": "ProcessMessages 955", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701588, "dur": 30, "ph": "X", "name": "ReadAsync 955", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701621, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701625, "dur": 32, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701660, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701663, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701708, "dur": 2, "ph": "X", "name": "ProcessMessages 884", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701712, "dur": 31, "ph": "X", "name": "ReadAsync 884", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701746, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701750, "dur": 49, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701803, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701807, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701859, "dur": 4, "ph": "X", "name": "ProcessMessages 1029", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701865, "dur": 37, "ph": "X", "name": "ReadAsync 1029", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701907, "dur": 2, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701911, "dur": 58, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701973, "dur": 2, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585701977, "dur": 34, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702014, "dur": 2, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702018, "dur": 29, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702052, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702055, "dur": 60, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702122, "dur": 3, "ph": "X", "name": "ProcessMessages 913", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702127, "dur": 25, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702154, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702159, "dur": 32, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702195, "dur": 2, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702200, "dur": 36, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702240, "dur": 2, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702245, "dur": 41, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702290, "dur": 2, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702294, "dur": 49, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702348, "dur": 4, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702355, "dur": 45, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702407, "dur": 3, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702412, "dur": 24, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702439, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702442, "dur": 47, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702494, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702497, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702556, "dur": 4, "ph": "X", "name": "ProcessMessages 1186", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702563, "dur": 43, "ph": "X", "name": "ReadAsync 1186", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702610, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702613, "dur": 37, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702655, "dur": 3, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702660, "dur": 33, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702697, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702700, "dur": 30, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702737, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702741, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702777, "dur": 2, "ph": "X", "name": "ProcessMessages 1146", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702782, "dur": 60, "ph": "X", "name": "ReadAsync 1146", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702847, "dur": 2, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702852, "dur": 41, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702897, "dur": 3, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702913, "dur": 39, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702957, "dur": 3, "ph": "X", "name": "ProcessMessages 1059", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585702962, "dur": 65, "ph": "X", "name": "ReadAsync 1059", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585703031, "dur": 3, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585703036, "dur": 39, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585703082, "dur": 3, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585703088, "dur": 63, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585703157, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585703161, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585703209, "dur": 4, "ph": "X", "name": "ProcessMessages 993", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585703217, "dur": 51, "ph": "X", "name": "ReadAsync 993", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585703273, "dur": 3, "ph": "X", "name": "ProcessMessages 903", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585703278, "dur": 39, "ph": "X", "name": "ReadAsync 903", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585703321, "dur": 3, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585703325, "dur": 35, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585703365, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585703369, "dur": 53, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585703428, "dur": 1, "ph": "X", "name": "ProcessMessages 101", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585703431, "dur": 39, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585703475, "dur": 2, "ph": "X", "name": "ProcessMessages 135", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585703479, "dur": 48, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585703533, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585703537, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585703599, "dur": 434, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585704040, "dur": 296, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585704344, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585704350, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585704438, "dur": 4, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585704446, "dur": 54, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585704507, "dur": 5, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585704515, "dur": 73, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585704596, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585704604, "dur": 57, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585704669, "dur": 7, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585704680, "dur": 53, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585704741, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585704748, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585704820, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585704826, "dur": 54, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585704886, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585704894, "dur": 80, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585704981, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585704989, "dur": 63, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705060, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705067, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705126, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705133, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705191, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705197, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705258, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705266, "dur": 68, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705340, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705346, "dur": 60, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705416, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705424, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705493, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705500, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705559, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705567, "dur": 48, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705621, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705628, "dur": 134, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705771, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705778, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705847, "dur": 5, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705856, "dur": 61, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705926, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705936, "dur": 55, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585705999, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706008, "dur": 73, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706089, "dur": 6, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706099, "dur": 60, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706166, "dur": 7, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706177, "dur": 54, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706238, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706246, "dur": 58, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706310, "dur": 6, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706320, "dur": 54, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706381, "dur": 6, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706391, "dur": 58, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706456, "dur": 6, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706466, "dur": 44, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706518, "dur": 6, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706528, "dur": 50, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706584, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706592, "dur": 49, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706648, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706657, "dur": 64, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706730, "dur": 8, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706742, "dur": 55, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706803, "dur": 6, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706813, "dur": 53, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706874, "dur": 6, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706883, "dur": 64, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706954, "dur": 7, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585706965, "dur": 89, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707061, "dur": 7, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707072, "dur": 57, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707136, "dur": 7, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707146, "dur": 61, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707214, "dur": 7, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707225, "dur": 65, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707300, "dur": 8, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707312, "dur": 62, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707380, "dur": 7, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707391, "dur": 62, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707460, "dur": 6, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707469, "dur": 66, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707543, "dur": 6, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707553, "dur": 58, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707618, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707628, "dur": 57, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707691, "dur": 6, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707700, "dur": 52, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707759, "dur": 6, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707769, "dur": 52, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707828, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707837, "dur": 53, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707898, "dur": 6, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707907, "dur": 58, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707973, "dur": 6, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585707983, "dur": 56, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585708046, "dur": 7, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585708057, "dur": 61, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585708126, "dur": 5, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585708135, "dur": 52, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585708194, "dur": 5, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585708203, "dur": 54, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585708265, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585708273, "dur": 72, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585708353, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585708358, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585708420, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585708427, "dur": 16265, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585724709, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585724719, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585724806, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585724815, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585724875, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585724883, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585724942, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585724949, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585725003, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585725014, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585725069, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585725075, "dur": 5542, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585730627, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585730633, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585730699, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585730706, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585730756, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585730764, "dur": 199, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585730972, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585730978, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585731039, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585731045, "dur": 1615, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585732668, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585732674, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585732743, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585732750, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585732810, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585732820, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585732944, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585732950, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733003, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733010, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733069, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733075, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733151, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733156, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733208, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733214, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733266, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733272, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733326, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733331, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733376, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733382, "dur": 159, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733548, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733553, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733608, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733613, "dur": 158, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733779, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733786, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733830, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733836, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733916, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733923, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733961, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585733968, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734006, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734010, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734118, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734123, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734179, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734185, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734230, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734234, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734280, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734287, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734378, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734383, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734425, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734430, "dur": 44, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734480, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734487, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734536, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734541, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734587, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734591, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734631, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734636, "dur": 82, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734724, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734729, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734774, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734779, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734862, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734867, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734915, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585734920, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735017, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735022, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735081, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735088, "dur": 147, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735243, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735249, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735309, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735316, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735372, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735377, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735485, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735491, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735550, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735557, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735613, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735619, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735686, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735693, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735755, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735763, "dur": 47, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735816, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735823, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735890, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735897, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735954, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585735961, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736033, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736039, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736098, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736107, "dur": 54, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736167, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736174, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736222, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736226, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736335, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736340, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736392, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736399, "dur": 46, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736452, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736458, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736526, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736534, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736576, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736582, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736619, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736625, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736742, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736748, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736804, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736810, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736863, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585736868, "dur": 185, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585737060, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585737066, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585737132, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585737139, "dur": 232, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585737379, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585737385, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585737445, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585737452, "dur": 791, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585738249, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585738255, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585738323, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585738329, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585738401, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585738408, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585738464, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585738471, "dur": 327, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585738802, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585738805, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585738860, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585738867, "dur": 60, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585738935, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585738942, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739002, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739009, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739063, "dur": 3, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739069, "dur": 63, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739140, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739148, "dur": 61, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739216, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739221, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739281, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739286, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739339, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739344, "dur": 230, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739582, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739587, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739639, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739645, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739700, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739706, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739763, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739769, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739828, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739834, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739896, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739902, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739988, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585739993, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585740051, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585740058, "dur": 276, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585740342, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585740347, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585740399, "dur": 928, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585741336, "dur": 67, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585741409, "dur": 7, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585741419, "dur": 637, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585742065, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585742071, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585742132, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585742138, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585742169, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585742173, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585742243, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585742248, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585742301, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585742308, "dur": 121, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585742436, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585742441, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585742489, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585742495, "dur": 497, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743000, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743004, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743072, "dur": 5, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743081, "dur": 55, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743143, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743149, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743214, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743220, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743274, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743281, "dur": 42, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743330, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743336, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743384, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743390, "dur": 121, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743519, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743525, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743578, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743584, "dur": 225, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743816, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743822, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743876, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743883, "dur": 43, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743932, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743938, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743989, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585743995, "dur": 109, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744110, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744115, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744167, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744175, "dur": 45, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744225, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744232, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744283, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744289, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744358, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744363, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744423, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744429, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744482, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744488, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744539, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744546, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744597, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744604, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744682, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744689, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744744, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744751, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744788, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744792, "dur": 143, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744941, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744947, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585744998, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585745005, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585745062, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585745068, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585745127, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585745132, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585745194, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585745201, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585745254, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585745258, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585745326, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585745330, "dur": 219, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585745556, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585745561, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585745605, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585745609, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585745655, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585745661, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585745763, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585745767, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585745811, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585745815, "dur": 509, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585746332, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585746338, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585746401, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585746408, "dur": 121, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585746535, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585746539, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585746585, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585746589, "dur": 2456, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585749058, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585749066, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585749149, "dur": 7, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585749160, "dur": 91029, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585840206, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585840214, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585840256, "dur": 2533, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585842800, "dur": 4663, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585847479, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585847504, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585847601, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585847610, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585847672, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585847679, "dur": 192, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585847879, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585847886, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585847947, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585847954, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585848009, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585848015, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585848048, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585848052, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585848119, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585848125, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585848181, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585848187, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585848304, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585848310, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585848363, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585848369, "dur": 1361, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585849739, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585849745, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585849789, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585849796, "dur": 247, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585850051, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585850056, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585850101, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585850107, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585850141, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585850146, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585850192, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585850198, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585850237, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585850242, "dur": 124, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585850374, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585850381, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585850411, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585850415, "dur": 157, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585850579, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585850584, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585850615, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585850620, "dur": 216, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585850860, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585850866, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585850900, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585850905, "dur": 710, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585851623, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585851629, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585851681, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585851687, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585851735, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585851741, "dur": 156, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585851904, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585851909, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585851989, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585851995, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585852040, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585852046, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585852144, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585852149, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585852189, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585852195, "dur": 159, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585852361, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585852366, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585852410, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585852416, "dur": 710, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585853134, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585853141, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585853179, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585853185, "dur": 100, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585853292, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585853299, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585853330, "dur": 5, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585853339, "dur": 635, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585853982, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585853989, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585854028, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585854034, "dur": 102, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585854144, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585854150, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585854178, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585854182, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585854208, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585854211, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585854331, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585854336, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585854381, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585854386, "dur": 609, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585855002, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585855008, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585855050, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585855054, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585855159, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585855164, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585855226, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585855233, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585855270, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585855275, "dur": 110, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585855394, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585855400, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585855454, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585855460, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585855507, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585855512, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585855555, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585855559, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585855613, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585855618, "dur": 608, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856232, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856237, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856281, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856287, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856335, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856340, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856386, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856391, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856432, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856438, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856476, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856481, "dur": 34, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856519, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856524, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856565, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856571, "dur": 37, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856612, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856617, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856657, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856663, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856701, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856707, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856746, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856752, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856799, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856805, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856849, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856855, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856895, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856902, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856941, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856947, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856984, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585856990, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857036, "dur": 3, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857043, "dur": 49, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857099, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857105, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857158, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857165, "dur": 43, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857212, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857218, "dur": 40, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857262, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857268, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857310, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857316, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857358, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857364, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857406, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857411, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857466, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857473, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857528, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857535, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857593, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857601, "dur": 52, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857659, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857668, "dur": 58, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857731, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857736, "dur": 186, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857930, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585857936, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585858001, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585858007, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585858056, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585858060, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585858104, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419585858107, "dur": 12341723, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419598199844, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419598199851, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419598199887, "dur": 7052, "ph": "X", "name": "ProcessMessages 2651", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419598206946, "dur": 1840050, "ph": "X", "name": "ReadAsync 2651", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600047014, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600047024, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600047100, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600047112, "dur": 242028, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600289154, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600289161, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600289223, "dur": 33, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600289259, "dur": 17247, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600306532, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600306540, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600306576, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600306582, "dur": 1983, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600308576, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600308583, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600308637, "dur": 38, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600308677, "dur": 129512, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600438203, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600438211, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600438300, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600438307, "dur": 1141, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600439456, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600439460, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600439507, "dur": 36, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419600439547, "dur": 3495756, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419603935319, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419603935328, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419603935410, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419603935417, "dur": 2339, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419603937767, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419603937774, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419603937838, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419603937847, "dur": 349503, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419604287361, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419604287368, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419604287429, "dur": 33, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419604287465, "dur": 12447, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419604299923, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419604299928, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419604299991, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419604299997, "dur": 833246, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419605133259, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419605133266, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419605133325, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419605133332, "dur": 1284, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419605134626, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419605134631, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419605134695, "dur": 404, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 14060, "tid": 12884901888, "ts": 1748419605135106, "dur": 11165, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 14060, "tid": 407257, "ts": 1748419605158667, "dur": 6264, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 14060, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 14060, "tid": 8589934592, "ts": 1748419585626146, "dur": 248590, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 14060, "tid": 8589934592, "ts": 1748419585874740, "dur": 9, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 14060, "tid": 8589934592, "ts": 1748419585874750, "dur": 1267, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 14060, "tid": 407257, "ts": 1748419605164937, "dur": 18, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 14060, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 14060, "tid": 4294967296, "ts": 1748419585606413, "dur": 19541106, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 14060, "tid": 4294967296, "ts": 1748419585610824, "dur": 9565, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 14060, "tid": 4294967296, "ts": 1748419605147538, "dur": 4476, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 14060, "tid": 4294967296, "ts": 1748419605149705, "dur": 252, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 14060, "tid": 4294967296, "ts": 1748419605152114, "dur": 20, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 14060, "tid": 407257, "ts": 1748419605164961, "dur": 21, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748419585651783, "dur": 60, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419585651874, "dur": 26748, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419585678634, "dur": 4087, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419585682851, "dur": 78, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748419585682929, "dur": 384, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419585683515, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_0D59EE4A35C68DB6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419585684252, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_C4EA87A8705F2EB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748419585683329, "dur": 19449, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419585702791, "dur": 19429819, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419605132618, "dur": 425, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419605133216, "dur": 65, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419605133316, "dur": 3364, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748419585683467, "dur": 19336, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585702823, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585702893, "dur": 334, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419585703228, "dur": 530, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1748419585702886, "dur": 872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_61E29B66A87F06EA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419585703793, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419585703791, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_C4EA87A8705F2EB3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419585703946, "dur": 247, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419585703945, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_C4356969004537FA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419585704196, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585704254, "dur": 250, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419585704252, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7934D26FA53D75CD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419585704507, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585704574, "dur": 210, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419585704572, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_3C31BD4786B00292.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419585704786, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585704892, "dur": 220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Types.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419585704890, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_B19AEDDBA5CF00C4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419585705164, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\UnityEditor.Android.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419585705163, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_CA6BA6B221BAC638.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419585705238, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585705450, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748419585705525, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585705769, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585705846, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585706410, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748419585707522, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585708278, "dur": 2092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585711123, "dur": 553, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mapbox\\Unity\\LayerProperties\\VectorSubLayerProperties.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419585710370, "dur": 2853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585713224, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585714532, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585715288, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585715863, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585716433, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585717119, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585717714, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585718255, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585719265, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585720096, "dur": 1179, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Editor\\BurstLoader.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419585721276, "dur": 6180, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Editor\\BurstInspectorGUI.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748419585719853, "dur": 7894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585727747, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585728410, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585728949, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585729494, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585730081, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585730629, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585731174, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585731745, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585732312, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419585732510, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419585733137, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585733333, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419585733547, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419585733710, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585733769, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419585734390, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419585733964, "dur": 655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419585734624, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585734854, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419585735259, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419585735467, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419585734714, "dur": 1051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419585735765, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585735949, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585736984, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585737647, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585738235, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419585738405, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419585739086, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585739205, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419585739354, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419585740131, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585740282, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419585740459, "dur": 1807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419585742267, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585742436, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419585742566, "dur": 1065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419585743632, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585743744, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419585743853, "dur": 606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419585744460, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585744545, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748419585744641, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748419585744928, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585744981, "dur": 99722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585844708, "dur": 1942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419585846651, "dur": 566, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585847244, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419585847229, "dur": 1999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419585849229, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585849415, "dur": 1778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419585851194, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585851266, "dur": 2032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419585853299, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585853455, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419585853512, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.Burst.dll"}}, {"pid": 12345, "tid": 1, "ts": 174841**********, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419585855013, "dur": 328, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419585855659, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1748419585853509, "dur": 2633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748419585856143, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585856276, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748419585857054, "dur": 19275563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585683493, "dur": 19325, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585702902, "dur": 342, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419585703246, "dur": 531, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1748419585702889, "dur": 888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_4764A97CA20C124C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419585703778, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585703918, "dur": 230, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419585703917, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_21CF1C58830621C0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419585704193, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419585704191, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_A04142BA6B9BD514.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419585704372, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419585704370, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_481316C4A4502B97.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419585704497, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585704600, "dur": 232, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419585704598, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_9FF7B422260E7EA7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419585704887, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.GradleProject.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419585705009, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1748419585704884, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_10AF8199D68E983D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419585705196, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585705405, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585705515, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419585706288, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748419585706417, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748419585706500, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419585706715, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748419585707042, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585707519, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585708121, "dur": 2002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585710124, "dur": 2173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585712297, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585713362, "dur": 1514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585714877, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585715561, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585716209, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585716766, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585717319, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585718152, "dur": 1096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585719249, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585719979, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585720637, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585721272, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585721960, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585722634, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585723310, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585724049, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585724657, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585725268, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585725997, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585726642, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585727261, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585728033, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585728714, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585729348, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585729956, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585730571, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585731115, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585731841, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585732399, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419585732588, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419585733149, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585733312, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419585734363, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\AssetMenu\\ProjectViewAssetSelection.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419585734617, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\AssetOverlays\\DrawAssetOverlay.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419585735284, "dur": 204, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\Configuration\\ChannelCertificateUiImpl.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419585737414, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\SceneView\\DrawSceneOperations.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419585737730, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.6.0\\Editor\\UI\\Avatar\\GetAvatar.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748419585733489, "dur": 4807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419585738297, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585738400, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585738480, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419585738641, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419585739060, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585739118, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585739223, "dur": 2393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585741617, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419585741797, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748419585742301, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585742431, "dur": 3301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585745733, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748419585745890, "dur": 100817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585847244, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\M2Mqtt.Net.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419585847333, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419585846709, "dur": 2247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Google.Android.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419585848957, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585849094, "dur": 2113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419585851208, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585851563, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419585851360, "dur": 2133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.UI.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419585853494, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585854947, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419585855026, "dur": 638, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Tasks.Dataflow.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419585856221, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748419585853559, "dur": 3251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748419585856811, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748419585857031, "dur": 19275581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585683541, "dur": 19287, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585702851, "dur": 252, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419585703104, "dur": 568, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1748419585702833, "dur": 840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_42F6E26E5392E818.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419585703755, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585703908, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419585703906, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_DE2A9AC6D714602F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419585704101, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419585704099, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_1D361BA92457D64F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419585704310, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419585704308, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_71D251FE16E95B59.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419585704514, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585704577, "dur": 221, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419585704575, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_E11C5609C2206DA8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419585704802, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585704882, "dur": 205, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Gradle.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419585704880, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_B2A85112FF760EF8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419585705298, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419585705566, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585705637, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585705883, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748419585706719, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748419585707510, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5331522906421752218.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748419585707569, "dur": 800, "ph": "X", "name": "File", "args": {"detail": "Assets\\TextMesh Pro\\Examples & Extras\\Scripts\\ChatController.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419585707568, "dur": 2074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585710895, "dur": 572, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mapbox\\Unity\\MeshGeneration\\Data\\KdTree\\NearestNeighbour.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419585709643, "dur": 3769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585713412, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585714507, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585715360, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585716127, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585716786, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585717464, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585718009, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585718545, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585719612, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585720936, "dur": 509, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Plugin\\Changelogs\\Changelog_1_4_6.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419585720159, "dur": 1494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585721653, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585722213, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585722786, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585723335, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585723948, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585724723, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585725286, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585725865, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585726425, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585727418, "dur": 634, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Ports\\InvalidOutput.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419585726959, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585728111, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585728719, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585729985, "dur": 871, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Input\\MouseButton.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419585729271, "dur": 1612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585730884, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585731602, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585732202, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419585732890, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\BurstCompileAttribute.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419585733005, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\BurstExecutionEnvironment.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419585733493, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\CompilerServices\\SPMD.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419585733612, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Editor\\BurstCompileTarget.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419585733835, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Editor\\BurstLoader.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419585733959, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\FunctionPointer.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419585734104, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\Arm\\NEON_AArch64_crypto.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419585734201, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\Arm\\NEON_AArch64_dotprod.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419585734321, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\Arm\\NEON_AArch64_rdma.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419585734410, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\Common.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419585734488, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\f16.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419585734666, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\v64.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419585734865, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\x86\\Common.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419585735045, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\x86\\Popcnt.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419585735174, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\x86\\Sse2.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419585735372, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\Intrinsics\\x86\\Ssse3.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419585735521, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Runtime\\SharedStatic.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748419585732371, "dur": 3271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748419585735643, "dur": 439, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585736102, "dur": 1477, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585737592, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748419585737756, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748419585738223, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585738345, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1748419585738820, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585738895, "dur": 115, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585739732, "dur": 99806, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1748419585846702, "dur": 528, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748419585844695, "dur": 2674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419585847370, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585847488, "dur": 1917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419585849406, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585849491, "dur": 1895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419585851387, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585851507, "dur": 1940, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419585853448, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585853655, "dur": 1954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748419585855610, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585856772, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748419585856974, "dur": 24534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748419585881509, "dur": 19251132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585683591, "dur": 19246, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585702854, "dur": 263, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748419585703118, "dur": 565, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1748419585702842, "dur": 842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_8F12278583549999.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419585703779, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585703868, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585703928, "dur": 225, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748419585703926, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_DB3452A33F8E3985.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419585704199, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748419585704198, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_907457C35FAB199A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419585704408, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748419585704406, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_8EC2840213530F10.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419585704635, "dur": 333, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748419585704633, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_0C6763A992557CB3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419585704972, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585705111, "dur": 200, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748419585705108, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B7E76160FABBEBBF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419585705425, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419585705589, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585705803, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748419585706008, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585706096, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419585706185, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585706512, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419585707293, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16765266549976182072.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748419585707574, "dur": 1869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585710764, "dur": 627, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mapbox\\Unity\\MeshGeneration\\Factories\\TerrainStrategies\\TerrainStrategy.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748419585709443, "dur": 2847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585712291, "dur": 1446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585713737, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585714570, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585715344, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585716013, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585716632, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585717220, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585717875, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585719062, "dur": 723, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@14.0.11\\Editor\\Controls\\VFXVector4Field.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748419585718463, "dur": 1936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585720399, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585720952, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585721496, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585722031, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585722562, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585723099, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585723627, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585724323, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585724944, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585725491, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585726043, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585726597, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585727165, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585727741, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585728323, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585728868, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585730222, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585730866, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585732340, "dur": 574, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Plugins\\OnScreen\\OnScreenStick.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748419585732915, "dur": 845, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Plugins\\OnScreen\\OnScreenControl.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748419585732056, "dur": 2135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585734855, "dur": 695, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748419585734192, "dur": 1433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419585735625, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585735848, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585736547, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585736748, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585737576, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585738237, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419585738421, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419585738919, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585739064, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585739210, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419585739383, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419585739737, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585739877, "dur": 1736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585741620, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419585741801, "dur": 767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419585742568, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585742705, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419585742869, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419585743467, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585743619, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748419585743782, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748419585744235, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585744366, "dur": 100359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585844730, "dur": 1865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748419585846597, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585847244, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748419585847510, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748419585846889, "dur": 2131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748419585849022, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585849125, "dur": 1844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748419585850971, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585851080, "dur": 2099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748419585853180, "dur": 1168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585854558, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748419585855658, "dur": 452, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Pkcs.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748419585854359, "dur": 2432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748419585856791, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585856969, "dur": 18427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585875397, "dur": 6105, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748419585881503, "dur": 19251165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585683634, "dur": 19213, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585702860, "dur": 233, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748419585703095, "dur": 557, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1748419585702852, "dur": 801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_4C09D1522AE95AC4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419585703773, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748419585703772, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_1FF66EA0847C44F7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419585703851, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585703916, "dur": 247, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748419585703915, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_0C40C310871AF0F2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419585704207, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748419585704205, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_FA0AC47C946F2CBA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419585704352, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748419585704350, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_05E2A176D490ADF3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419585704494, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585704572, "dur": 273, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748419585704569, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_7FEBE6A2654794CE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419585704904, "dur": 311, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748419585704902, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_0C0EDCF4D8373D10.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419585705452, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585705700, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_5085858D534FBD2F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419585705831, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748419585706063, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748419585706195, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585706407, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748419585706868, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585707347, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585707457, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8213421625503935943.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748419585708796, "dur": 726, "ph": "X", "name": "File", "args": {"detail": "Assets\\Script\\Data\\TrackerData.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748419585707705, "dur": 2228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585709934, "dur": 2491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585712425, "dur": 1701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585714127, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585715052, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585715509, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585716086, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585716664, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585718002, "dur": 1067, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@14.0.11\\Editor\\Models\\Operators\\Implementations\\OrientedBoxVolume.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748419585717251, "dur": 2010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585719261, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585719902, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585720484, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585721076, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585721721, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585722371, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585723038, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585723688, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585724397, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585725009, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585725616, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585726243, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585726861, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585727438, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585728262, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585729457, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585730289, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585730986, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585731694, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585732461, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419585732659, "dur": 850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419585733510, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585733770, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585733943, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419585734398, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419585734949, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585735547, "dur": 757, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748419585735046, "dur": 1591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419585736638, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585736781, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585737548, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585737603, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419585737751, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419585738096, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585738184, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585738265, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585738433, "dur": 789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585739222, "dur": 2416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585741639, "dur": 778, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585742418, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748419585742601, "dur": 803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748419585743405, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585743532, "dur": 101202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585844738, "dur": 1859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419585846598, "dur": 766, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585847575, "dur": 4216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748419585852461, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Localization.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748419585847372, "dur": 6259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419585853631, "dur": 1117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 174841**********, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.AdaptivePerformance.Simulator.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748419585854947, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748419585854756, "dur": 2039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Simulator.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748419585856795, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585856959, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585857294, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748419585857349, "dur": 19275333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585683674, "dur": 19186, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585702876, "dur": 346, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748419585703224, "dur": 495, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1748419585702866, "dur": 854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_F372D6AB03EB9C8E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419585703764, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_9CE78850A812468C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419585703945, "dur": 212, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748419585703943, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_17FCD0F4DE70BA41.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419585704203, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748419585704201, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6D1F6C0CAAFE17A8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419585704374, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585704448, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748419585704446, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_379C41BE8A980C3D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419585704657, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748419585704656, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_CAD47FCAD374E7F3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419585704932, "dur": 274, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748419585704930, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_C8C0272C381EE5BB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419585705353, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748419585705351, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_4E55F340B0300207.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419585705529, "dur": 251, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_4E55F340B0300207.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419585705917, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748419585706151, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585706248, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585706639, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748419585706966, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748419585707072, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748419585707124, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585707250, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5437769195855483410.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748419585707517, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585708348, "dur": 965, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mapbox\\Unity\\SourceLayers\\VectorLayer.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748419585708348, "dur": 3227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585711861, "dur": 519, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mapbox\\Unity\\DataContainers\\ElevationRequiredOptions.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748419585711575, "dur": 2114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585713690, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585714682, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585715440, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585715992, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585716561, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585717143, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585717780, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585718322, "dur": 1510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585719832, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585720392, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585720952, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585721497, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585722030, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585722562, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585723099, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585723627, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585724498, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585725056, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585725605, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585726156, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585726707, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585727281, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585727850, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585728476, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585729035, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585729620, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585730492, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585731057, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585731612, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585732316, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419585733175, "dur": 386, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Evaluation\\InfiniteRuntimeClip.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748419585732530, "dur": 1186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419585733717, "dur": 497, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585734235, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585734404, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419585734855, "dur": 475, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748419585734594, "dur": 1723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419585736318, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585736458, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585736660, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585737285, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585737654, "dur": 583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585738239, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419585738392, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419585738848, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585738932, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585739212, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419585739339, "dur": 347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419585739687, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585739831, "dur": 1801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585741632, "dur": 786, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585742419, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Profiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748419585742603, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Profiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748419585743086, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585743217, "dur": 102338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585846655, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.HttpListener.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748419585845558, "dur": 1966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419585847525, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585847658, "dur": 1822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419585849481, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585849553, "dur": 2009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419585851563, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585851726, "dur": 750, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419585852479, "dur": 1976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419585854457, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585856367, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748419585854584, "dur": 2222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748419585856807, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585856966, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748419585857436, "dur": 19275218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585683706, "dur": 19167, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585702892, "dur": 352, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419585703246, "dur": 549, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1748419585702879, "dur": 917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_3B5B0D1C936F55AC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419585703913, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419585703911, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_1825307E4DEB52FB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419585704145, "dur": 184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419585704144, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_D9274EFAD2DC078D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419585704332, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585704412, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419585704410, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_209FA7789E0977C1.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419585704650, "dur": 246, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419585704647, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_86C139E5AAFE1760.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419585704942, "dur": 316, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419585704939, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_6B72B6C78467EF61.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419585705310, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419585705510, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748419585705711, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419585705709, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_39330D1DD49ABBAC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419585705782, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585706295, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419585706662, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585706809, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748419585707543, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585707605, "dur": 1735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585709341, "dur": 2578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585712922, "dur": 716, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mapbox\\Examples\\6_ZoomableMap\\Scripts\\SpawnOnMap.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748419585711920, "dur": 1895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585713816, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585714466, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585715111, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585715662, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585716223, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585716781, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585717381, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585718304, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585719323, "dur": 539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585719863, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585720418, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585720973, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585721550, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585722109, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585722667, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585723233, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585723785, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585724755, "dur": 2684, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Graphs\\ShaderDropdown.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748419585724340, "dur": 3189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585728024, "dur": 2240, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Logic\\NumericComparison.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748419585727530, "dur": 2804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585730771, "dur": 1675, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Debugging\\Prefabs\\Scripts\\DebugUIHandlerValue.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748419585730334, "dur": 2544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585732880, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419585733055, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585733131, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.UI.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419585733527, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Mapbox\\Core\\Plugins\\ThirdParty\\Mapbox.IO.Compression\\net35\\Mapbox.IO.Compression.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419585733480, "dur": 968, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.UI.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419585734452, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585734609, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585735316, "dur": 242, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419585734836, "dur": 748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419585735585, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585735729, "dur": 578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585736307, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585736866, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585737549, "dur": 690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585738239, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585738443, "dur": 767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585739210, "dur": 886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585740097, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419585740212, "dur": 1116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419585741329, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585741435, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585741623, "dur": 804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585742427, "dur": 877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585743305, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419585743474, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419585743829, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585743931, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Extension.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748419585744039, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Extension.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748419585744373, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585744477, "dur": 100238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585844721, "dur": 1923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748419585846645, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585847244, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419585847672, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Core.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419585847896, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-fibers-l1-1-0.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419585847024, "dur": 2501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748419585849536, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585849948, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419585849736, "dur": 1895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748419585851632, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585852461, "dur": 891, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.ApiExplorer.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419585851741, "dur": 2708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748419585854450, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 174841**********, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419585855659, "dur": 565, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419585856320, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.AppContext.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748419585854528, "dur": 2691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748419585857220, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748419585857319, "dur": 19275295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585683748, "dur": 19141, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585702936, "dur": 360, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748419585703297, "dur": 514, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1748419585702894, "dur": 918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_91C7B02DD7E937E1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419585703813, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585703882, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748419585703880, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_CF7D4E5FB72BD329.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419585704110, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748419585704108, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_538E8D352E59CE79.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419585704314, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748419585704312, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_B9BA886FA66D1477.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419585704469, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585704553, "dur": 228, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748419585704551, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_0EFC0BA480375449.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419585704785, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585704880, "dur": 243, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748419585704878, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_14536D8C3E47E1E6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419585705127, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585705197, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585705335, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585705666, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748419585705882, "dur": 139, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748419585706155, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748419585706344, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748419585706641, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748419585706776, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585706876, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585706962, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748419585707358, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16503526017140955143.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748419585707424, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585707526, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585708356, "dur": 1422, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mapbox\\Unity\\SourceLayers\\ISubLayerStyle.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748419585708356, "dur": 3823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585712179, "dur": 1613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585713793, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585714380, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585715159, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585715732, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585716299, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585716863, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585717423, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585718041, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585718591, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585720211, "dur": 4560, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@3.4.0\\PostProcessing\\Editor\\Tools\\CubeLutAssetImporter.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748419585719810, "dur": 5143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585724953, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585725513, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585726090, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585726654, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585727403, "dur": 623, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Variables\\Obsolete\\ISceneVariableUnit.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748419585728026, "dur": 1112, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Variables\\Obsolete\\ISavedVariableUnit.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748419585727221, "dur": 2245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585729467, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585730062, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585730848, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585732307, "dur": 605, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\bool3x3.gen.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748419585731655, "dur": 1753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585733410, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419585733634, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419585733884, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419585734390, "dur": 477, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748419585734088, "dur": 1070, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419585735158, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585735291, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585735510, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585736067, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585736601, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585737196, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585737799, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585738235, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748419585738365, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585738423, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748419585739026, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585739132, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585739218, "dur": 2401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585741619, "dur": 815, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585742435, "dur": 102262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585846119, "dur": 591, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748419585844698, "dur": 2504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419585847203, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585847335, "dur": 264, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.Timeline.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748419585847334, "dur": 2259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419585849594, "dur": 605, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585850210, "dur": 1871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Google.Android.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419585852082, "dur": 564, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585853337, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Components.Authorization.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748419585852658, "dur": 2055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748419585854715, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585854966, "dur": 1421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585856400, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585856461, "dur": 563, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419585857029, "dur": 18442067, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748419604299099, "dur": 833403, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748419604299098, "dur": 833406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419585683785, "dur": 19121, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585702925, "dur": 314, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419585703240, "dur": 591, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1748419585702912, "dur": 920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_1D4B133AF68A73C6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748419585703875, "dur": 199, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419585703873, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_0CA0CC3598913E32.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748419585704108, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419585704107, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3BB6C9DD97724E22.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748419585704326, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419585704324, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_64F63BE84EA9CE17.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748419585704509, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419585704507, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_0AAA9749F1127471.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748419585704619, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585704698, "dur": 282, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419585704697, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E194AA87B50D9488.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748419585704984, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585705116, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419585705113, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_5DDE8E3DC54C0DB1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748419585705518, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1748419585705607, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1748419585705732, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.17\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419585705731, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_8CDD503792CD44AF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748419585706143, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585707056, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1748419585707134, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4172707023850728460.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1748419585707187, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585707326, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5712859670010231491.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1748419585707557, "dur": 1339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585708896, "dur": 823, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mapbox\\Unity\\SourceLayers\\IImageryLayer.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748419585708896, "dur": 3095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585711992, "dur": 1559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585713551, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585714286, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585715086, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585716388, "dur": 539, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.adaptiveperformance.google.android@5.1.3\\Runtime\\Provider\\Management\\GoogleAndroidProviderLoader.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748419585715661, "dur": 1415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585717077, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585717861, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585718405, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585719438, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585720007, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585720557, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585721102, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585721678, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585722265, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585722826, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585723431, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585724030, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585724603, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585725157, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585725766, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585726335, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585726921, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585727534, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585728102, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585729139, "dur": 656, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\StaticInvokerBase.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748419585728679, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585729913, "dur": 2495, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\Unity\\GUIStyle_DirectConverter.cs"}}, {"pid": 12345, "tid": 9, "ts": 1748419585729879, "dur": 3358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585733532, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419585733754, "dur": 645, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419585733241, "dur": 1519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748419585734761, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585734930, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1748419585735119, "dur": 835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1748419585735955, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585736158, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585736993, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585737876, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585738245, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585738366, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585738427, "dur": 785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585739212, "dur": 2405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585741618, "dur": 820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585742438, "dur": 102262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585846701, "dur": 638, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.NetTcp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419585844702, "dur": 2900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748419585847603, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585849104, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419585847681, "dur": 2006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748419585849688, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585850323, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419585851276, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Identity.Stores.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419585849944, "dur": 2422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748419585852367, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585852511, "dur": 1867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Simulator.Extension.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748419585854378, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585854947, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419585855658, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Console.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419585856366, "dur": 413, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419585856834, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1748419585854587, "dur": 2710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1748419585857297, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1748419585857401, "dur": 19275207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585683811, "dur": 19110, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585702985, "dur": 708, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1748419585702924, "dur": 770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_C1B7931C83CAC136.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748419585703695, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585703756, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585703753, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_0CB3585AEAFFAB32.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748419585703880, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585703878, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_5D282878EB567DDE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748419585704071, "dur": 223, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585704069, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_C6EAA360B1853425.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748419585704336, "dur": 176, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585704334, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_183139F0BCFA56D6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748419585704562, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_183139F0BCFA56D6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748419585704631, "dur": 248, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585704629, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_847CCDCB962CD0E3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748419585704928, "dur": 251, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585704925, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_0D59EE4A35C68DB6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748419585705318, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585705504, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748419585705741, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Mapbox\\Core\\Plugins\\Mapbox\\vector-tile-cs\\net46\\Mapbox.VectorTile.Geometry.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585706213, "dur": 540, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585706758, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585706819, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585706958, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585707106, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585707170, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585707235, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585707382, "dur": 347, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585707778, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585707961, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585708129, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585708296, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585708419, "dur": 195, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585708616, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585708686, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585708856, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585708998, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585709099, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585709271, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585709323, "dur": 200, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585709525, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585709633, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585709707, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585709936, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585710013, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585710179, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585710312, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585710518, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585710674, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585710746, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585710971, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585711082, "dur": 262, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585711346, "dur": 232, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585711580, "dur": 220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585711802, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585711967, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585712107, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585712265, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585712399, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585712550, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585712608, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585712707, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585712763, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585712991, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585713163, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585713306, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585713478, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585713607, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585713789, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585713968, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585714182, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585714249, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585714398, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585714558, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585714718, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585714898, "dur": 283, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585715183, "dur": 972, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585716262, "dur": 458, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585716773, "dur": 361, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585717136, "dur": 284, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585717422, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585717475, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585717726, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585717819, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585717992, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585718172, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585718367, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585718510, "dur": 1141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585719673, "dur": 464, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585720139, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Drawing.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585720262, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585720462, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Transactions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585720596, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585720820, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGUI\\MaterialAssemblyReference\\RawRenderQueue.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748419585721116, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\BaseEventData.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748419585721258, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\PointerEventData.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748419585721311, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventHandle.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748419585721473, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventInterfaces.cs"}}, {"pid": 12345, "tid": 10, "ts": 1748419585705677, "dur": 18230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748419585723909, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585724073, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585724698, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585725257, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585725828, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585726397, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585726932, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585727483, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585728051, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585728607, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585729156, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585729727, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585730298, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585730844, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585731397, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585731943, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585732793, "dur": 831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748419585733625, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585733795, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1748419585734390, "dur": 472, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585733970, "dur": 1268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1748419585735239, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585735433, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585735541, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585736826, "dur": 1076, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585737902, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585738269, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585738349, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585738475, "dur": 736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585739211, "dur": 2432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585741644, "dur": 796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585742440, "dur": 102253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585846655, "dur": 351, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585844699, "dur": 2504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748419585847204, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585847574, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585847352, "dur": 2036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748419585849394, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585849465, "dur": 1832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748419585851297, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585851372, "dur": 1870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748419585853243, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585853331, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585853784, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 10, "ts": 1748419585853330, "dur": 2190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1748419585855521, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585855623, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585855690, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585855780, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585855863, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585855953, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585856021, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585856106, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585856191, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585856251, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585856348, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585856424, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585856487, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585856553, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585856617, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585856703, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585856799, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585856918, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419585856981, "dur": 14448497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1748419600305480, "dur": 340, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1748419600305479, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1748419600305867, "dur": 2067, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1748419600307937, "dur": 4824763, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585683852, "dur": 19074, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585702941, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748419585702993, "dur": 671, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1748419585702931, "dur": 734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_934EE5D4B70C0964.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748419585703779, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748419585703777, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_2F2FB047803CA83B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748419585703893, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585703964, "dur": 265, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748419585703962, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_5EEE5700CF7F2CDF.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748419585704266, "dur": 224, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748419585704264, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_187779C7F7B3B4CE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748419585704493, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585704571, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748419585704569, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_4D8A662031E48D37.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748419585704779, "dur": 331, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748419585704778, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_A57E8B70117326A4.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748419585705418, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1748419585705960, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1748419585706071, "dur": 179, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1748419585706287, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1748419585706805, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1748419585706990, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585707404, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585707560, "dur": 760, "ph": "X", "name": "File", "args": {"detail": "Assets\\TextMesh Pro\\Examples & Extras\\Scripts\\TMP_TextSelector_B.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748419585707560, "dur": 3042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585710603, "dur": 652, "ph": "X", "name": "File", "args": {"detail": "Assets\\Mapbox\\Unity\\DataContainers\\TileJsonData.cs"}}, {"pid": 12345, "tid": 11, "ts": 1748419585710603, "dur": 2688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585713291, "dur": 1521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585714813, "dur": 576, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-multibyte-l1-1-0.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748419585715389, "dur": 2026, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-math-l1-1-0.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748419585714813, "dur": 3582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585718396, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585719615, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585720237, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585720806, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585721572, "dur": 1468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585723040, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585724289, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585725133, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585725924, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585726703, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585727379, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585728546, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585729194, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585729888, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585730518, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585731091, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585731759, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585732536, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": ****************, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": ****************, "dur": 284, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Mapbox\\Core\\Plugins\\Mapbox\\MapboxAccounts\\net35\\MapboxAccountsUnity.dll"}}, {"pid": 12345, "tid": 11, "ts": ****************, "dur": 654, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\bool3.gen.cs"}}, {"pid": 12345, "tid": 11, "ts": ****************, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\double4x3.gen.cs"}}, {"pid": 12345, "tid": 11, "ts": ****************, "dur": 1807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": ****************, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": ****************, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748419585735543, "dur": 662, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748419585735451, "dur": 1173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748419585736625, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585736769, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585737574, "dur": 685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585738259, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585738438, "dur": 779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585739217, "dur": 2420, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585741638, "dur": 782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585742421, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585742711, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1748419585742898, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1748419585743396, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585743538, "dur": 101204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585844744, "dur": 1906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748419585846651, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585847316, "dur": 266, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Mapbox\\Core\\Plugins\\Mapbox\\vector-tile-cs\\net46\\Mapbox.VectorTile.PbfReader.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748419585846777, "dur": 2137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748419585848915, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585849115, "dur": 1795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748419585850911, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585850979, "dur": 1917, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Unity.AdaptivePerformance.Profiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748419585852897, "dur": 475, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585854559, "dur": 221, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748419585853378, "dur": 2117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748419585855497, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585856188, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419585856778, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1748419585856979, "dur": 12352914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419598209896, "dur": 1832740, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748419598209895, "dur": 1834485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748419600046008, "dur": 293, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1748419600046378, "dur": 242108, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1748419600305475, "dur": 131983, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748419600305474, "dur": 131985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748419600437483, "dur": 1335, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1748419600438822, "dur": 4693802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419585683884, "dur": 19049, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419585702994, "dur": 718, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1748419585702934, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_C5AD924FEB2B8122.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419585703767, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585703765, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_638B50AF35071B35.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419585703958, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585703956, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_28855A63B12059F1.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419585704208, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585704207, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_43D4B55CA123EFA0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419585704444, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585704442, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_5F7426AD1A2DD658.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419585704661, "dur": 228, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585704660, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_9C87E32EB3F2A719.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419585704945, "dur": 491, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585704943, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_140A21A116E2DF85.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419585705504, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419585705929, "dur": 763, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585706694, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585706754, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585706816, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585706991, "dur": 397, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585707397, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585707530, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585707616, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585707692, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585707752, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585707961, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585708124, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585708292, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585708430, "dur": 205, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585708678, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585708867, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585709004, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585709136, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585709262, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585709328, "dur": 186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585709515, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585709637, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585709712, "dur": 230, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585709946, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585710029, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585710186, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585710315, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585710484, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585710674, "dur": 260, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585710972, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585711092, "dur": 244, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585711338, "dur": 230, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585711570, "dur": 206, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585711778, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585711919, "dur": 176, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585712097, "dur": 225, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585712324, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585712394, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585712550, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585712609, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585712739, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585712883, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585712980, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585713159, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585713314, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585713586, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585713774, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585713903, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585714211, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585714382, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585714539, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585714708, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585714917, "dur": 245, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585715164, "dur": 329, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585715495, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585715624, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585715807, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Duplex.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585715959, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585716222, "dur": 666, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585716892, "dur": 418, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585717312, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585717441, "dur": 1628, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585719126, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585719211, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585719346, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Microsoft.CSharp.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585719502, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.DataSetExtensions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585719571, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585719628, "dur": 500, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585720130, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Drawing.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585720252, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.47f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419585705698, "dur": 18398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748419585724097, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419585724240, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419585724407, "dur": 5459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748419585729867, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419585729979, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419585730120, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419585730320, "dur": 1600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748419585731921, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419585732097, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419585733417, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputActionState.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748419585733499, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputActionTrace.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748419585733713, "dur": 219, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Actions\\InputControlScheme.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748419585734446, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\InputControlAttribute.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748419585734719, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\InputProcessor.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748419585735501, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Controls\\QuaternionControl.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748419585736171, "dur": 1393, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@1.7.0\\InputSystem\\Devices\\Commands\\QueryUserIdCommand.cs"}}, {"pid": 12345, "tid": 12, "ts": 1748419585732292, "dur": 5786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748419585738079, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419585738232, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419585738393, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419585738457, "dur": 1479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748419585739937, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419585740093, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419585740245, "dur": 1161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748419585741407, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419585741616, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419585741793, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748419585742271, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419585742417, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419585742607, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748419585743154, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419585743299, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419585743470, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748419585743970, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419585744118, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419585744308, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Google.Android.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748419585744813, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419585744958, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419585745124, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AdaptivePerformance.Simulator.Google.Extension.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748419585745579, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419585745729, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1748419585745927, "dur": 840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748419585748107, "dur": 243, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419585748495, "dur": 12450729, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748419598210197, "dur": 5724097, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 12, "ts": 1748419598209887, "dur": 5724593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1748419603934481, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419603934605, "dur": 1274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748419603936923, "dur": 173, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1748419603937110, "dur": 349598, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1748419604299092, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1748419604299091, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1748419604299261, "dur": 833358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748419605141232, "dur": 4057, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 14060, "tid": 407257, "ts": 1748419605165660, "dur": 2841, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 14060, "tid": 407257, "ts": 1748419605168713, "dur": 2679, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 14060, "tid": 407257, "ts": 1748419605155981, "dur": 16298, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}